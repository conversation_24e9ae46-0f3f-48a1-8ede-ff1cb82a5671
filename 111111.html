<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 数据可逆模糊化处理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 基础样式复用 */
    .btn-purple {
      background-color: var(--primary-color);
      color: white;
      border: none;
    }
    .btn-purple:hover {
      background-color: var(--primary-color);
    }
    .add-btn-container {
      margin-bottom: 16px;
      text-align: right;
    }
    .add-btn {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
    }
    .add-btn i {
      margin-right: 8px;
    }
    .tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
    .tag-success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .tag-danger {
      background-color: #ffebee;
      color: #c62828;
    }
    .tag-warning {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    .tag-info {
      background-color: #e3f2fd;
      color: #1565c0;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
      align-items: center;
      justify-content: center;
    }
    .modal-content {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 12px;
    }
    .progress-bar {
      height: 8px;
      background-color: #eee;
      border-radius: 4px;
      width: 100px;
    }
    .progress {
      height: 100%;
      border-radius: 4px;
    }

    /* 分页样式（按图片要求设计） */
    .pagination-tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin: 16px 0;
    }
    .pagination-tab {
      padding: 10px 20px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: 2px solid transparent;
      margin-right: 4px;
      font-weight: 500;
    }
    .pagination-tab.active {
      border-bottom: 2px solid var(--primary-color);
      color: var(--primary-color);
    }
    .pagination-tab:hover {
      background-color: #f5f5f5;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child " data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child active" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-magic page-title-icon"></i>
      数据可逆模糊化处理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item"><a href="task_scheduling.html" style="text-decoration: none; color: inherit;">任务调度</a></div>
      <div class="breadcrumb-item active">数据可逆模糊化处理</div>
    </div>

    <!-- 分页标签（按图片样式设计） -->
    <div class="pagination-tabs">
      <div class="pagination-tab active" data-tab="strategy">配置模糊化策略</div>
      <div class="pagination-tab" data-tab="task">执行模糊化任务</div>
    </div>

    <!-- 配置模糊化策略内容 -->
    <div class="tab-content active" id="strategy-tab">
      <!-- 新增按钮 -->
     <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div class="card-title">模糊化策略列表</div>
        <button class="btn btn-purple add-btn" onclick="openAddStrategyModal()">
          <i class="fas fa-plus"></i> 新增
        </button>
      </div>
      
      <!-- 模糊化策略列表 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">模糊化策略列表</div>
        </div>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>策略ID</th>
                <th>策略名称</th>
                <th>模糊化算法</th>
                <th>数据源</th>
                <th>版本号</th>
                <th>创建时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>#FS001</td>
                <td>客户数据模糊化策略</td>
                <td>高斯模糊</td>
                <td>MySQL</td>
                <td>V1.0</td>
                <td>2023-07-10 15:30</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" onclick="openEditModal('#FS001', '客户数据模糊化策略', '高斯模糊', 'MySQL', 'V1.0', '2023-07-10 15:30', '启用', 'key123')"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>#FS002</td>
                <td>交易数据模糊化策略</td>
                <td>中值模糊</td>
                <td>Oracle</td>
                <td>V1.1</td>
                <td>2023-07-12 09:15</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" onclick="openEditModal('#FS002', '交易数据模糊化策略', '中值模糊', 'Oracle', 'V1.1', '2023-07-12 09:15', '启用', 'key456')"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>#FS003</td>
                <td>交易数据模糊化策略</td>
                <td>中值模糊</td>
                <td>Oracle</td>
                <td>V1.1</td>
                <td>2023-07-13 09:32</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" onclick="openEditModal('#FS003', '交易数据模糊化策略', '中值模糊', 'Oracle', 'V1.1', '2023-07-13 09:32', '启用', 'key789')"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>#FS004</td>
                <td>客户数据模糊化策略</td>
                <td>高值模糊</td>
                <td>SQL Server</td>
                <td>V1.2</td>
                <td>2023-07-16 08:11</td>
                <td><span class="tag tag-success">停用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" onclick="openEditModal('#FS004', '客户数据模糊化策略', '高值模糊', 'SQL Server', 'V1.2', '2023-07-16 08:11', '停用', 'key101')"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 执行模糊化任务内容 -->
    <div class="tab-content" id="task-tab">
      <!-- 新增按钮 -->
      <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div class="card-title">模糊化任务列表</div>
        <button class="btn btn-purple add-btn" onclick="openAddTaskModal()">
          <i class="fas fa-plus"></i> 新增
        </button>
      </div>
      
      <!-- 模糊化任务列表 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">模糊化任务列表</div>
        </div>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>任务ID</th>
                <th>任务名称</th>
                <th>模糊化算法</th>
                <th>优先级</th>
                <th>状态</th>
                <th>开始时间</th>
                <th>结束时间</th>
                <th>进度</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>#FT001</td>
                <td>客户数据模糊化处理</td>
                <td>高斯模糊</td>
                <td><span class="tag tag-warning">中</span></td>
                <td><span class="tag tag-success">已完成</span></td>
                <td>2023-07-10 16:00</td>
                <td>2023-07-10 16:30</td>
                <td><div class="progress-bar"><div class="progress" style="width: 100%; background-color: var(--success-color);"></div></div>100%</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-stop"></i></button>
                </td>
              </tr>
              <tr>
                <td>#FT002</td>
                <td>交易数据模糊化处理</td>
                <td>中值模糊</td>
                <td><span class="tag tag-danger">高</span></td>
                <td><span class="tag tag-info">处理中</span></td>
                <td>2023-07-12 10:00</td>
                <td>-</td>
                <td><div class="progress-bar"><div class="progress" style="width: 60%; background-color: var(--primary-color);"></div></div>60%</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-stop"></i></button>
                </td>
              </tr>
              <tr>
                <td>#FT003</td>
                <td>交易数据模糊化处理</td>
                <td>高斯模糊</td>
                <td><span class="tag tag-success">低</span></td>
                <td><span class="tag tag-info">处理中</span></td>
                <td>2023-07-12 10:00</td>
                <td>-</td>
                <td><div class="progress-bar"><div class="progress" style="width: 80%; background-color: var(--primary-color);"></div></div>80%</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-stop"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增模糊化策略模态框 -->
  <div id="addStrategyModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新增模糊化策略</h3>
        <button class="btn" onclick="closeAddStrategyModal()" style="color: var(--text-secondary);"><i class="fas fa-times"></i></button>
      </div>
      
      <div class="modal-body">
        <div id="strategyForm">
          <div class="form-group">
            <label>策略ID <span style="color: red;">*</span></label>
            <input type="text" id="newStrategyId" class="form-control">
          </div>
          <div class="form-group">
            <label>策略名称 <span style="color: red;">*</span></label>
            <input type="text" id="newStrategyName" class="form-control">
          </div>
          <div class="form-group">
            <label>模糊化算法 <span style="color: red;">*</span></label>
            <select id="newFuzzificationAlgorithm" class="form-control">
              <option value="gaussian">高斯模糊</option>
              <option value="median">中值模糊</option>
              <option value="high">高值模糊</option>
              <option value="custom">自定义算法</option>
            </select>
          </div>
          <div id="newCustomAlgorithmParams" style="display: none; margin-bottom: 12px;">
            <label>自定义算法参数 (JSON格式) <span style="color: red;">*</span></label>
            <textarea id="newAlgorithmParameters" class="form-control" style="height: 80px;"></textarea>
          </div>
          <div class="form-group">
            <label>数据源 <span style="color: red;">*</span></label>
            <select id="newDataSource" class="form-control">
              <option value="mysql">MySQL</option>
              <option value="oracle">Oracle</option>
              <option value="sqlserver">SQL Server</option>
            </select>
          </div>
          <div class="form-group">
            <label>可逆密钥 <span style="color: red;">*</span></label>
            <input type="text" id="newReversibilityKey" class="form-control">
          </div>
          <div class="form-group">
            <button class="btn btn-purple" onclick="saveNewStrategy()">保存策略</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 修改模糊化策略模态框 -->
  <div id="editModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>修改模糊化策略</h3>
        <button class="btn" onclick="closeEditModal()" style="color: var(--text-secondary);"><i class="fas fa-times"></i></button>
      </div>
      
      <div class="modal-body">
        <div id="editStrategyForm">
          <div class="form-group">
            <label>策略ID <span style="color: red;">*</span></label>
            <input type="text" id="editStrategyId" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>策略名称 <span style="color: red;">*</span></label>
            <input type="text" id="editStrategyName" class="form-control">
          </div>
          <div class="form-group">
            <label>模糊化算法 <span style="color: red;">*</span></label>
            <select id="editFuzzificationAlgorithm" class="form-control">
              <option value="gaussian">高斯模糊</option>
              <option value="median">中值模糊</option>
              <option value="high">高值模糊</option>
              <option value="custom">自定义算法</option>
            </select>
          </div>
          <div id="editCustomAlgorithmParams" style="display: none; margin-bottom: 12px;">
            <label>自定义算法参数 (JSON格式) <span style="color: red;">*</span></label>
            <textarea id="editAlgorithmParameters" class="form-control" style="height: 80px;"></textarea>
          </div>
          <div class="form-group">
            <label>数据源 <span style="color: red;">*</span></label>
            <select id="editDataSource" class="form-control">
              <option value="mysql">MySQL</option>
              <option value="oracle">Oracle</option>
              <option value="sqlserver">SQL Server</option>
            </select>
          </div>
          <div class="form-group">
            <label>可逆密钥 <span style="color: red;">*</span></label>
            <input type="text" id="editReversibilityKey" class="form-control">
          </div>
          <div class="form-group">
            <label>版本号</label>
            <input type="text" id="editVersion" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>创建时间</label>
            <input type="text" id="editCreateTime" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>状态</label>
            <select id="editStatus" class="form-control">
              <option value="启用">启用</option>
              <option value="停用">停用</option>
            </select>
          </div>
          <div class="form-group">
            <button class="btn btn-purple" onclick="saveEditStrategy()">保存修改</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增模糊化任务模态框 -->
  <div id="addTaskModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新增模糊化任务</h3>
        <button class="btn" onclick="closeAddTaskModal()" style="color: var(--text-secondary);"><i class="fas fa-times"></i></button>
      </div>
      
      <form id="fuzzificationTaskForm">
        <div class="form-group">
          <label for="taskId">任务ID <span style="color: red;">*</span></label>
          <input type="text" id="taskId" class="form-control" placeholder="请输入任务ID" required>
        </div>
        
        <div class="form-group">
          <label for="taskName">任务名称 <span style="color: red;">*</span></label>
          <input type="text" id="taskName" class="form-control" placeholder="请输入任务名称" required>
        </div>
        
        <div class="form-group">
          <label for="fuzzificationAlgorithm">模糊化算法 <span style="color: red;">*</span></label>
          <select id="fuzzificationAlgorithm" class="form-control" required>
            <option value="">请选择算法</option>
            <option value="gaussian">高斯模糊</option>
            <option value="median">中值模糊</option>
            <option value="average">均值模糊</option>
            <option value="laplacian">拉普拉斯模糊</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="taskStrategyId">模糊化策略 <span style="color: red;">*</span></label>
          <select id="taskStrategyId" class="form-control" required>
            <option value="">请选择策略</option>
            <option value="S001">基础模糊策略</option>
            <option value="S002">深度模糊策略</option>
            <option value="S003">平衡模糊策略</option>
          </select>
        </div>
        
        <div class="form-group">
          <button type="button" class="btn btn-purple" onclick="queryDataSource()">查询数据源详情</button>
        </div>
        
        <div id="dataSourceInfo" style="display: none; margin: 16px 0; padding: 16px; border: 1px solid #eee; border-radius: 4px;">
          <h4 style="margin-top: 0;">数据源详情</h4>
          <div class="form-group">
            <label>数据源名称</label>
            <input type="text" id="dataSourceName" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>数据源类型</label>
            <input type="text" id="dataSourceType" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>连接状态</label>
            <input type="text" id="connectionStatus" class="form-control" readonly>
          </div>
          <div class="form-group">
            <label>数据量</label>
            <input type="text" id="dataVolume" class="form-control" readonly>
          </div>
        </div>
        
        <div class="form-group">
          <label for="dataRange">数据范围 <span style="color: red;">*</span></label>
          <select id="dataRange" class="form-control" required>
            <option value="all">全部数据</option>
            <option value="recent7">最近7天</option>
            <option value="recent30">最近30天</option>
            <option value="custom">自定义范围</option>
          </select>
        </div>
        
        <div id="customDateRange" style="display: none; margin-bottom: 16px;">
          <div style="display: flex; gap: 16px;">
            <div class="form-group" style="flex: 1;">
              <label for="startDate">开始日期</label>
              <input type="date" id="startDate" class="form-control">
            </div>
            <div class="form-group" style="flex: 1;">
              <label for="endDate">结束日期</label>
              <input type="date" id="endDate" class="form-control">
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="priority">优先级 <span style="color: red;">*</span></label>
          <select id="priority" class="form-control" required>
            <option value="high">高</option>
            <option value="medium" selected>中</option>
            <option value="low">低</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="reversibilityKey">可逆性密钥 <span style="color: red;">*</span></label>
          <input type="text" id="reversibilityKey" class="form-control" placeholder="请输入可逆性密钥" required>
        </div>
        
        <div class="form-actions" style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 24px;">
          <button type="button" class="btn" onclick="closeAddTaskModal()">取消</button>
          <button type="submit" class="btn btn-purple">提交任务</button>
        </div>
      </form>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 分页标签切换功能（按图片样式实现）
    document.querySelectorAll('.pagination-tab').forEach(tab => {
      tab.addEventListener('click', function() {
        // 移除所有标签的活跃状态
        document.querySelectorAll('.pagination-tab').forEach(t => {
          t.classList.remove('active');
        });
        // 隐藏所有内容
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        // 激活当前标签和对应内容
        this.classList.add('active');
        const tabId = this.getAttribute('data-tab');
        document.getElementById(`${tabId}-tab`).classList.add('active');
      });
    });

    // 初始化页面组件
    document.addEventListener('DOMContentLoaded', function() {
      // 策略页面算法选择监听
      document.getElementById('newFuzzificationAlgorithm').addEventListener('change', function() {
        const customParams = document.getElementById('newCustomAlgorithmParams');
        customParams.style.display = this.value === 'custom' ? 'block' : 'none';
      });

      // 编辑页面算法选择监听
      document.getElementById('editFuzzificationAlgorithm').addEventListener('change', function() {
        const customParams = document.getElementById('editCustomAlgorithmParams');
        customParams.style.display = this.value === 'custom' ? 'block' : 'none';
      });

      // 任务页面数据范围监听
      document.getElementById('dataRange').addEventListener('change', function() {
        const customDateRange = document.getElementById('customDateRange');
        customDateRange.style.display = this.value === 'custom' ? 'block' : 'none';
      });

      // 任务表单提交处理
      document.getElementById('fuzzificationTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const taskId = document.getElementById('taskId').value;
        const algorithm = document.getElementById('fuzzificationAlgorithm').value;
        const strategyId = document.getElementById('taskStrategyId').value;
        const dataRange = document.getElementById('dataRange').value;
        const priority = document.getElementById('priority').value;
        const key = document.getElementById('reversibilityKey').value;

        if (!taskId || !algorithm || !strategyId || !dataRange || !priority || !key) {
          alert('请填写所有必填字段');
          return;
        }

        if (dataRange === 'custom' && (!document.getElementById('startDate').value || !document.getElementById('endDate').value)) {
          alert('请选择自定义日期范围');
          return;
        }

        // 模拟提交
        setTimeout(() => {
          alert('任务提交成功');
          closeAddTaskModal();
          simulateTaskProgress();
        }, 800);
      });
    });

    // 策略模态框操作
    function openAddStrategyModal() {
      document.getElementById('addStrategyModal').style.display = 'flex';
    }

    function closeAddStrategyModal() {
      document.getElementById('addStrategyModal').style.display = 'none';
      document.getElementById('newCustomAlgorithmParams').style.display = 'none';
      document.getElementById('strategyForm').reset();
    }

    function saveNewStrategy() {
      const strategyId = document.getElementById('newStrategyId').value;
      const name = document.getElementById('newStrategyName').value;
      const algorithm = document.getElementById('newFuzzificationAlgorithm').value;
      const dataSource = document.getElementById('newDataSource').value;
      const key = document.getElementById('newReversibilityKey').value;

      if (!strategyId || !name || !algorithm || !dataSource || !key) {
        alert('请填写所有必填字段');
        return;
      }

      if (algorithm === 'custom' && !document.getElementById('newAlgorithmParameters').value) {
        alert('请输入自定义算法参数');
        return;
      }

      // 模拟保存
      setTimeout(() => {
        alert('策略保存成功');
        closeAddStrategyModal();
        location.reload();
      }, 500);
    }

    // 编辑策略操作
    function openEditModal(id, name, algorithm, dataSource, version, createTime, status, key) {
      document.getElementById('editStrategyId').value = id;
      document.getElementById('editStrategyName').value = name;
      document.getElementById('editVersion').value = version;
      document.getElementById('editCreateTime').value = createTime;
      document.getElementById('editStatus').value = status;
      document.getElementById('editReversibilityKey').value = key;

      // 设置算法
      let algoValue = {
        '高斯模糊': 'gaussian',
        '中值模糊': 'median',
        '高值模糊': 'high',
        '自定义算法': 'custom'
      }[algorithm] || 'gaussian';
      document.getElementById('editFuzzificationAlgorithm').value = algoValue;
      document.getElementById('editCustomAlgorithmParams').style.display = algoValue === 'custom' ? 'block' : 'none';

      // 设置数据源
      let dataSourceValue = {
        'MySQL': 'mysql',
        'Oracle': 'oracle',
        'SQL Server': 'sqlserver'
      }[dataSource] || 'mysql';
      document.getElementById('editDataSource').value = dataSourceValue;

      document.getElementById('editModal').style.display = 'flex';
    }

    function closeEditModal() {
      document.getElementById('editModal').style.display = 'none';
    }

    function saveEditStrategy() {
      // 验证逻辑同新增
      setTimeout(() => {
        alert('策略修改成功');
        closeEditModal();
        location.reload();
      }, 500);
    }

    // 任务模态框操作
    function openAddTaskModal() {
      document.getElementById('addTaskModal').style.display = 'flex';
    }

    function closeAddTaskModal() {
      document.getElementById('addTaskModal').style.display = 'none';
      document.getElementById('fuzzificationTaskForm').reset();
      document.getElementById('dataSourceInfo').style.display = 'none';
      document.getElementById('customDateRange').style.display = 'none';
    }

    // 查询数据源详情
    function queryDataSource() {
      const algorithm = document.getElementById('fuzzificationAlgorithm').value;
      if (!algorithm) {
        alert('请选择模糊化算法');
        return;
      }

      // 模拟查询
      setTimeout(() => {
        const data = {
          'gaussian': { name: '客户信息库', type: 'MySQL', status: '已连接', volume: '150,000条' },
          'median': { name: '交易记录库', type: 'Oracle', status: '已连接', volume: '320,000条' },
          'average': { name: '产品评价库', type: 'MongoDB', status: '已连接', volume: '85,000条' },
          'laplacian': { name: '用户行为库', type: 'Redis', status: '已连接', volume: '520,000条' }
        }[algorithm];

        document.getElementById('dataSourceName').value = data.name;
        document.getElementById('dataSourceType').value = data.type;
        document.getElementById('connectionStatus').value = data.status;
        document.getElementById('dataVolume').value = data.volume;
        document.getElementById('dataSourceInfo').style.display = 'block';
        alert('数据源查询成功');
      }, 500);
    }

    // 模拟任务进度
    function simulateTaskProgress() {
      const tbody = document.querySelector('#task-tab table tbody');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td>#FT${Math.floor(1000 + Math.random() * 9000)}</td>
        <td>${document.getElementById('taskName').value}</td>
        <td>${document.getElementById('fuzzificationAlgorithm').options[document.getElementById('fuzzificationAlgorithm').selectedIndex].text}</td>
        <td><span class="tag ${document.getElementById('priority').value === 'high' ? 'tag-danger' : document.getElementById('priority').value === 'medium' ? 'tag-warning' : 'tag-success'}">${document.getElementById('priority').options[document.getElementById('priority').selectedIndex].text}</span></td>
        <td><span class="tag tag-info">处理中</span></td>
        <td>${new Date().toLocaleString()}</td>
        <td>-</td>
        <td><div class="progress-bar"><div class="progress" style="width: 0%; background-color: var(--primary-color);"></div></div>0%</td>
        <td>
          <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
          <button class="btn" style="color: var(--danger-color);"><i class="fas fa-stop"></i></button>
        </td>
      `;
      tbody.insertBefore(newRow, tbody.firstChild);

      // 模拟进度更新
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 10) + 1;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          newRow.querySelector('.tag-info').className = 'tag tag-success';
          newRow.querySelector('.tag-success').textContent = '已完成';
          newRow.querySelector('.progress').style.backgroundColor = 'var(--success-color)';
          newRow.querySelector('td:nth-child(7)').textContent = new Date().toLocaleString();
        }
        newRow.querySelector('.progress').style.width = `${progress}%`;
        newRow.querySelector('.progress-bar').innerHTML = `<div class="progress" style="width: ${progress}%; background-color: var(--primary-color);"></div>${progress}%`;
      }, 500);
    }
  </script>
</body>
</html>