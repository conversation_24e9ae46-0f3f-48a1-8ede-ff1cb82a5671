<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 告警处理管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
   <style>
    .tab-content {
      display: none;
      padding: 20px 0;
    }
    .tab-content.active {
      display: block;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
    }
    .tab.active {
      border-bottom-color: var(--primary-color);
      color: var(--primary-color);
      font-weight: 500;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
   <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child " data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child " data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child  active" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-tools page-title-icon"></i>
      告警处理管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item"><a href="task_scheduling.html" style="text-decoration: none; color: inherit;">任务调度</a></div>
      <div class="breadcrumb-item active">告警处理</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <div class="tab active" data-tab-target="processing-flow">处理流程配置</div>
      <div class="tab" data-tab-target="feedback-mechanism">处理反馈机制</div>
      <div class="tab" data-tab-target="effect-evaluation">处理效果评估</div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content active" id="processing-flow">
      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">处理流程配置列表</div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <button class="btn btn-primary" data-modal-target="flowModal" style="height: 39px;margin-top: -19px;">
                <i class="fas fa-plus"></i> 新增
              </button>
              <div class="search-box" style="width: 250px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索流程...">
              </div>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>流程编号</th>
                  <th>流程名称</th>
                  <th>版本</th>
                  <th>告警级别</th>
                  <th>处理时限</th>
                  <th>节点负责人</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PF001</td>
                  <td>严重告警处理流程</td>
                  <td>1.0</td>
                  <td><span class="tag tag-danger">严重</span></td>
                  <td>30分钟</td>
                  <td>张三</td>
                  <td>2023-05-20 10:00:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>PF002</td>
                  <td>重要告警处理流程</td>
                  <td>1.0</td>
                  <td><span class="tag tag-warning">重要</span></td>
                  <td>60分钟</td>
                  <td>李四</td>
                  <td>2023-05-20 11:30:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>PF003</td>
                  <td>次要告警处理流程</td>
                  <td>1.0</td>
                  <td><span class="tag tag-info">次要</span></td>
                  <td>120分钟</td>
                  <td>王五</td>
                  <td>2023-05-20 14:15:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="feedback-mechanism">
      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">处理反馈机制列表</div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <button class="btn btn-primary" data-modal-target="feedbackModal" style="height: 39px;margin-top: -19px;">
                <i class="fas fa-plus"></i> 新增
              </button>
              <div class="search-box" style="width: 250px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索反馈机制...">
              </div>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>反馈编号</th>
                  <th>反馈名称</th>
                  <th>反馈方式</th>
                  <th>反馈周期</th>
                  <th>适用级别</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>FM001</td>
                  <td>严重告警即时反馈</td>
                  <td>短信+邮件</td>
                  <td>即时</td>
                  <td>严重</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>2023-05-21 09:30:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>FM002</td>
                  <td>重要告警定时反馈</td>
                  <td>邮件</td>
                  <td>30分钟</td>
                  <td>重要</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>2023-05-21 10:15:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>FM003</td>
                  <td>一般告警日报</td>
                  <td>系统消息</td>
                  <td>1440分钟</td>
                  <td>次要+警告</td>
                  <td><span class="tag tag-warning">禁用</span></td>
                  <td>2023-05-21 15:45:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="effect-evaluation">
      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">处理效果评估列表</div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <button class="btn btn-primary" data-modal-target="evaluationModal" style="height: 39px;margin-top: -19px;">
                <i class="fas fa-plus"></i> 新增
              </button>
              <div class="search-box" style="width: 250px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索评估...">
              </div>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>评估编号</th>
                  <th>评估名称</th>
                  <th>关联告警ID</th>
                  <th>评估标准</th>
                  <th>评估结果</th>
                  <th>评估人</th>
                  <th>评估时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>EE001</td>
                  <td>服务器告警处理评估</td>
                  <td>A1001</td>
                  <td>处理时效标准</td>
                  <td><span class="tag tag-success">优秀</span></td>
                  <td>管理员</td>
                  <td>2023-05-22 09:45:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>EE002</td>
                  <td>数据库告警处理评估</td>
                  <td>A1002</td>
                  <td>处理质量标准</td>
                  <td><span class="tag tag-warning">良好</span></td>
                  <td>管理员</td>
                  <td>2023-05-22 11:20:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>EE003</td>
                  <td>网络告警处理评估</td>
                  <td>A1003</td>
                  <td>用户满意度标准</td>
                  <td><span class="tag tag-info">一般</span></td>
                  <td>管理员</td>
                  <td>2023-05-22 14:30:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 处理流程配置模态框 -->
  <div class="modal" id="flowModal">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新增处理流程配置</h5>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="flowModalForm">
            <div class="form-group">
              <label for="modalFlowCode">流程编号 <span style="color: red;">*</span></label>
              <input type="text" id="modalFlowCode" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalFlowName">流程名称 <span style="color: red;">*</span></label>
              <input type="text" id="modalFlowName" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalFlowVersion">版本 <span style="color: red;">*</span></label>
              <input type="text" id="modalFlowVersion" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalFlowLevel">告警级别 <span style="color: red;">*</span></label>
              <select id="modalFlowLevel" class="form-control" required>
                <option value="">请选择告警级别</option>
                <option value="critical">严重</option>
                <option value="major">重要</option>
                <option value="minor">次要</option>
                <option value="warning">警告</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" onclick="document.getElementById('flowModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="saveModalForm('flowModalForm', 'flowModal')">保存</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 处理反馈机制模态框 -->
  <div class="modal" id="feedbackModal">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新增处理反馈机制</h5>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="feedbackModalForm">
            <div class="form-group">
              <label for="modalFeedbackCode">反馈编号 <span style="color: red;">*</span></label>
              <input type="text" id="modalFeedbackCode" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalFeedbackName">反馈名称 <span style="color: red;">*</span></label>
              <input type="text" id="modalFeedbackName" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalFeedbackMethod">反馈方式 <span style="color: red;">*</span></label>
              <select id="modalFeedbackMethod" class="form-control" required>
                <option value="">请选择反馈方式</option>
                <option value="email">邮件</option>
                <option value="sms">短信</option>
                <option value="system">系统消息</option>
                <option value="wechat">微信</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" onclick="document.getElementById('feedbackModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="saveModalForm('feedbackModalForm', 'feedbackModal')">保存</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 处理效果评估模态框 -->
  <div class="modal" id="evaluationModal">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新增处理效果评估</h5>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="evaluationModalForm">
            <div class="form-group">
              <label for="modalEvaluationCode">评估编号 <span style="color: red;">*</span></label>
              <input type="text" id="modalEvaluationCode" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalEvaluationName">评估名称 <span style="color: red;">*</span></label>
              <input type="text" id="modalEvaluationName" class="form-control" required>
            </div>
            <div class="form-group">
              <label for="modalEvaluationStandard">评估标准 <span style="color: red;">*</span></label>
              <select id="modalEvaluationStandard" class="form-control" required>
                <option value="">请选择评估标准</option>
                <option value="timeliness">处理时效</option>
                <option value="quality">处理质量</option>
                <option value="satisfaction">用户满意度</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" onclick="document.getElementById('evaluationModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="saveModalForm('evaluationModalForm', 'evaluationModal')">保存</button>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 标签页切换功能
    document.addEventListener('DOMContentLoaded', function() {
      const tabBtns = document.querySelectorAll('.tab');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          const target = btn.getAttribute('data-tab-target');

          // 切换按钮状态
          tabBtns.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // 切换内容显示
          tabContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === target) {
              content.classList.add('active');
            }
          });
        });
      });

      // 表单提交处理
      document.getElementById('addFlowForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // 模拟表单提交和保存
        alert('处理流程配置已保存!');
        this.reset();
      });

      document.getElementById('addFeedbackForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // 模拟表单提交和保存
        alert('处理反馈机制已保存!');
        this.reset();
      });

      document.getElementById('addEvaluationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // 模拟表单提交和保存
        alert('处理效果评估已保存!');
        this.reset();
      });
    });

    // 重置表单函数
    function resetForm(formId) {
      document.getElementById(formId).reset();
    }

    // 保存模态框表单
    function saveModalForm(formId, modalId) {
      const form = document.getElementById(formId);
      if (form.checkValidity()) {
        // 模拟表单提交和保存
        alert('数据已保存!');
        form.reset();
        document.getElementById(modalId).classList.remove('show');
      } else {
        // 触发表单验证提示
        form.reportValidity();
      }
    }
  </script>
</body>
</html>