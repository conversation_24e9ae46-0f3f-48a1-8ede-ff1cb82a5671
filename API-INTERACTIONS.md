# DevOps平台 API交互功能说明

## 概述

本项目为DevOps平台的所有页面按钮添加了真实的API交互功能。每个按钮点击都会发送HTTP请求到 `http://127.0.0.1/api/v1`，您可以在浏览器开发者工具中查看所有网络请求。

## 新增文件

### 1. `js/api-client.js`
通用API客户端工具，提供以下功能：
- 统一的HTTP请求方法（GET、POST、PUT、DELETE）
- 自动loading状态管理
- 错误处理和用户反馈
- 模拟数据响应（当后端不存在时）
- 美观的通知消息显示

### 2. `api-demo.html`
API交互演示页面，展示所有功能的测试按钮和实时API请求日志。

## 页面功能详情

### 1. DevOps Dashboard (`devops_dashboard.html`)

#### 新增API交互：
- **通知下拉菜单**: 点击时调用 `GET /api/v1/notifications` 获取最新通知
- **标记通知已读**: 点击通知项调用 `POST /api/v1/notifications/mark-read`
- **用户菜单操作**:
  - 个人中心: `GET /api/v1/user/profile`
  - 系统设置: 跳转到设置页面
  - 退出登录: `POST /api/v1/auth/logout`
- **图表操作**:
  - 导出部署数据: `GET /api/v1/dashboard/deployment-trend`
  - 刷新部署图表: `GET /api/v1/dashboard/deployment-trend`
  - 导出资源数据: `GET /api/v1/dashboard/resource-usage`
  - 刷新资源图表: `GET /api/v1/dashboard/resource-usage`
- **查看全部流水线**: `GET /api/v1/pipelines` 然后跳转

### 2. Deployment Management (`deployment_management.html`)

#### 新增API交互：
- **部署新应用**: `POST /api/v1/deployments/applications`
- **环境切换**: `GET /api/v1/deployments/applications?env={env}`
- **应用扩缩容**: `PUT /api/v1/deployments/applications/{id}/scale`
- **重启应用**: `POST /api/v1/deployments/applications/{id}/restart`
- **停止应用**: `POST /api/v1/deployments/applications/{id}/stop`

#### 功能特点：
- 所有操作都有loading状态
- 操作成功后自动刷新应用列表
- 扩缩容操作通过模态框确认

### 3. Monitoring Center (`monitoring_center.html`)

#### 新增API交互：
- **时间范围切换**: `GET /api/v1/monitoring/metrics?timeRange={range}`
- **刷新监控数据**: `GET /api/v1/monitoring/metrics`
- **刷新告警**: `GET /api/v1/monitoring/alerts`
- **解决告警**: `POST /api/v1/monitoring/alerts/{id}/resolve`
- **静音告警**: `POST /api/v1/monitoring/alerts/{id}/mute`

#### 功能特点：
- 时间范围切换自动更新所有图表
- 告警操作后自动更新告警列表和计数
- 支持实时监控数据刷新

### 4. Pipeline Management (`pipeline_management.html`)

#### 新增API交互：
- **新建流水线**: `POST /api/v1/pipelines`
- **运行流水线**: `POST /api/v1/pipelines/{id}/run`
- **停止流水线**: `POST /api/v1/pipelines/{id}/stop`
- **搜索流水线**: `GET /api/v1/pipelines?search={term}&status={status}`

#### 功能特点：
- 支持服务端搜索和过滤
- 流水线状态实时更新
- 操作按钮根据流水线状态动态显示

### 5. Service Topology (`service_topology.html`)

#### 新增API交互：
- **刷新拓扑**: `GET /api/v1/topology/services`
- **切换布局**: `POST /api/v1/user/preferences` (保存用户偏好)
- **视图模式切换**: `GET /api/v1/topology/dependencies?viewMode={mode}`
- **服务过滤**: `GET /api/v1/topology/services?status={filter}`
- **查看服务日志**: `GET /api/v1/services/{id}/logs`
- **搜索服务**: `GET /api/v1/topology/services?search={term}`

#### 功能特点：
- 支持多种拓扑布局（层次、圆形）
- 服务日志在新窗口中显示
- 实时服务状态更新

## API端点列表

### 通用接口
- `GET /api/v1/notifications` - 获取通知列表
- `POST /api/v1/notifications/mark-read` - 标记通知已读
- `GET /api/v1/user/profile` - 获取用户信息
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/user/preferences` - 保存用户偏好

### Dashboard接口
- `GET /api/v1/dashboard/stats` - 获取总览统计
- `GET /api/v1/dashboard/deployment-trend` - 获取部署趋势
- `GET /api/v1/dashboard/resource-usage` - 获取资源使用情况

### 部署管理接口
- `GET /api/v1/deployments/environments` - 获取环境列表
- `GET /api/v1/deployments/applications` - 获取应用列表
- `POST /api/v1/deployments/applications` - 创建新部署
- `PUT /api/v1/deployments/applications/{id}/scale` - 应用扩缩容
- `POST /api/v1/deployments/applications/{id}/restart` - 重启应用
- `POST /api/v1/deployments/applications/{id}/stop` - 停止应用

### 监控接口
- `GET /api/v1/monitoring/metrics` - 获取监控指标
- `GET /api/v1/monitoring/alerts` - 获取告警列表
- `POST /api/v1/monitoring/alerts/{id}/resolve` - 解决告警
- `POST /api/v1/monitoring/alerts/{id}/mute` - 静音告警

### 流水线接口
- `GET /api/v1/pipelines` - 获取流水线列表
- `POST /api/v1/pipelines` - 创建新流水线
- `POST /api/v1/pipelines/{id}/run` - 运行流水线
- `POST /api/v1/pipelines/{id}/stop` - 停止流水线

### 服务拓扑接口
- `GET /api/v1/topology/services` - 获取服务列表
- `GET /api/v1/topology/dependencies` - 获取服务依赖
- `GET /api/v1/services/{id}/logs` - 获取服务日志

## 使用方法

### 1. 查看API交互演示
打开 `api-demo.html` 页面，点击各种测试按钮查看API交互效果。

### 2. 查看网络请求
1. 按F12打开浏览器开发者工具
2. 切换到Network标签页
3. 点击任意页面按钮
4. 观察发送的HTTP请求和响应

### 3. 自定义API响应
在 `js/api-client.js` 的 `getMockResponse` 方法中修改模拟数据，可以自定义API响应内容。

## 技术特点

### 1. 真实HTTP请求
- 所有按钮都发送真实的HTTP请求
- 可在浏览器Network面板中查看
- 支持完整的请求/响应周期

### 2. 优雅的用户体验
- 自动loading状态管理
- 美观的成功/失败通知
- 响应式的按钮状态

### 3. 错误处理
- 网络错误自动降级到模拟数据
- 友好的错误提示信息
- 不影响页面正常使用

### 4. 可扩展性
- 统一的API客户端设计
- 易于添加新的API端点
- 支持自定义请求头和参数

## 注意事项

1. **后端服务**: 当前没有真实的后端服务，API请求会失败并返回模拟数据
2. **CORS**: 如果连接真实后端，可能需要配置CORS策略
3. **认证**: 当前使用模拟的Bearer token，实际使用时需要真实的认证机制
4. **数据持久化**: 模拟数据不会持久化，页面刷新后会重置

## 开发建议

1. **连接真实后端**: 修改 `js/api-client.js` 中的 `baseURL` 指向真实的API服务
2. **添加认证**: 在请求头中添加真实的认证token
3. **错误处理**: 根据实际API响应格式调整错误处理逻辑
4. **数据格式**: 根据后端API调整请求和响应的数据格式
