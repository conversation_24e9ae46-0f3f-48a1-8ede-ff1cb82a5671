# DevOps平台 JSON API接口文档 (第二部分)

## 3. 容器部署管理接口

### 3.1 获取环境列表
```nginx
location /api/deployment_environments {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "environmentId": "prod",
                "environmentName": "生产环境",
                "status": "active",
                "applicationCount": 15,
                "resourceUsage": {
                    "cpu": 75,
                    "memory": 68,
                    "storage": 45
                }
            },
            {
                "environmentId": "staging",
                "environmentName": "预发布环境",
                "status": "active",
                "applicationCount": 8,
                "resourceUsage": {
                    "cpu": 45,
                    "memory": 52,
                    "storage": 30
                }
            },
            {
                "environmentId": "dev",
                "environmentName": "开发环境",
                "status": "active",
                "applicationCount": 12,
                "resourceUsage": {
                    "cpu": 35,
                    "memory": 40,
                    "storage": 25
                }
            }
        ]
    }';
}
```

### 3.2 获取应用列表
```nginx
location /api/deployment_applications {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "applicationId": "user-service",
                "applicationName": "用户服务",
                "environment": "prod",
                "status": "running",
                "version": "v1.2.3",
                "image": "registry.company.com/user-service:v1.2.3",
                "instances": {
                    "desired": 3,
                    "current": 3,
                    "ready": 3
                },
                "health": {
                    "status": "healthy",
                    "total": 3,
                    "ready": 3,
                    "unhealthy": 0
                },
                "resources": {
                    "cpuUsage": 45,
                    "memoryUsage": 512,
                    "cpuLimit": "1000m",
                    "memoryLimit": "1Gi"
                },
                "createdAt": "2024-01-10T08:00:00Z",
                "updatedAt": "2024-01-15T10:00:00Z"
            },
            {
                "applicationId": "order-service",
                "applicationName": "订单服务",
                "environment": "prod",
                "status": "running",
                "version": "v2.1.0",
                "image": "registry.company.com/order-service:v2.1.0",
                "instances": {
                    "desired": 5,
                    "current": 5,
                    "ready": 4
                },
                "health": {
                    "status": "warning",
                    "total": 5,
                    "ready": 4,
                    "unhealthy": 1
                },
                "resources": {
                    "cpuUsage": 78,
                    "memoryUsage": 1024,
                    "cpuLimit": "2000m",
                    "memoryLimit": "2Gi"
                },
                "createdAt": "2024-01-08T14:30:00Z",
                "updatedAt": "2024-01-15T09:45:00Z"
            }
        ]
    }';
}
```

### 3.3 创建新部署
```nginx
location /api/create_deployment {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "应用部署创建成功",
        "data": {
            "deploymentId": "deploy_001",
            "applicationId": "new-service",
            "applicationName": "新服务",
            "environment": "prod",
            "status": "deploying",
            "version": "v1.0.0",
            "image": "registry.company.com/new-service:v1.0.0",
            "instances": {
                "desired": 2,
                "current": 0,
                "ready": 0
            },
            "estimatedCompletionTime": "2024-01-15T10:35:00Z",
            "createdAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

### 3.4 应用扩缩容
```nginx
location /api/scale_application {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "应用扩缩容操作成功",
        "data": {
            "applicationId": "user-service",
            "applicationName": "用户服务",
            "environment": "prod",
            "scalingOperation": {
                "previousInstances": 3,
                "targetInstances": 5,
                "status": "scaling",
                "startedAt": "2024-01-15T10:30:25Z",
                "estimatedCompletionTime": "2024-01-15T10:32:00Z"
            }
        }
    }';
}
```

### 3.5 重启应用
```nginx
location /api/restart_application {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "应用重启操作成功",
        "data": {
            "applicationId": "order-service",
            "applicationName": "订单服务",
            "environment": "prod",
            "restartOperation": {
                "status": "restarting",
                "strategy": "rolling",
                "startedAt": "2024-01-15T10:30:25Z",
                "estimatedCompletionTime": "2024-01-15T10:33:00Z",
                "progress": {
                    "totalInstances": 5,
                    "restartedInstances": 0,
                    "currentPhase": "preparing"
                }
            }
        }
    }';
}
```

### 3.6 停止应用
```nginx
location /api/stop_application {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "应用停止操作成功",
        "data": {
            "applicationId": "payment-service",
            "applicationName": "支付服务",
            "environment": "prod",
            "stopOperation": {
                "status": "stopping",
                "startedAt": "2024-01-15T10:30:25Z",
                "estimatedCompletionTime": "2024-01-15T10:31:30Z",
                "progress": {
                    "totalInstances": 2,
                    "stoppedInstances": 0,
                    "currentPhase": "draining_connections"
                }
            }
        }
    }';
}
```

## 4. 监控中心接口

### 4.1 获取监控指标
```nginx
location /api/monitoring_metrics {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "timeRange": "1h",
            "metrics": {
                "cpu": {
                    "metricName": "CPU使用率",
                    "unit": "%",
                    "currentValue": 65,
                    "dataPoints": [
                        {"timestamp": "2024-01-15T09:30:00Z", "value": 45},
                        {"timestamp": "2024-01-15T09:45:00Z", "value": 52},
                        {"timestamp": "2024-01-15T10:00:00Z", "value": 58},
                        {"timestamp": "2024-01-15T10:15:00Z", "value": 62},
                        {"timestamp": "2024-01-15T10:30:00Z", "value": 65}
                    ]
                },
                "memory": {
                    "metricName": "内存使用率",
                    "unit": "%",
                    "currentValue": 78,
                    "dataPoints": [
                        {"timestamp": "2024-01-15T09:30:00Z", "value": 70},
                        {"timestamp": "2024-01-15T09:45:00Z", "value": 72},
                        {"timestamp": "2024-01-15T10:00:00Z", "value": 75},
                        {"timestamp": "2024-01-15T10:15:00Z", "value": 76},
                        {"timestamp": "2024-01-15T10:30:00Z", "value": 78}
                    ]
                },
                "network": {
                    "metricName": "网络流量",
                    "unit": "Mbps",
                    "currentValue": 125,
                    "dataPoints": [
                        {"timestamp": "2024-01-15T09:30:00Z", "value": 98},
                        {"timestamp": "2024-01-15T09:45:00Z", "value": 105},
                        {"timestamp": "2024-01-15T10:00:00Z", "value": 115},
                        {"timestamp": "2024-01-15T10:15:00Z", "value": 120},
                        {"timestamp": "2024-01-15T10:30:00Z", "value": 125}
                    ]
                },
                "disk": {
                    "metricName": "磁盘I/O",
                    "unit": "IOPS",
                    "currentValue": 450,
                    "dataPoints": [
                        {"timestamp": "2024-01-15T09:30:00Z", "value": 380},
                        {"timestamp": "2024-01-15T09:45:00Z", "value": 395},
                        {"timestamp": "2024-01-15T10:00:00Z", "value": 420},
                        {"timestamp": "2024-01-15T10:15:00Z", "value": 435},
                        {"timestamp": "2024-01-15T10:30:00Z", "value": 450}
                    ]
                }
            }
        }
    }';
}
```

### 4.2 获取告警列表
```nginx
location /api/monitoring_alerts {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "alertId": "alert_001",
                "alertName": "CPU使用率过高",
                "level": "critical",
                "status": "active",
                "message": "user-service 节点 CPU 使用率达到 95%",
                "service": "user-service",
                "node": "node-01",
                "threshold": 90,
                "currentValue": 95,
                "createdAt": "2024-01-15T10:28:00Z",
                "relativeTime": "2分钟前",
                "isMuted": false
            },
            {
                "alertId": "alert_002",
                "alertName": "内存使用率警告",
                "level": "warning",
                "status": "active",
                "message": "order-service 内存使用率达到 85%",
                "service": "order-service",
                "node": "node-02",
                "threshold": 80,
                "currentValue": 85,
                "createdAt": "2024-01-15T10:25:00Z",
                "relativeTime": "5分钟前",
                "isMuted": false
            }
        ],
        "summary": {
            "totalAlerts": 2,
            "activeAlerts": 2,
            "criticalAlerts": 1,
            "warningAlerts": 1
        }
    }';
}
```

### 4.3 解决告警
```nginx
location /api/resolve_alert {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "告警已成功解决",
        "data": {
            "alertId": "alert_001",
            "alertName": "CPU使用率过高",
            "previousStatus": "active",
            "currentStatus": "resolved",
            "resolvedBy": "admin",
            "resolvedAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

### 4.4 静音告警
```nginx
location /api/mute_alert {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "告警已静音",
        "data": {
            "alertId": "alert_002",
            "alertName": "内存使用率警告",
            "isMuted": true,
            "mutedBy": "admin",
            "mutedAt": "2024-01-15T10:30:25Z",
            "muteDuration": "1h"
        }
    }';
}
```

## 5. CI/CD流水线管理接口

### 5.1 获取流水线列表
```nginx
location /api/pipelines {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "pipelineId": "pipeline_001",
                "pipelineName": "user-service-pipeline",
                "description": "用户服务CI/CD流水线",
                "status": "success",
                "branch": "main",
                "author": "张三",
                "repository": "https://github.com/company/user-service.git",
                "lastRunTime": "2024-01-15T09:45:00Z",
                "duration": "6m 30s",
                "buildNumber": 125
            },
            {
                "pipelineId": "pipeline_002",
                "pipelineName": "order-service-pipeline",
                "description": "订单服务CI/CD流水线",
                "status": "running",
                "branch": "develop",
                "author": "李四",
                "repository": "https://github.com/company/order-service.git",
                "lastRunTime": "2024-01-15T10:20:00Z",
                "duration": "4m 12s",
                "buildNumber": 89
            }
        ]
    }';
}
```
