# DevOps平台 JSON API接口文档 (第三部分)

## 5. CI/CD流水线管理接口 (续)

### 5.2 创建新流水线
```nginx
location /api/create_pipeline {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "流水线创建成功",
        "data": {
            "pipelineId": "pipeline_003",
            "pipelineName": "new-service-pipeline",
            "description": "新服务CI/CD流水线",
            "status": "created",
            "branch": "main",
            "author": "admin",
            "repository": "https://github.com/company/new-service.git",
            "createdAt": "2024-01-15T10:30:25Z",
            "configuration": {
                "triggers": ["push", "pull_request"],
                "stages": ["checkout", "test", "build", "deploy"],
                "environment": "staging"
            }
        }
    }';
}
```

### 5.3 运行流水线
```nginx
location /api/run_pipeline {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "流水线已启动",
        "data": {
            "pipelineId": "pipeline_001",
            "pipelineName": "user-service-pipeline",
            "runId": "run_12345",
            "status": "running",
            "startedAt": "2024-01-15T10:30:25Z",
            "estimatedDuration": "6m 30s",
            "triggeredBy": "admin",
            "branch": "main",
            "commit": {
                "hash": "a1b2c3d4",
                "message": "Fix user authentication bug",
                "author": "张三"
            }
        }
    }';
}
```

### 5.4 停止流水线
```nginx
location /api/stop_pipeline {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "流水线已停止",
        "data": {
            "pipelineId": "pipeline_002",
            "pipelineName": "order-service-pipeline",
            "runId": "run_67890",
            "status": "cancelled",
            "stoppedAt": "2024-01-15T10:30:25Z",
            "stoppedBy": "admin",
            "reason": "用户手动停止",
            "duration": "2m 15s"
        }
    }';
}
```

### 5.5 搜索流水线
```nginx
location /api/search_pipelines {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "搜索完成",
        "data": {
            "searchCriteria": {
                "keyword": "user",
                "status": "success",
                "author": "",
                "branch": ""
            },
            "totalCount": 1,
            "results": [
                {
                    "pipelineId": "pipeline_001",
                    "pipelineName": "user-service-pipeline",
                    "description": "用户服务CI/CD流水线",
                    "status": "success",
                    "branch": "main",
                    "author": "张三",
                    "lastRunTime": "2024-01-15T09:45:00Z",
                    "duration": "6m 30s",
                    "matchedFields": ["pipelineName", "description"]
                }
            ]
        }
    }';
}
```

## 6. 认证和用户管理接口

### 6.1 用户登出
```nginx
location /api/auth_logout {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "退出登录成功",
        "data": {
            "userId": "admin_001",
            "username": "管理员",
            "logoutTime": "2024-01-15T10:30:25Z",
            "sessionDuration": "2h 30m",
            "redirectUrl": "/login.html"
        }
    }';
}
```

### 6.2 获取用户权限
```nginx
location /api/user_permissions {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "userId": "admin_001",
            "role": "administrator",
            "permissions": [
                {
                    "module": "dashboard",
                    "actions": ["view", "export"]
                },
                {
                    "module": "deployment",
                    "actions": ["view", "create", "update", "delete", "scale", "restart", "stop"]
                },
                {
                    "module": "monitoring",
                    "actions": ["view", "resolve_alert", "mute_alert"]
                },
                {
                    "module": "pipeline",
                    "actions": ["view", "create", "run", "stop", "delete"]
                },
                {
                    "module": "topology",
                    "actions": ["view", "refresh", "view_logs"]
                }
            ],
            "lastUpdated": "2024-01-15T08:00:00Z"
        }
    }';
}
```

## 7. 系统配置接口

### 7.1 获取系统配置
```nginx
location /api/system_config {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "systemInfo": {
                "version": "v2.1.0",
                "buildTime": "2024-01-10T12:00:00Z",
                "environment": "production"
            },
            "features": {
                "autoRefresh": true,
                "realTimeMonitoring": true,
                "alertNotifications": true,
                "multiEnvironment": true
            },
            "limits": {
                "maxPipelines": 100,
                "maxApplications": 500,
                "maxAlerts": 1000,
                "sessionTimeout": 3600
            },
            "integrations": {
                "kubernetes": {
                    "enabled": true,
                    "version": "v1.28.0"
                },
                "prometheus": {
                    "enabled": true,
                    "endpoint": "http://prometheus:9090"
                },
                "grafana": {
                    "enabled": true,
                    "endpoint": "http://grafana:3000"
                }
            }
        }
    }';
}
```

### 7.2 更新系统配置
```nginx
location /api/update_system_config {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "系统配置更新成功",
        "data": {
            "configId": "config_001",
            "updatedBy": "admin",
            "updatedAt": "2024-01-15T10:30:25Z",
            "changes": [
                {
                    "field": "autoRefresh",
                    "oldValue": false,
                    "newValue": true
                },
                {
                    "field": "sessionTimeout",
                    "oldValue": 1800,
                    "newValue": 3600
                }
            ],
            "requiresRestart": false
        }
    }';
}
```

## 8. 数据导出接口

### 8.1 导出部署数据
```nginx
location /api/export_deployment_data {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "数据导出成功",
        "data": {
            "exportId": "export_001",
            "exportType": "deployment_trend",
            "format": "json",
            "fileSize": "2.5KB",
            "recordCount": 24,
            "downloadUrl": "/downloads/deployment_trend_20240115.json",
            "expiresAt": "2024-01-16T10:30:25Z",
            "createdAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

### 8.2 导出监控数据
```nginx
location /api/export_monitoring_data {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "监控数据导出成功",
        "data": {
            "exportId": "export_002",
            "exportType": "monitoring_metrics",
            "format": "csv",
            "fileSize": "15.2MB",
            "recordCount": 8640,
            "timeRange": "24h",
            "metrics": ["cpu", "memory", "network", "disk"],
            "downloadUrl": "/downloads/monitoring_metrics_20240115.csv",
            "expiresAt": "2024-01-16T10:30:25Z",
            "createdAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

## 9. 健康检查接口

### 9.1 系统健康检查
```nginx
location /api/health_check {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "系统运行正常",
        "data": {
            "status": "healthy",
            "timestamp": "2024-01-15T10:30:25Z",
            "uptime": "15d 8h 30m",
            "version": "v2.1.0",
            "components": {
                "database": {
                    "status": "healthy",
                    "responseTime": "12ms"
                },
                "redis": {
                    "status": "healthy",
                    "responseTime": "3ms"
                },
                "kubernetes": {
                    "status": "healthy",
                    "responseTime": "45ms"
                },
                "prometheus": {
                    "status": "healthy",
                    "responseTime": "28ms"
                }
            },
            "metrics": {
                "requestsPerSecond": 125,
                "averageResponseTime": "85ms",
                "errorRate": "0.02%"
            }
        }
    }';
}
```
