# DevOps平台 JSON API接口文档

## 1. 服务拓扑管理接口

### 1.1 获取服务拓扑列表
```nginx
location /api/topology_services {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "serviceId": "api-gateway",
                "serviceName": "API Gateway",
                "serviceType": "gateway",
                "status": "healthy",
                "instanceCount": 2,
                "version": "v1.0.0",
                "cpuUsage": 45,
                "memoryUsage": 512,
                "position": {
                    "x": 400,
                    "y": 100
                },
                "dependencies": ["user-service", "order-service", "payment-service"]
            },
            {
                "serviceId": "user-service",
                "serviceName": "User Service",
                "serviceType": "service",
                "status": "healthy",
                "instanceCount": 3,
                "version": "v1.2.3",
                "cpuUsage": 35,
                "memoryUsage": 768,
                "position": {
                    "x": 200,
                    "y": 250
                },
                "dependencies": ["user-db"]
            },
            {
                "serviceId": "order-service",
                "serviceName": "Order Service",
                "serviceType": "service",
                "status": "healthy",
                "instanceCount": 5,
                "version": "v2.1.0",
                "cpuUsage": 65,
                "memoryUsage": 1024,
                "position": {
                    "x": 400,
                    "y": 250
                },
                "dependencies": ["order-db", "payment-service"]
            },
            {
                "serviceId": "payment-service",
                "serviceName": "Payment Service",
                "serviceType": "service",
                "status": "unhealthy",
                "instanceCount": 2,
                "version": "v1.5.2",
                "cpuUsage": 85,
                "memoryUsage": 1536,
                "position": {
                    "x": 600,
                    "y": 250
                },
                "dependencies": ["payment-db", "external-payment-api"]
            },
            {
                "serviceId": "notification-service",
                "serviceName": "Notification Service",
                "serviceType": "service",
                "status": "healthy",
                "instanceCount": 2,
                "version": "v1.0.8",
                "cpuUsage": 25,
                "memoryUsage": 256,
                "position": {
                    "x": 800,
                    "y": 250
                },
                "dependencies": ["message-queue"]
            },
            {
                "serviceId": "user-db",
                "serviceName": "User Database",
                "serviceType": "database",
                "status": "healthy",
                "instanceCount": 1,
                "version": "PostgreSQL 13",
                "cpuUsage": 20,
                "memoryUsage": 2048,
                "position": {
                    "x": 200,
                    "y": 400
                },
                "dependencies": []
            },
            {
                "serviceId": "order-db",
                "serviceName": "Order Database",
                "serviceType": "database",
                "status": "healthy",
                "instanceCount": 1,
                "version": "PostgreSQL 13",
                "cpuUsage": 30,
                "memoryUsage": 4096,
                "position": {
                    "x": 400,
                    "y": 400
                },
                "dependencies": []
            },
            {
                "serviceId": "payment-db",
                "serviceName": "Payment Database",
                "serviceType": "database",
                "status": "healthy",
                "instanceCount": 1,
                "version": "PostgreSQL 13",
                "cpuUsage": 25,
                "memoryUsage": 2048,
                "position": {
                    "x": 600,
                    "y": 400
                },
                "dependencies": []
            },
            {
                "serviceId": "message-queue",
                "serviceName": "Message Queue",
                "serviceType": "middleware",
                "status": "healthy",
                "instanceCount": 3,
                "version": "RabbitMQ 3.8",
                "cpuUsage": 15,
                "memoryUsage": 512,
                "position": {
                    "x": 800,
                    "y": 400
                },
                "dependencies": []
            },
            {
                "serviceId": "external-payment-api",
                "serviceName": "External Payment API",
                "serviceType": "external",
                "status": "warning",
                "instanceCount": 1,
                "version": "v2.0",
                "cpuUsage": 0,
                "memoryUsage": 0,
                "position": {
                    "x": 600,
                    "y": 500
                },
                "dependencies": []
            }
        ]
    }';
}
```

### 1.2 获取服务依赖关系
```nginx
location /api/topology_dependencies {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "viewMode": "dependencies",
            "relationships": [
                {
                    "sourceService": "api-gateway",
                    "targetService": "user-service",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "api-gateway",
                    "targetService": "order-service",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "api-gateway",
                    "targetService": "payment-service",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "user-service",
                    "targetService": "user-db",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "order-service",
                    "targetService": "order-db",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "order-service",
                    "targetService": "payment-service",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "payment-service",
                    "targetService": "payment-db",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "payment-service",
                    "targetService": "external-payment-api",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "notification-service",
                    "targetService": "message-queue",
                    "relationshipType": "depends_on"
                }
            ]
        }
    }';
}
```

### 1.3 获取服务日志
```nginx
location /api/service_logs {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "serviceId": "user-service",
            "serviceName": "User Service",
            "logLevel": "INFO",
            "totalLines": 1250,
            "logs": [
                {
                    "timestamp": "2024-01-15T10:30:25.123Z",
                    "level": "INFO",
                    "message": "User authentication successful for user ID: 12345",
                    "source": "AuthController"
                },
                {
                    "timestamp": "2024-01-15T10:30:24.856Z",
                    "level": "DEBUG",
                    "message": "Database connection established",
                    "source": "DatabaseManager"
                },
                {
                    "timestamp": "2024-01-15T10:30:23.445Z",
                    "level": "INFO",
                    "message": "Processing user profile update request",
                    "source": "UserController"
                },
                {
                    "timestamp": "2024-01-15T10:30:22.123Z",
                    "level": "WARN",
                    "message": "High memory usage detected: 85%",
                    "source": "HealthMonitor"
                },
                {
                    "timestamp": "2024-01-15T10:30:21.789Z",
                    "level": "ERROR",
                    "message": "Failed to connect to external service: timeout after 5000ms",
                    "source": "ExternalServiceClient"
                }
            ],
            "pagination": {
                "currentPage": 1,
                "totalPages": 25,
                "pageSize": 50
            }
        }
    }';
}
```

### 1.4 保存用户偏好设置
```nginx
location /api/user_preferences {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "用户偏好设置保存成功",
        "data": {
            "userId": "admin",
            "preferences": {
                "topologyLayout": "circular",
                "defaultViewMode": "services",
                "autoRefreshInterval": 30,
                "showResourceMetrics": true,
                "theme": "light"
            },
            "updatedAt": "2024-01-15T10:30:25.123Z"
        }
    }';
}
```

### 1.5 过滤服务列表
```nginx
location /api/filter_services {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "filterCriteria": {
                "status": "healthy",
                "serviceType": "all",
                "searchKeyword": ""
            },
            "totalCount": 8,
            "filteredServices": [
                {
                    "serviceId": "api-gateway",
                    "serviceName": "API Gateway",
                    "serviceType": "gateway",
                    "status": "healthy",
                    "instanceCount": 2,
                    "healthScore": 95
                },
                {
                    "serviceId": "user-service",
                    "serviceName": "User Service",
                    "serviceType": "service",
                    "status": "healthy",
                    "instanceCount": 3,
                    "healthScore": 98
                },
                {
                    "serviceId": "order-service",
                    "serviceName": "Order Service",
                    "serviceType": "service",
                    "status": "healthy",
                    "instanceCount": 5,
                    "healthScore": 92
                },
                {
                    "serviceId": "notification-service",
                    "serviceName": "Notification Service",
                    "serviceType": "service",
                    "status": "healthy",
                    "instanceCount": 2,
                    "healthScore": 99
                },
                {
                    "serviceId": "user-db",
                    "serviceName": "User Database",
                    "serviceType": "database",
                    "status": "healthy",
                    "instanceCount": 1,
                    "healthScore": 97
                },
                {
                    "serviceId": "order-db",
                    "serviceName": "Order Database",
                    "serviceType": "database",
                    "status": "healthy",
                    "instanceCount": 1,
                    "healthScore": 94
                },
                {
                    "serviceId": "payment-db",
                    "serviceName": "Payment Database",
                    "serviceType": "database",
                    "status": "healthy",
                    "instanceCount": 1,
                    "healthScore": 96
                },
                {
                    "serviceId": "message-queue",
                    "serviceName": "Message Queue",
                    "serviceType": "middleware",
                    "status": "healthy",
                    "instanceCount": 3,
                    "healthScore": 98
                }
            ]
        }
    }';
}
```

## 2. DevOps Dashboard接口

### 2.1 获取通知列表
```nginx
location /api/notifications {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "notificationId": "notif_001",
                "title": "新任务通知",
                "message": "您有3个新任务需要处理",
                "type": "task",
                "priority": "medium",
                "isRead": false,
                "createdAt": "2024-01-15T10:25:00Z",
                "relativeTime": "2分钟前"
            },
            {
                "notificationId": "notif_002",
                "title": "数据采集完成",
                "message": "昨日数据采集已完成",
                "type": "system",
                "priority": "low",
                "isRead": false,
                "createdAt": "2024-01-15T09:30:00Z",
                "relativeTime": "1小时前"
            },
            {
                "notificationId": "notif_003",
                "title": "系统更新",
                "message": "平台将于今晚23:00进行维护",
                "type": "maintenance",
                "priority": "high",
                "isRead": true,
                "createdAt": "2024-01-15T08:00:00Z",
                "relativeTime": "2小时前"
            }
        ],
        "unreadCount": 2
    }';
}
```

### 2.2 标记通知已读
```nginx
location /api/mark_notification_read {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "通知已标记为已读",
        "data": {
            "notificationId": "notif_001",
            "isRead": true,
            "updatedAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

### 2.3 获取用户信息
```nginx
location /api/user_profile {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "userId": "admin_001",
            "username": "管理员",
            "email": "<EMAIL>",
            "role": "administrator",
            "department": "IT运维部",
            "avatar": "https://picsum.photos/id/1005/40/40",
            "lastLoginTime": "2024-01-15T09:00:00Z",
            "permissions": [
                "dashboard_view",
                "deployment_manage",
                "monitoring_view",
                "pipeline_manage",
                "topology_view"
            ]
        }
    }';
}
```

### 2.4 获取仪表板统计数据
```nginx
location /api/dashboard_stats {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "runningApplications": {
                "current": 24,
                "change": "+2",
                "changeType": "increase",
                "comparedTo": "昨日"
            },
            "todayDeployments": {
                "current": 18,
                "change": "+5",
                "changeType": "increase",
                "comparedTo": "昨日"
            },
            "activeAlerts": {
                "current": 3,
                "change": "-2",
                "changeType": "decrease",
                "comparedTo": "昨日"
            },
            "systemHealth": {
                "current": 98.5,
                "change": "+0.2%",
                "changeType": "increase",
                "comparedTo": "昨日"
            }
        }
    }';
}
```

### 2.5 获取部署趋势数据
```nginx
location /api/deployment_trend {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "timeRange": "24h",
            "dataPoints": [
                {
                    "time": "00:00",
                    "totalDeployments": 2,
                    "successfulDeployments": 2,
                    "failedDeployments": 0
                },
                {
                    "time": "04:00",
                    "totalDeployments": 1,
                    "successfulDeployments": 1,
                    "failedDeployments": 0
                },
                {
                    "time": "08:00",
                    "totalDeployments": 5,
                    "successfulDeployments": 4,
                    "failedDeployments": 1
                },
                {
                    "time": "12:00",
                    "totalDeployments": 8,
                    "successfulDeployments": 7,
                    "failedDeployments": 1
                },
                {
                    "time": "16:00",
                    "totalDeployments": 12,
                    "successfulDeployments": 11,
                    "failedDeployments": 1
                },
                {
                    "time": "20:00",
                    "totalDeployments": 6,
                    "successfulDeployments": 6,
                    "failedDeployments": 0
                }
            ]
        }
    }';
}
```
