# DevOps平台 API接口总结

## 📋 接口概览

基于页面内容分析，为DevOps平台的5个主要页面生成了**33个JSON API接口**，所有接口采用统一的Nginx location配置格式。

## 🔧 接口格式说明

### 统一响应格式
```json
{
  "code": 200,
  "message": "成功", 
  "data": { ... }
}
```

### Nginx配置格式
```nginx
location /api/XXXX {
    default_type "application/json;charset=gbk";
    return 200 '{"code":200,"message":"成功","data":YYYY}';
}
```

## 📊 接口分类统计

| 模块 | 接口数量 | 接口列表 |
|------|----------|----------|
| **服务拓扑管理** | 5个 | topology_services, topology_dependencies, service_logs, user_preferences, filter_services |
| **DevOps Dashboard** | 6个 | notifications, mark_notification_read, user_profile, dashboard_stats, deployment_trend, resource_usage |
| **容器部署管理** | 6个 | deployment_environments, deployment_applications, create_deployment, scale_application, restart_application, stop_application |
| **监控中心** | 4个 | monitoring_metrics, monitoring_alerts, resolve_alert, mute_alert |
| **CI/CD流水线** | 5个 | pipelines, create_pipeline, run_pipeline, stop_pipeline, search_pipelines |
| **认证用户管理** | 2个 | auth_logout, user_permissions |
| **系统配置** | 2个 | system_config, update_system_config |
| **数据导出** | 2个 | export_deployment_data, export_monitoring_data |
| **健康检查** | 1个 | health_check |

## 🎯 核心功能接口

### 1. 服务拓扑管理 (5个接口)

#### `/api/topology_services` - 获取服务拓扑列表
- **功能**: 获取所有服务的拓扑信息
- **返回**: 服务ID、名称、类型、状态、实例数、资源使用、位置坐标
- **用途**: 渲染服务拓扑图

#### `/api/topology_dependencies` - 获取服务依赖关系  
- **功能**: 获取服务间依赖关系
- **返回**: 源服务、目标服务、关系类型
- **用途**: 绘制服务依赖连线

#### `/api/service_logs` - 获取服务日志
- **功能**: 获取指定服务的日志信息
- **返回**: 日志条目、时间戳、级别、消息、分页信息
- **用途**: 服务日志查看窗口

#### `/api/user_preferences` - 保存用户偏好
- **功能**: 保存用户界面偏好设置
- **返回**: 用户ID、偏好配置、更新时间
- **用途**: 布局切换、主题设置

#### `/api/filter_services` - 过滤服务列表
- **功能**: 根据条件过滤服务
- **返回**: 过滤条件、总数、过滤结果
- **用途**: 服务搜索和状态过滤

### 2. DevOps Dashboard (6个接口)

#### `/api/notifications` - 获取通知列表
- **功能**: 获取用户通知
- **返回**: 通知ID、标题、消息、类型、优先级、已读状态
- **用途**: 通知下拉菜单

#### `/api/mark_notification_read` - 标记通知已读
- **功能**: 标记通知为已读
- **返回**: 通知ID、已读状态、更新时间
- **用途**: 通知状态管理

#### `/api/user_profile` - 获取用户信息
- **功能**: 获取当前用户详细信息
- **返回**: 用户ID、姓名、邮箱、角色、部门、头像、权限
- **用途**: 用户菜单显示

#### `/api/dashboard_stats` - 获取仪表板统计
- **功能**: 获取总览统计数据
- **返回**: 运行应用数、今日部署数、活跃告警数、系统健康度
- **用途**: 仪表板卡片显示

#### `/api/deployment_trend` - 获取部署趋势
- **功能**: 获取部署趋势图表数据
- **返回**: 时间范围、数据点、成功/失败部署数
- **用途**: 部署趋势图表

#### `/api/resource_usage` - 获取资源使用情况
- **功能**: 获取集群资源使用数据
- **返回**: 资源类型、使用百分比、总容量、已用容量、状态
- **用途**: 资源使用图表

### 3. 容器部署管理 (6个接口)

#### `/api/deployment_environments` - 获取环境列表
- **功能**: 获取所有部署环境
- **返回**: 环境ID、名称、状态、应用数量、资源使用
- **用途**: 环境标签页

#### `/api/deployment_applications` - 获取应用列表
- **功能**: 获取指定环境的应用
- **返回**: 应用ID、名称、状态、版本、实例信息、健康状态、资源使用
- **用途**: 应用列表展示

#### `/api/create_deployment` - 创建新部署
- **功能**: 创建新的应用部署
- **返回**: 部署ID、应用信息、状态、预计完成时间
- **用途**: 部署新应用按钮

#### `/api/scale_application` - 应用扩缩容
- **功能**: 执行应用扩缩容操作
- **返回**: 应用ID、扩缩容操作信息、状态、预计完成时间
- **用途**: 扩缩容按钮

#### `/api/restart_application` - 重启应用
- **功能**: 重启指定应用
- **返回**: 应用ID、重启操作信息、进度
- **用途**: 重启按钮

#### `/api/stop_application` - 停止应用
- **功能**: 停止指定应用
- **返回**: 应用ID、停止操作信息、进度
- **用途**: 停止按钮

### 4. 监控中心 (4个接口)

#### `/api/monitoring_metrics` - 获取监控指标
- **功能**: 获取系统监控指标数据
- **返回**: 时间范围、CPU/内存/网络/磁盘指标、数据点
- **用途**: 监控图表显示

#### `/api/monitoring_alerts` - 获取告警列表
- **功能**: 获取系统告警信息
- **返回**: 告警ID、名称、级别、状态、消息、服务、阈值、当前值
- **用途**: 告警列表展示

#### `/api/resolve_alert` - 解决告警
- **功能**: 标记告警为已解决
- **返回**: 告警ID、状态变更、解决人、解决时间
- **用途**: 解决告警按钮

#### `/api/mute_alert` - 静音告警
- **功能**: 静音指定告警
- **返回**: 告警ID、静音状态、静音人、静音时间、静音时长
- **用途**: 静音告警按钮

### 5. CI/CD流水线 (5个接口)

#### `/api/pipelines` - 获取流水线列表
- **功能**: 获取所有流水线信息
- **返回**: 流水线ID、名称、描述、状态、分支、作者、运行时间、构建号
- **用途**: 流水线列表展示

#### `/api/create_pipeline` - 创建新流水线
- **功能**: 创建新的CI/CD流水线
- **返回**: 流水线ID、名称、状态、配置信息
- **用途**: 新建流水线按钮

#### `/api/run_pipeline` - 运行流水线
- **功能**: 启动流水线执行
- **返回**: 流水线ID、运行ID、状态、触发信息、提交信息
- **用途**: 运行流水线按钮

#### `/api/stop_pipeline` - 停止流水线
- **功能**: 停止正在运行的流水线
- **返回**: 流水线ID、运行ID、停止状态、停止原因
- **用途**: 停止流水线按钮

#### `/api/search_pipelines` - 搜索流水线
- **功能**: 根据条件搜索流水线
- **返回**: 搜索条件、总数、搜索结果、匹配字段
- **用途**: 搜索和过滤功能

## 🔐 认证和系统接口 (7个接口)

### 认证用户管理 (2个)
- `/api/auth_logout` - 用户登出
- `/api/user_permissions` - 获取用户权限

### 系统配置 (2个)  
- `/api/system_config` - 获取系统配置
- `/api/update_system_config` - 更新系统配置

### 数据导出 (2个)
- `/api/export_deployment_data` - 导出部署数据
- `/api/export_monitoring_data` - 导出监控数据

### 健康检查 (1个)
- `/api/health_check` - 系统健康检查

## 🚀 使用说明

1. **接口基础路径**: `http://127.0.0.1/api`
2. **字符编码**: `charset=gbk`
3. **响应格式**: 统一JSON格式
4. **状态码**: 200表示成功
5. **数据字段**: 所有业务数据在`data`字段中

## 📝 实现特点

1. **真实数据结构**: 基于页面实际显示内容生成JSON
2. **英文字段名**: 所有JSON key使用英文命名
3. **完整信息**: 包含页面所需的所有数据字段
4. **标准格式**: 采用统一的响应格式和错误处理
5. **可扩展性**: 接口设计支持后续功能扩展
