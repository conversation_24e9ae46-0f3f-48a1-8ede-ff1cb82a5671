# DevOps平台完整JSON API接口文档

## 接口说明

本文档包含DevOps平台所有页面的JSON API接口定义，采用Nginx location配置格式。所有接口返回统一的JSON格式：

```json
{
  "code": 200,
  "message": "成功",
  "data": { ... }
}
```

## 接口列表概览

| 模块 | 接口数量 | 主要功能 |
|------|----------|----------|
| 服务拓扑管理 | 5个 | 服务列表、依赖关系、日志查看、用户偏好、服务过滤 |
| DevOps Dashboard | 6个 | 通知管理、用户信息、仪表板数据、部署趋势、资源使用 |
| 容器部署管理 | 6个 | 环境管理、应用管理、部署操作、扩缩容、重启停止 |
| 监控中心 | 4个 | 监控指标、告警管理、告警解决、告警静音 |
| CI/CD流水线 | 5个 | 流水线管理、创建运行、停止搜索 |
| 认证用户管理 | 2个 | 用户登出、权限管理 |
| 系统配置 | 2个 | 配置查看、配置更新 |
| 数据导出 | 2个 | 部署数据导出、监控数据导出 |
| 健康检查 | 1个 | 系统健康状态 |

**总计：33个API接口**

## 1. 服务拓扑管理接口

### 1.1 获取服务拓扑列表
**接口路径：** `/api/topology_services`
**请求方法：** GET
**功能描述：** 获取所有服务的拓扑信息，包括服务状态、实例数量、资源使用情况和位置坐标

```nginx
location /api/topology_services {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "serviceId": "api-gateway",
                "serviceName": "API Gateway",
                "serviceType": "gateway",
                "status": "healthy",
                "instanceCount": 2,
                "version": "v1.0.0",
                "cpuUsage": 45,
                "memoryUsage": 512,
                "position": {"x": 400, "y": 100},
                "dependencies": ["user-service", "order-service"]
            },
            {
                "serviceId": "user-service",
                "serviceName": "User Service",
                "serviceType": "service",
                "status": "healthy",
                "instanceCount": 3,
                "version": "v1.2.3",
                "cpuUsage": 35,
                "memoryUsage": 768,
                "position": {"x": 200, "y": 250},
                "dependencies": ["user-db"]
            }
        ]
    }';
}
```

### 1.2 获取服务依赖关系
**接口路径：** `/api/topology_dependencies`
**请求方法：** GET
**功能描述：** 获取服务间的依赖关系图

```nginx
location /api/topology_dependencies {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "viewMode": "dependencies",
            "relationships": [
                {
                    "sourceService": "api-gateway",
                    "targetService": "user-service",
                    "relationshipType": "depends_on"
                },
                {
                    "sourceService": "user-service",
                    "targetService": "user-db",
                    "relationshipType": "depends_on"
                }
            ]
        }
    }';
}
```

### 1.3 获取服务日志
**接口路径：** `/api/service_logs`
**请求方法：** GET
**功能描述：** 获取指定服务的日志信息

```nginx
location /api/service_logs {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "serviceId": "user-service",
            "serviceName": "User Service",
            "logLevel": "INFO",
            "totalLines": 1250,
            "logs": [
                {
                    "timestamp": "2024-01-15T10:30:25.123Z",
                    "level": "INFO",
                    "message": "User authentication successful for user ID: 12345",
                    "source": "AuthController"
                },
                {
                    "timestamp": "2024-01-15T10:30:24.856Z",
                    "level": "DEBUG",
                    "message": "Database connection established",
                    "source": "DatabaseManager"
                }
            ],
            "pagination": {
                "currentPage": 1,
                "totalPages": 25,
                "pageSize": 50
            }
        }
    }';
}
```

### 1.4 保存用户偏好设置
**接口路径：** `/api/user_preferences`
**请求方法：** POST
**功能描述：** 保存用户的界面偏好设置

```nginx
location /api/user_preferences {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "用户偏好设置保存成功",
        "data": {
            "userId": "admin",
            "preferences": {
                "topologyLayout": "circular",
                "defaultViewMode": "services",
                "autoRefreshInterval": 30,
                "showResourceMetrics": true,
                "theme": "light"
            },
            "updatedAt": "2024-01-15T10:30:25.123Z"
        }
    }';
}
```

### 1.5 过滤服务列表
**接口路径：** `/api/filter_services`
**请求方法：** GET
**功能描述：** 根据状态、类型等条件过滤服务列表

```nginx
location /api/filter_services {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "filterCriteria": {
                "status": "healthy",
                "serviceType": "all",
                "searchKeyword": ""
            },
            "totalCount": 8,
            "filteredServices": [
                {
                    "serviceId": "api-gateway",
                    "serviceName": "API Gateway",
                    "serviceType": "gateway",
                    "status": "healthy",
                    "instanceCount": 2,
                    "healthScore": 95
                }
            ]
        }
    }';
}
```

## 2. DevOps Dashboard接口

### 2.1 获取通知列表
**接口路径：** `/api/notifications`
**请求方法：** GET
**功能描述：** 获取用户通知列表

```nginx
location /api/notifications {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": [
            {
                "notificationId": "notif_001",
                "title": "新任务通知",
                "message": "您有3个新任务需要处理",
                "type": "task",
                "priority": "medium",
                "isRead": false,
                "createdAt": "2024-01-15T10:25:00Z",
                "relativeTime": "2分钟前"
            }
        ],
        "unreadCount": 2
    }';
}
```

### 2.2 标记通知已读
**接口路径：** `/api/mark_notification_read`
**请求方法：** POST
**功能描述：** 标记指定通知为已读状态

```nginx
location /api/mark_notification_read {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "通知已标记为已读",
        "data": {
            "notificationId": "notif_001",
            "isRead": true,
            "updatedAt": "2024-01-15T10:30:25Z"
        }
    }';
}
```

### 2.3 获取用户信息
**接口路径：** `/api/user_profile`
**请求方法：** GET
**功能描述：** 获取当前登录用户的详细信息

```nginx
location /api/user_profile {
    default_type "application/json;charset=gbk";
    return 200 '{
        "code": 200,
        "message": "成功",
        "data": {
            "userId": "admin_001",
            "username": "管理员",
            "email": "<EMAIL>",
            "role": "administrator",
            "department": "IT运维部",
            "avatar": "https://picsum.photos/id/1005/40/40",
            "lastLoginTime": "2024-01-15T09:00:00Z",
            "permissions": [
                "dashboard_view",
                "deployment_manage",
                "monitoring_view",
                "pipeline_manage",
                "topology_view"
            ]
        }
    }';
}
```
