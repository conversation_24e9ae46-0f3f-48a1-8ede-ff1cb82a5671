<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营报告应用</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      运营报告应用
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营报告应用</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex; width: 70%; flex-wrap: wrap; gap: 12px;">
        <div class="search-box" style="width: 300px; height: 70px; margin-bottom: 0;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" id="reportSearch" placeholder="搜索报告名称...">
        </div>
        <div>
          <label for="reportType" style="margin-right: 8px;">报告类型:</label>
          <select id="reportType" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部类型</option>
            <option value="daily">日报</option>
            <option value="weekly">周报</option>
            <option value="monthly">月报</option>
            <option value="activity">活动报告</option>
          </select>
        </div>
        <div>
          <label for="reportStatus" style="margin-right: 8px;">报告状态:</label>
          <select id="reportStatus" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="draft">草稿</option>
            <option value="reviewing">审核中</option>
            <option value="published">已发布</option>
            <option value="rejected">已驳回</option>
          </select>
        </div>
      <div style="display: flex; align-items: center; white-space: nowrap;">
          <label for="startDate" style="margin-right: 8px;">生成时间:</label>
          <input type="date" id="startDate" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); margin-right: 8px;">
          <span style="margin: 0 8px;">至</span>
          <input type="date" id="endDate" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
        </div>
      </div>
     <!-- 修改查询按钮所在区域的HTML，保持不变 -->
<div style="display: flex;">
  <button class="btn search-btn" onclick="searchReports()"><i class="fas fa-search"></i> 查询</button>
  <button class="btn reset-btn" onclick="resetReportSearch()" style="margin-left: 12px;"><i class="fas fa-undo"></i> 重置</button>
</div>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs" style="margin-bottom: 20px;">
      <div class="tab-item active" data-tab-target="reportListTab"><i class="fas fa-list"></i> 报告列表</div>
      <div class="tab-item" data-tab-target="reviewTab"><i class="fas fa-check-circle"></i> 报告审查</div>
      <div class="tab-item" data-tab-target="statisticsTab"><i class="fas fa-chart-bar"></i> 统计分析</div>
      <!-- 在统计分析标签后添加新标签 -->
<div class="tab-item" data-tab-target="accessRecordTab"><i class="fas fa-clipboard-check"></i> 运营报告查阅登记</div>
    </div>
    <!-- 添加运营报告查阅登记标签页内容 -->
<div class="tab-content" id="accessRecordTab">
  <div class="card">
    <div style="padding: 16px;">
      <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-clipboard-check"></i> 运营报告查阅登记</div>
      
      <form id="accessRecordForm">
        <input type="hidden" id="accessRecordId"> <!-- 自动生成，无需用户输入 -->
        
        <div class="form-group" style="margin-bottom: 16px;">
          <label for="recordReportInstanceId" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告实例ID <span style="color: red;">*</span></label>
          <input type="text" id="recordReportInstanceId" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
        </div>
        
        <div class="form-group" style="margin-bottom: 16px;">
          <label for="readingTime" style="display: block; margin-bottom: 8px; font-weight: 500;">阅读时间 <span style="color: red;">*</span></label>
          <input type="datetime-local" id="readingTime" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
        </div>
        
        <div class="form-group" style="margin-bottom: 16px;">
          <label for="readerId" style="display: block; margin-bottom: 8px; font-weight: 500;">阅读人工号 <span style="color: red;">*</span></label>
          <input type="text" id="readerId" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
        </div>
        
        <div class="form-group" style="margin-bottom: 16px;">
          <label for="readerOrg" style="display: block; margin-bottom: 8px; font-weight: 500;">阅读人机构 <span style="color: red;">*</span></label>
          <input type="text" id="readerOrg" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
        </div>
        
        <div class="form-group" style="margin-bottom: 16px;">
          <label for="readingDuration" style="display: block; margin-bottom: 8px; font-weight: 500;">阅读时长（分钟）<span style="color: red;">*</span></label>
          <input type="number" id="readingDuration" min="1" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
        </div>
        
        <div class="form-group">
          <button type="button" class="btn btn-primary" onclick="saveAccessRecord()"><i class="fas fa-save"></i> 保存查阅记录</button>
          <button type="button" class="btn" style="border: 1px solid var(--border-color); margin-left: 12px;" onclick="resetAccessRecordForm()"><i class="fas fa-undo"></i> 重置</button>
        </div>
      </form>

      <div style="margin-top: 24px;">
        <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-history"></i> 查阅记录历史</div>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>查阅记录ID</th>
                <th>报告实例ID</th>
                <th>阅读时间</th>
                <th>阅读人工号</th>
                <th>阅读人机构</th>
                <th>阅读时长(分钟)</th>
              </tr>
            </thead>
            <tbody id="accessRecordHistory">
              <!-- 查阅记录将通过JavaScript动态生成 -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>


    <!-- 标签页内容 -->
    <div class="tab-content active" id="reportListTab">
      <!-- 报告列表表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>报告名称</th>
                <th>报告类型</th>
                <th>生成时间</th>
                <th>状态</th>
                <th>创建人</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="reportListBody">
              <!-- 报告列表将通过JavaScript动态生成 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="reviewTab">
      <!-- 审查列表表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>报告名称</th>
                <th>报告类型</th>
                <th>生成时间</th>
                <th>提交人</th>
                <th>提交时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="reviewListBody">
              <!-- 审查列表将通过JavaScript动态生成 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="statisticsTab">
      <!-- 统计图表 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-chart-pie"></i> 报告状态分布</div>
          <div style="height: 300px;">
            <canvas id="statusChart"></canvas>
          </div>
        </div>
      </div>
      <div class="card">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-chart-bar"></i> 报告数量趋势</div>
          <div style="height: 300px;">
            <canvas id="trendChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 预览报告模态框 -->
  <div class="modal" id="previewReportModal">
    <div class="modal-content" style="width: 90%; max-width: 1000px;">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-eye"></i> 预览运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
        <div id="reportPreviewContent">
          <!-- 报告预览内容将通过JavaScript动态生成 -->
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('previewReportModal').classList.remove('show')">关闭</button>
        <button class="btn btn-secondary" onclick="generateWordDocument()"><i class="fas fa-file-word"></i> 生成Word文档</button>
        <button class="btn btn-primary" onclick="publishReport()"><i class="fas fa-publish"></i> 发布报告</button>
      </div>
    </div>
  </div>

  

  <!-- 审查报告模态框 -->
  <div class="modal" id="reviewReportModal">
    <div class="modal-content" style="width: 90%; max-width: 1000px;">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-check-circle"></i> 审查运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body" style="display: flex; flex-direction: column; gap: 20px;">
        <div style="max-height: 40vh; overflow-y: auto;">
          <div id="reviewReportContent">
            <!-- 报告内容将通过JavaScript动态生成 -->
          </div>
        </div>
        <div>
          <label for="reviewComment" style="display: block; margin-bottom: 8px; font-weight: 500;">审查意见</label>
          <textarea id="reviewComment" rows="5" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" placeholder="请输入审查意见"></textarea>
        </div>
        <div style="display: flex; gap: 12px;">
          <div style="display: flex; align-items: center;">
            <input type="radio" id="approve" name="reviewResult" value="approve" checked style="margin-right: 8px;">
            <label for="approve">通过</label>
          </div>
          <div style="display: flex; align-items: center;">
            <input type="radio" id="reject" name="reviewResult" value="reject" style="margin-right: 8px;">
            <label for="reject">驳回</label>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('reviewReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitReview()"><i class="fas fa-save"></i> 提交审查结果</button>
      </div>
    </div>
  </div>

  <!-- 调整报告内容模态框 -->
  <div class="modal" id="editReportModal">
    <div class="modal-content" style="width: 90%; max-width: 1000px;">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 调整报告内容</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div style="margin-bottom: 16px;">
          <label for="editReportContent" style="display: block; margin-bottom: 8px; font-weight: 500;">报告内容</label>
          <textarea id="editReportContent" rows="15" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="saveReportChanges()"><i class="fas fa-save"></i> 保存修改</button>
      </div>
    </div>
  </div>

  <!-- 发布报告模态框 -->
  <div class="modal" id="publishReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-publish"></i> 发布运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="publishReportForm">
          <input type="hidden" id="publishReportId">
          <div class="form-group">
            <label for="publishReportTitle" style="display: block; margin-bottom: 8px; font-weight: 500;">报告标题</label>
            <input type="text" id="publishReportTitle" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
          </div>
          <div class="form-group">
            <label for="publishRegion" style="display: block; margin-bottom: 8px; font-weight: 500;">发布区域</label>
            <select id="publishRegion" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
              <option value="">请选择发布区域</option>
              <option value="national">全国</option>
              <option value="north">华北地区</option>
              <option value="east">华东地区</option>
              <option value="south">华南地区</option>
              <option value="central">中部地区</option>
              <option value="northwest">西北地区</option>
              <option value="southwest">西南地区</option>
              <option value="northeast">东北地区</option>
            </select>
          </div>
          <div class="form-group">
            <label for="publishObject" style="display: block; margin-bottom: 8px; font-weight: 500;">发布对象</label>
            <select id="publishObject" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
              <option value="">请选择发布对象</option>
              <option value="all">全部用户</option>
              <option value="admin">管理员</option>
              <option value="operation">运营人员</option>
              <option value="sales">销售人员</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="form-group" id="customPublishObject" style="display: none;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">自定义发布对象</label>
            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
              <label style="display: flex; align-items: center;"><input type="checkbox" name="customObject" value="user1"> 用户1</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="customObject" value="user2"> 用户2</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="customObject" value="user3"> 用户3</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="customObject" value="user4"> 用户4</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="customObject" value="user5"> 用户5</label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('publishReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="confirmPublish()"><i class="fas fa-publish"></i> 确认发布</button>
      </div>
    </div>
  </div>

  <!-- 分享报告模态框 -->
  <div class="modal" id="shareReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-share-alt"></i> 分享运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="shareReportForm">
          <input type="hidden" id="shareReportId">
          <div class="form-group">
            <label for="shareObject" style="display: block; margin-bottom: 8px; font-weight: 500;">分享对象</label>
            <select id="shareObject" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
              <option value="">请选择分享对象</option>
              <option value="user">特定用户</option>
              <option value="department">部门</option>
              <option value="role">角色</option>
            </select>
          </div>
          <div class="form-group" id="userShareObject" style="display: none;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">选择用户</label>
            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareUser" value="user1"> 用户1</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareUser" value="user2"> 用户2</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareUser" value="user3"> 用户3</label>
            </div>
          </div>
          <div class="form-group" id="departmentShareObject" style="display: none;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">选择部门</label>
            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareDepartment" value="dept1"> 部门1</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareDepartment" value="dept2"> 部门2</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareDepartment" value="dept3"> 部门3</label>
            </div>
          </div>
          <div class="form-group" id="roleShareObject" style="display: none;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">选择角色</label>
            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareRole" value="role1"> 角色1</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareRole" value="role2"> 角色2</label>
              <label style="display: flex; align-items: center;"><input type="checkbox" name="shareRole" value="role3"> 角色3</label>
            </div>
          </div>
          <div class="form-group">
            <label for="shareComment" style="display: block; margin-bottom: 8px; font-weight: 500;">分享说明</label>
            <textarea id="shareComment" rows="3" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" placeholder="请输入分享说明"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('shareReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="confirmShare()"><i class="fas fa-share-alt"></i> 确认分享</button>
      </div>
    </div>
  </div>

  <!-- 点评报告模态框 -->
  <div class="modal" id="commentReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-comment"></i> 点评运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="commentReportForm">
          <input type="hidden" id="commentReportId">
           <div class="form-group">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">整体评价</label>
            <div style="display: flex; gap: 12px; white-space: nowrap;">
              <label style="display: flex; align-items: center;"><input type="radio" name="overallRating" value="positive" checked style="margin-right: 8px;"> 好评</label>
              <label style="display: flex; align-items: center;"><input type="radio" name="overallRating" value="negative" style="margin-right: 8px;"> 差评</label>
            </div>
          </div>
          <div class="form-group">
            <label for="commentContent" style="display: block; margin-bottom: 8px; font-weight: 500;">点评内容</label>
            <textarea id="commentContent" rows="5" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" placeholder="请输入点评内容" required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('commentReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitComment()"><i class="fas fa-save"></i> 提交点评</button>
      </div>
    </div>
  </div>

  <!-- 精准度打分模态框 -->
  <div class="modal" id="accuracyRatingModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-star"></i> 精准度打分</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="accuracyRatingForm">
          <input type="hidden" id="accuracyReportId">
          <div class="form-group">
            <label for="dataAccuracy" style="display: block; margin-bottom: 8px; font-weight: 500;">数据精准度</label>
            <div style="display: flex; align-items: center;">
              <input type="range" id="dataAccuracy" min="1" max="5" value="3" style="width: 200px; margin-right: 12px;">
              <span id="dataAccuracyValue">3</span> 分
            </div>
          </div>
          <div class="form-group">
            <label for="analysisAccuracy" style="display: block; margin-bottom: 8px; font-weight: 500;">分析精准度</label>
            <div style="display: flex; align-items: center;">
              <input type="range" id="analysisAccuracy" min="1" max="5" value="3" style="width: 200px; margin-right: 12px;">
              <span id="analysisAccuracyValue">3</span> 分
            </div>
          </div>
          <div class="form-group">
            <label for="predictionAccuracy" style="display: block; margin-bottom: 8px; font-weight: 500;">预测精准度</label>
            <div style="display: flex; align-items: center;">
              <input type="range" id="predictionAccuracy" min="1" max="5" value="3" style="width: 200px; margin-right: 12px;">
              <span id="predictionAccuracyValue">3</span> 分
            </div>
          </div>
          <div class="form-group">
            <label for="accuracyComment" style="display: block; margin-bottom: 8px; font-weight: 500;">打分说明</label>
            <textarea id="accuracyComment" rows="3" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" placeholder="请输入打分说明"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('accuracyRatingModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitAccuracyRating()"><i class="fas fa-save"></i> 提交打分</button>
      </div>
    </div>
  </div>

  <!-- 提交审核模态框 -->
  <div class="modal" id="submitReviewModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-paper-plane"></i> 提交运营报告审核</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="submitReviewForm">
          <input type="hidden" id="reviewReportId">
          <div class="form-group">
            <label for="reviewInstanceId" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告实例ID</label>
            <input type="text" id="reviewInstanceId" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
          </div>
          <div class="form-group">
            <label for="reviewReportTitle" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告标题</label>
            <input type="text" id="reviewReportTitle" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
          </div>
          <div class="form-group">
            <label for="reviewPublishRegion" style="display: block; margin-bottom: 8px; font-weight: 500;">发布区域</label>
            <select id="reviewPublishRegion" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
              <option value="">请选择发布区域</option>
              <option value="national">全国</option>
              <option value="north">华北地区</option>
              <option value="east">华东地区</option>
              <option value="south">华南地区</option>
              <option value="central">中部地区</option>
              <option value="northwest">西北地区</option>
              <option value="southwest">西南地区</option>
              <option value="northeast">东北地区</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reviewPublishObject" style="display: block; margin-bottom: 8px; font-weight: 500;">发布对象</label>
            <select id="reviewPublishObject" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" required>
              <option value="">请选择发布对象</option>
              <option value="all">全部用户</option>
              <option value="admin">管理员</option>
              <option value="operation">运营人员</option>
              <option value="sales">销售人员</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('submitReviewModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="confirmSubmitReview()"><i class="fas fa-check"></i> 确认提交</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // 模拟报告数据
    let reportData = [
      { id: 1, name: '用户增长分析报告', type: 'daily', typeText: '日报', generateTime: '2023-07-15 08:00', status: 'published', statusText: '已发布', creator: '系统管理员', content: '<h2>用户增长分析报告</h2><p>本报告分析了2023-07-15的用户增长情况...</p>' },
      { id: 2, name: '产品销售周报', type: 'weekly', typeText: '周报', generateTime: '2023-07-16 10:00', status: 'published', statusText: '已发布', creator: '系统管理员', content: '<h2>产品销售周报</h2><p>本报告分析了2023-07-10至2023-07-16的销售情况...</p>' },
      { id: 3, name: '6月运营数据分析', type: 'monthly', typeText: '月报', generateTime: '2023-07-01 14:45', status: 'reviewing', statusText: '审核中', creator: '系统管理员', content: '<h2>6月运营数据分析</h2><p>本报告分析了2023年6月的运营数据...</p>' },
      { id: 4, name: '活动效果评估报告', type: 'activity', typeText: '活动报告', generateTime: '2023-07-11 11:30', status: 'rejected', statusText: '已驳回', creator: '系统管理员', content: '<h2>活动效果评估报告</h2><p>本报告评估了2023-07-01至2023-07-10的促销活动效果...</p>' },
      { id: 5, name: '7月市场分析报告', type: 'monthly', typeText: '月报', generateTime: '2023-08-01 10:30', status: 'draft', statusText: '草稿', creator: '市场部', content: '<h2>7月市场分析报告</h2><p>本报告分析了2023年7月的市场情况...</p>' }
    ];

    // 模拟审查数据
    let reviewData = [
      { id: 1, reportId: 3, reportName: '6月运营数据分析', type: 'monthly', typeText: '月报', generateTime: '2023-07-01 14:45', submitter: '系统管理员', submitTime: '2023-07-01 15:00' },
      { id: 2, reportId: 5, reportName: '7月市场分析报告', type: 'monthly', typeText: '月报', generateTime: '2023-08-01 10:30', submitter: '市场部', submitTime: '2023-08-01 11:00' }
    ];

    // 存储运营报告发布信息
    let reportPublishInfo = [];

    // 当前选中的报告ID
    let currentReportId = null;

    // 渲染报告列表
    function renderReportList() {
      const tableBody = document.getElementById('reportListBody');
      tableBody.innerHTML = '';

      reportData.forEach(report => {
        const row = document.createElement('tr');
        // 状态标签样式
        let statusTag = '';
        switch(report.status) {
          case 'published':
            statusTag = '<span class="tag tag-success">已发布</span>';
            break;
          case 'reviewing':
            statusTag = '<span class="tag tag-info">审核中</span>';
            break;
          case 'draft':
            statusTag = '<span class="tag tag-warning">草稿</span>';
            break;
          case 'rejected':
            statusTag = '<span class="tag tag-danger">已驳回</span>';
            break;
        }

        // 操作按钮
        let actionButtons = '';
        if (report.status === 'draft') {
          actionButtons = `
            <button class="btn" style="color: var(--primary-color);" onclick="viewReport(${report.id})"><i class="fas fa-eye"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="editReport(${report.id})"><i class="fas fa-edit"></i></button>
            <button class="btn" style="color: var(--danger-color);" onclick="deleteReport(${report.id})"><i class="fas fa-trash"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="submitForReview(${report.id})" title="提交审核"><i class="fas fa-paper-plane"></i></button>
          `;
        } else if (report.status === 'reviewing') {
          actionButtons = `
            <button class="btn" style="color: var(--primary-color);" onclick="viewReport(${report.id})" title="查看报告"><i class="fas fa-eye"></i></button>
          `;
        } else if (report.status === 'rejected') {
          actionButtons = `
            <button class="btn" style="color: var(--primary-color);" onclick="viewReport(${report.id})" title="查看报告"><i class="fas fa-eye"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="editReport(${report.id})" title="修改报告"><i class="fas fa-edit"></i></button>
          `;
        } else if (report.status === 'published') {
          actionButtons = `
            <button class="btn" style="color: var(--primary-color);" onclick="viewReport(${report.id})" title="查看报告"><i class="fas fa-eye"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="downloadReport(${report.id})" title="下载报告"><i class="fas fa-download"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="shareReport(${report.id})" title="分享报告"><i class="fas fa-share-alt"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="commentReport(${report.id})" title="点评报告"><i class="fas fa-comment"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="rateAccuracy(${report.id})" title="精准度打分"><i class="fas fa-star"></i></button>
          `;
        }

        row.innerHTML = `
          <td>${report.name}</td>
          <td>${report.typeText}</td>
          <td>${report.generateTime}</td>
          <td>${statusTag}</td>
          <td>${report.creator}</td>
          <td>${actionButtons}</td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 渲染审查列表
    function renderReviewList() {
      const tableBody = document.getElementById('reviewListBody');
      tableBody.innerHTML = '';

      reviewData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.reportName}</td>
          <td>${item.typeText}</td>
          <td>${item.generateTime}</td>
          <td>${item.submitter}</td>
          <td>${item.submitTime}</td>
          <td>
            <button class="btn" style="color: var(--primary-color);" onclick="reviewReport(${item.reportId})" title="审查报告"><i class="fas fa-check-circle"></i></button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 初始化图表
    function initCharts() {
      // 报告状态分布图表
      const statusCtx = document.getElementById('statusChart').getContext('2d');
      const statusChart = new Chart(statusCtx, {
        type: 'pie',
        data: {
          labels: ['已发布', '审核中', '草稿', '已驳回'],
          datasets: [{
            data: [2, 1, 1, 1],
            backgroundColor: [
              'rgba(40, 167, 69, 0.7)',
              'rgba(0, 123, 255, 0.7)',
              'rgba(255, 193, 7, 0.7)',
              'rgba(220, 53, 69, 0.7)'
            ],
            borderColor: [
              'rgba(40, 167, 69, 1)',
              'rgba(0, 123, 255, 1)',
              'rgba(255, 193, 7, 1)',
              'rgba(220, 53, 69, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      });

      // 报告数量趋势图表
      const trendCtx = document.getElementById('trendChart').getContext('2d');
      const trendChart = new Chart(trendCtx, {
        type: 'bar',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
          datasets: [{
            label: '报告数量',
            data: [12, 15, 18, 14, 20, 22, 19],
            backgroundColor: 'rgba(0, 123, 255, 0.7)',
            borderColor: 'rgba(0, 123, 255, 1)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    // 查看报告
    function viewReport(id) {
      currentReportId = id;
      const report = reportData.find(item => item.id === id);
      if (report) {
        document.getElementById('reportPreviewContent').innerHTML = report.content;
        document.getElementById('previewReportModal').classList.add('show');

        // 记录查阅信息
        recordViewInfo(id);
      }
    }

    // 编辑报告
    function editReport(id) {
      currentReportId = id;
      const report = reportData.find(item => item.id === id);
      if (report) {
        document.getElementById('editReportContent').value = report.content;
        document.getElementById('editReportModal').classList.add('show');
      }
    }

    // 保存报告修改
    function saveReportChanges() {
      const content = document.getElementById('editReportContent').value;
      const report = reportData.find(item => item.id === currentReportId);
      if (report) {
        report.content = content;
        alert('报告内容已更新！');
        document.getElementById('editReportModal').classList.remove('show');
      }
    }

    // 删除报告
    function deleteReport(id) {
      if (confirm('确定要删除此报告吗？')) {
        reportData = reportData.filter(item => item.id !== id);
        renderReportList();
        alert('报告已删除！');
      }
    }

    // 提交审核
    function submitForReview(id) {
      currentReportId = id;
      const report = reportData.find(item => item.id === id);
      if (report) {
        // 填充表单默认值
        document.getElementById('reviewReportId').value = id;
        document.getElementById('reviewInstanceId').value = 'REP-' + Date.now(); // 生成默认ID
        document.getElementById('reviewReportTitle').value = report.name;
        
        // 显示提交审核模态框
        document.getElementById('submitReviewModal').classList.add('show');
      }
    }

    // 确认提交审核
    function confirmSubmitReview() {
      const reportId = parseInt(document.getElementById('reviewReportId').value);
      const instanceId = document.getElementById('reviewInstanceId').value;
      const title = document.getElementById('reviewReportTitle').value;
      const region = document.getElementById('reviewPublishRegion').value;
      const object = document.getElementById('reviewPublishObject').value;
      
      // 简单验证
      if (!instanceId || !title || !region || !object) {
        alert('请填写完整信息');
        return;
      }
      
      // 获取当前时间
      const now = new Date();
      const publishTime = now.getFullYear() + '-' + 
                         (now.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                         now.getDate().toString().padStart(2, '0') + ' ' + 
                         now.getHours().toString().padStart(2, '0') + ':' + 
                         now.getMinutes().toString().padStart(2, '0');
      
      // 获取发布人（从页面中获取）
      const publisher = '管理员'; // 实际应用中应该从登录信息获取
      
      // 保存发布信息
      reportPublishInfo.push({
        reportId: reportId,
        instanceId: instanceId,
        title: title,
        region: region,
        object: object,
        publishTime: publishTime,
        publisher: publisher
      });
      
      // 更新报告状态
      const report = reportData.find(item => item.id === reportId);
      if (report) {
        report.status = 'reviewing';
        report.statusText = '审核中';
        renderReportList();
      }
      
      // 添加到审查列表
      const reviewItem = reportData.find(item => item.id === reportId);
      if (reviewItem) {
        reviewData.push({
          id: reviewData.length + 1,
          reportId: reportId,
          reportName: reviewItem.name,
          type: reviewItem.type,
          typeText: reviewItem.typeText,
          generateTime: reviewItem.generateTime,
          submitter: publisher,
          submitTime: publishTime
        });
        renderReviewList();
      }
      
      // 关闭模态框并提示
      document.getElementById('submitReviewModal').classList.remove('show');
      alert('报告已提交审核！');
      
      // 打印保存的信息（实际应用中应该发送到服务器）
      console.log('保存的发布信息:', reportPublishInfo[reportPublishInfo.length - 1]);
    }

    // 记录查阅信息
    function recordViewInfo(id) {
      console.log(`用户查阅了报告 ID: ${id}`);
      // 实际应用中可以发送到服务器记录
    }

    // 下载报告
    function downloadReport(id) {
      alert('正在下载报告...');
      console.log(`下载报告 ID: ${id}`);
    }

    // 分享报告
    function shareReport(id) {
      currentReportId = id;
      document.getElementById('shareReportId').value = id;
      document.getElementById('shareReportModal').classList.add('show');
    }

    // 确认分享
    function confirmShare() {
      // 处理分享逻辑
      alert('报告分享成功！');
      document.getElementById('shareReportModal').classList.remove('show');
    }

    // 点评报告
    function commentReport(id) {
      currentReportId = id;
      document.getElementById('commentReportId').value = id;
      document.getElementById('commentReportModal').classList.add('show');
    }

    // 提交点评
    function submitComment() {
      // 处理点评提交逻辑
      alert('点评提交成功！');
      document.getElementById('commentReportModal').classList.remove('show');
    }

    // 精准度打分
    function rateAccuracy(id) {
      currentReportId = id;
      document.getElementById('accuracyReportId').value = id;
      document.getElementById('accuracyRatingModal').classList.add('show');
    }

    // 提交打分
    function submitAccuracyRating() {
      // 处理打分提交逻辑
      alert('精准度打分提交成功！');
      document.getElementById('accuracyRatingModal').classList.remove('show');
    }

    // 审查报告
    function reviewReport(id) {
      currentReportId = id;
      const report = reportData.find(item => item.id === id);
      if (report) {
        document.getElementById('reviewReportContent').innerHTML = report.content;
        document.getElementById('reviewReportModal').classList.add('show');
      }
    }

    // 提交审查结果
    function submitReview() {
      // 处理审查结果提交逻辑
      alert('审查结果已提交！');
      document.getElementById('reviewReportModal').classList.remove('show');
    }

    // 发布报告
    function publishReport() {
      document.getElementById('publishReportId').value = currentReportId;
      const report = reportData.find(item => item.id === currentReportId);
      if (report) {
        document.getElementById('publishReportTitle').value = report.name;
      }
      document.getElementById('publishReportModal').classList.add('show');
    }

    // 确认发布
    function confirmPublish() {
      // 处理发布逻辑
      alert('报告发布成功！');
      document.getElementById('publishReportModal').classList.remove('show');
      document.getElementById('previewReportModal').classList.remove('show');
    }

    // 搜索报告
    function searchReports() {
      // 处理搜索逻辑
      alert('搜索功能执行中...');
    }

// 在现有script中添加或修改以下函数

// 保存原始报告数据，用于重置功能
const originalReportData = [...reportData];

// 搜索报告
function searchReports() {
  // 获取查询条件
  const reportName = document.getElementById('reportSearch').value.trim().toLowerCase();
  const reportType = document.getElementById('reportType').value;
  const reportStatus = document.getElementById('reportStatus').value;
  const startDate = document.getElementById('startDate').value;
  const endDate = document.getElementById('endDate').value;
  
  // 从原始数据中筛选
  const filteredReports = originalReportData.filter(report => {
    // 报告名称筛选（模糊匹配）
    if (reportName && !report.name.toLowerCase().includes(reportName)) {
      return false;
    }
    
    // 报告类型筛选
    if (reportType !== 'all' && report.type !== reportType) {
      return false;
    }
    
    // 报告状态筛选
    if (reportStatus !== 'all' && report.status !== reportStatus) {
      return false;
    }
    
    // 生成时间筛选
    const reportDate = new Date(report.generateTime).toISOString().split('T')[0];
    if (startDate && new Date(reportDate) < new Date(startDate)) {
      return false;
    }
    if (endDate && new Date(reportDate) > new Date(endDate)) {
      return false;
    }
    
    return true;
  });
  
  // 更新当前显示的数据并重新渲染
  reportData = [...filteredReports];
  renderReportList();
  
  // 如果没有匹配结果，显示提示
  if (filteredReports.length === 0) {
    document.getElementById('reportListBody').innerHTML = `
      <tr>
        <td colspan="6" style="text-align: center; padding: 20px;">没有找到匹配的报告</td>
      </tr>
    `;
  }
}

// 重置查询条件
function resetReportSearch() {
  // 清空输入框
  document.getElementById('reportSearch').value = '';
  document.getElementById('reportType').value = 'all';
  document.getElementById('reportStatus').value = 'all';
  document.getElementById('startDate').value = '';
  document.getElementById('endDate').value = '';
  
  // 恢复原始数据并重新渲染
  reportData = [...originalReportData];
  renderReportList();
}

// 保持其他现有函数不变...

    // 生成Word文档
    function generateWordDocument() {
      alert('正在生成Word文档...');
    }

    // 页面加载完成后初始化
    window.onload = function() {
      renderReportList();
      renderReviewList();
      initCharts();

      // 为评分滑块添加事件监听
      document.getElementById('dataAccuracy').addEventListener('input', function() {
        document.getElementById('dataAccuracyValue').textContent = this.value;
      });
      document.getElementById('analysisAccuracy').addEventListener('input', function() {
        document.getElementById('analysisAccuracyValue').textContent = this.value;
      });
      document.getElementById('predictionAccuracy').addEventListener('input', function() {
        document.getElementById('predictionAccuracyValue').textContent = this.value;
      });

      // 为发布对象选择添加事件监听
      document.getElementById('publishObject').addEventListener('change', function() {
        if (this.value === 'custom') {
          document.getElementById('customPublishObject').style.display = 'block';
        } else {
          document.getElementById('customPublishObject').style.display = 'none';
        }
      });

      // 为分享对象选择添加事件监听
      document.getElementById('shareObject').addEventListener('change', function() {
        document.getElementById('userShareObject').style.display = 'none';
        document.getElementById('departmentShareObject').style.display = 'none';
        document.getElementById('roleShareObject').style.display = 'none';
        
        if (this.value === 'user') {
          document.getElementById('userShareObject').style.display = 'block';
        } else if (this.value === 'department') {
          document.getElementById('departmentShareObject').style.display = 'block';
        } else if (this.value === 'role') {
          document.getElementById('roleShareObject').style.display = 'block';
        }
      });

      // 标签页切换功能
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', function() {
          // 移除所有标签页的active类
          tabItems.forEach(tab => tab.classList.remove('active'));
          // 给当前点击的标签页添加active类
          this.classList.add('active');
          
          // 隐藏所有标签内容
          const tabContents = document.querySelectorAll('.tab-content');
          tabContents.forEach(content => content.classList.remove('active'));
          
          // 显示对应的标签内容
          const target = this.getAttribute('data-tab-target');
          document.getElementById(target).classList.add('active');
        });
      });

      // 关闭模态框功能
      const modalCloseButtons = document.querySelectorAll('.modal-close');
      modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
          this.closest('.modal').classList.remove('show');
        });
      });
    };
  </script>
  <script>
  // 存储查阅记录数据
  let accessRecords = [];
  
  // 初始化时设置当前时间为默认阅读时间
  document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    // 格式化时间为datetime-local所需的格式
    const formattedDateTime = now.toISOString().slice(0, 16);
    document.getElementById('readingTime').value = formattedDateTime;
    
    // 初始化标签页切换功能
    initAccessRecordTab();
    
    // 添加API调用功能
    addApiCallToButtons();
  });

  // 为各个按钮添加API调用功能
  function addApiCallToButtons() {
    // 查询按钮
    const searchBtn = document.querySelector('button[onclick="searchReports()"]');
    if (searchBtn) {
      searchBtn.addEventListener('click', function() {
        // 调用查询报告接口
        fetch('http://localhost:8000/api/report/application/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'search_reports',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('查询报告接口调用完成');
        });
      });
    }

    // 重置按钮
    const resetBtn = document.querySelector('button[onclick="resetReportSearch()"]');
    if (resetBtn) {
      resetBtn.addEventListener('click', function() {
        // 调用重置接口
        fetch('http://localhost:8000/api/report/application/reset', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'reset_report_search',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('重置报告搜索接口调用完成');
        });
      });
    }

    // 保存查阅记录按钮
    const saveRecordBtn = document.querySelector('button[onclick="saveAccessRecord()"]');
    if (saveRecordBtn) {
      saveRecordBtn.addEventListener('click', function() {
        // 调用保存查阅记录接口
        fetch('http://localhost:8000/api/report/application/save_access_record', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'save_access_record',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('保存查阅记录接口调用完成');
        });
      });
    }

    // 重置查阅记录表单按钮
    const resetRecordBtn = document.querySelector('button[onclick="resetAccessRecordForm()"]');
    if (resetRecordBtn) {
      resetRecordBtn.addEventListener('click', function() {
        // 调用重置查阅记录接口
        fetch('http://localhost:8000/api/report/application/reset_access_record', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'reset_access_record',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('重置查阅记录接口调用完成');
        });
      });
    }

    // 生成Word文档按钮
    const generateWordBtn = document.querySelector('button[onclick="generateWordDocument()"]');
    if (generateWordBtn) {
      generateWordBtn.addEventListener('click', function() {
        // 调用生成Word文档接口
        fetch('http://localhost:8000/api/report/application/generate_word', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'generate_word_document',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('生成Word文档接口调用完成');
        });
      });
    }

    // 发布报告按钮
    const publishBtn = document.querySelector('button[onclick="publishReport()"]');
    if (publishBtn) {
      publishBtn.addEventListener('click', function() {
        // 调用发布报告接口
        fetch('http://localhost:8000/api/report/application/publish', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'publish_report',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('发布报告接口调用完成');
        });
      });
    }

    // 提交审查结果按钮
    const submitReviewBtn = document.querySelector('button[onclick="submitReview()"]');
    if (submitReviewBtn) {
      submitReviewBtn.addEventListener('click', function() {
        // 调用提交审查结果接口
        fetch('http://localhost:8000/api/report/application/submit_review', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'submit_review',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('提交审查结果接口调用完成');
        });
      });
    }

    // 保存修改按钮
    const saveChangesBtn = document.querySelector('button[onclick="saveReportChanges()"]');
    if (saveChangesBtn) {
      saveChangesBtn.addEventListener('click', function() {
        // 调用保存修改接口
        fetch('http://localhost:8000/api/report/application/save_changes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'save_report_changes',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('保存修改接口调用完成');
        });
      });
    }

    // 确认发布按钮
    const confirmPublishBtn = document.querySelector('button[onclick="confirmPublish()"]');
    if (confirmPublishBtn) {
      confirmPublishBtn.addEventListener('click', function() {
        // 调用确认发布接口
        fetch('http://localhost:8000/api/report/application/confirm_publish', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'confirm_publish',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('确认发布接口调用完成');
        });
      });
    }

    // 确认分享按钮
    const confirmShareBtn = document.querySelector('button[onclick="confirmShare()"]');
    if (confirmShareBtn) {
      confirmShareBtn.addEventListener('click', function() {
        // 调用确认分享接口
        fetch('http://localhost:8000/api/report/application/confirm_share', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'confirm_share',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('确认分享接口调用完成');
        });
      });
    }

    // 提交点评按钮
    const submitCommentBtn = document.querySelector('button[onclick="submitComment()"]');
    if (submitCommentBtn) {
      submitCommentBtn.addEventListener('click', function() {
        // 调用提交点评接口
        fetch('http://localhost:8000/api/report/application/submit_comment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'submit_comment',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('提交点评接口调用完成');
        });
      });
    }

    // 提交打分按钮
    const submitRatingBtn = document.querySelector('button[onclick="submitAccuracyRating()"]');
    if (submitRatingBtn) {
      submitRatingBtn.addEventListener('click', function() {
        // 调用提交打分接口
        fetch('http://localhost:8000/api/report/application/submit_rating', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'submit_accuracy_rating',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('提交打分接口调用完成');
        });
      });
    }

    // 确认提交按钮
    const confirmSubmitBtn = document.querySelector('button[onclick="confirmSubmitReview()"]');
    if (confirmSubmitBtn) {
      confirmSubmitBtn.addEventListener('click', function() {
        // 调用确认提交接口
        fetch('http://localhost:8000/api/report/application/confirm_submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'confirm_submit_review',
            timestamp: new Date().toISOString()
          })
        }).catch(error => {
          console.log('确认提交接口调用完成');
        });
      });
    }
  }
  
  // 初始化查阅登记标签页
  function initAccessRecordTab() {
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(item => {
      item.addEventListener('click', function() {
        // 移除所有标签页的active类
        document.querySelectorAll('.tab-item').forEach(tab => {
          tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        
        // 为当前点击的标签页添加active类
        this.classList.add('active');
        const target = this.getAttribute('data-tab-target');
        document.getElementById(target).classList.add('active');
        
        // 如果切换到查阅登记标签页，刷新记录列表
        if (target === 'accessRecordTab') {
          renderAccessRecords();
        }
      });
    });
  }
  
  // 保存查阅记录
  function saveAccessRecord() {
    // 获取表单数据
    const reportInstanceId = document.getElementById('recordReportInstanceId').value;
    const readingTime = document.getElementById('readingTime').value;
    const readerId = document.getElementById('readerId').value;
    const readerOrg = document.getElementById('readerOrg').value;
    const readingDuration = document.getElementById('readingDuration').value;
    
    // 简单验证
    if (!reportInstanceId || !readingTime || !readerId || !readerOrg || !readingDuration) {
      alert('请填写所有必填字段');
      return;
    }
    
    // 生成唯一ID
    const recordId = 'AR' + Date.now();
    
    // 创建记录对象
    const newRecord = {
      id: recordId,
      reportInstanceId: reportInstanceId,
      readingTime: readingTime,
      readerId: readerId,
      readerOrg: readerOrg,
      readingDuration: readingDuration
    };
    
    // 保存记录
    accessRecords.push(newRecord);
    
    // 刷新记录列表
    renderAccessRecords();
    
    // 重置表单
    resetAccessRecordForm();
    
    alert('查阅记录保存成功！');
  }
  
  // 渲染查阅记录列表
  function renderAccessRecords() {
    const tableBody = document.getElementById('accessRecordHistory');
    tableBody.innerHTML = '';
    
    if (accessRecords.length === 0) {
      const emptyRow = document.createElement('tr');
      emptyRow.innerHTML = '<td colspan="6" style="text-align: center; padding: 20px;">暂无查阅记录</td>';
      tableBody.appendChild(emptyRow);
      return;
    }
    
    // 倒序显示，最新的记录在前面
    const sortedRecords = [...accessRecords].reverse();
    
    sortedRecords.forEach(record => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${record.id}</td>
        <td>${record.reportInstanceId}</td>
        <td>${formatDateTime(record.readingTime)}</td>
        <td>${record.readerId}</td>
        <td>${record.readerOrg}</td>
        <td>${record.readingDuration}</td>
      `;
      tableBody.appendChild(row);
    });
  }
  
  // 重置表单
  function resetAccessRecordForm() {
    document.getElementById('accessRecordForm').reset();
    // 重置后仍设置当前时间为默认值
    const now = new Date();
    const formattedDateTime = now.toISOString().slice(0, 16);
    document.getElementById('readingTime').value = formattedDateTime;
  }
  
  // 格式化日期时间显示
  function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(',', ' ');
  }
</script>
</body>
</html>