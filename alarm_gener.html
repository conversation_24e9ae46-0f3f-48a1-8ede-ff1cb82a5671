<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <title>告警生成系统</title>
    <style>
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        h1 {
            color: var(--primary-color);
            font-size: 24px;
        }

        .tabs {
            display: flex;
            background-color: var(--card-bg);
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .tab-button {
            padding: 15px 20px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: var(--primary-color);
            border-bottom: 3px solid var(--primary-color);
        }

        .tab-button:hover:not(.active) {
            color: var(--secondary-color);
            background-color: rgba(59, 130, 246, 0.05);
        }

        .tab-content {
            display: none;
            padding: 25px;
            background-color: var(--card-bg);
            border-radius: 0 0 8px 8px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #1E3A8A;
        }

        .btn-accent {
            background-color: var(--accent-color);
            color: white;
        }

        .btn-accent:hover {
            background-color: #EA580C;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--border-color);
        }

        .btn-outline:hover {
            background-color: var(--background);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .table-container {
            margin-top: 20px;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: rgba(59, 130, 246, 0.05);
            font-weight: 500;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
        }

        .modal {
            background-color: var(--card-bg);
            border-radius: 8px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-light);
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-buttons button {
            padding: 6px 10px;
            font-size: 13px;
        }

        .required-mark {
            color: var(--danger-color);
        }
    </style>
</head>
<body>

     <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child " data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child  active" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
   <div class="main-content">

    <div class="page-title">
      <i class="fas fa-shield-alt page-title-icon"></i>
      告警生成系统
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透</a></div>
      <div class="breadcrumb-item active">告警生成系统</div>
    </div>

     <div class="tabs">
            <button class="tab-button active" data-tab="event">新增告警事件</button>
            <button class="tab-button" data-tab="content">告警内容定制</button>
            <button class="tab-button" data-tab="aggregation">告警聚合策略</button>
            <button class="tab-button" data-tab="suppression">告警抑制规则</button>
        </div>

        <!-- 新增告警事件 -->
        <div class="tab-content active" id="event-tab">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>告警事件列表</h2>
                <button class="btn btn-accent" id="add-event-btn">新增事件</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>事件ID</th>
                            <th>等级</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="event-table-body">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                        <tr>
                            <td>EVT-2023-001</td>
                            <td><span class="badge badge-danger">严重</span></td>
                            <td>系统错误</td>
                            <td><span class="badge badge-success">已处理</span></td>
                            <td>2023-11-01 10:23</td>
                            <td class="action-buttons">
                                <button class="btn btn-outline" onclick="viewEvent('EVT-2023-001')">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 告警内容定制 -->
        <div class="tab-content" id="content-tab">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>告警内容模板</h2>
                <button class="btn btn-accent" id="add-content-btn">新增模板</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>模板ID</th>
                            <th>标题</th>
                            <th>类型</th>
                            <th>版本</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="content-table-body">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                        <tr>
                            <td>TPL-2023-001</td>
                            <td>系统错误告警</td>
                            <td>邮件模板</td>
                            <td>v1.2</td>
                            <td>2023-10-25 14:30</td>
                            <td class="action-buttons">
                                <button class="btn btn-outline" onclick="viewContent('TPL-2023-001')">查看</button>
                                <button class="btn btn-outline" onclick="editContent('TPL-2023-001')">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 告警聚合策略 -->
        <div class="tab-content" id="aggregation-tab">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>聚合策略列表</h2>
                <button class="btn btn-accent" id="add-aggregation-btn">新增策略</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>策略名称</th>
                            <th>聚合字段</th>
                            <th>聚合周期</th>
                            <th>阈值</th>
                            <th>创建人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="aggregation-table-body">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                        <tr>
                            <td>系统错误聚合</td>
                            <td>事件类型</td>
                            <td>30分钟</td>
                            <td>5次</td>
                            <td>admin</td>
                            <td class="action-buttons">
                                <button class="btn btn-outline" onclick="viewAggregation('系统错误聚合')">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 告警抑制规则 -->
        <div class="tab-content" id="suppression-tab">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>抑制规则列表</h2>
                <button class="btn btn-accent" id="add-suppression-btn">新增规则</button>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>规则名称</th>
                            <th>抑制条件</th>
                            <th>抑制时长</th>
                            <th>适用对象</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="suppression-table-body">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                        <tr>
                            <td>临时抑制警告</td>
                            <td>eventLevel='warning'</td>
                            <td>1小时</td>
                            <td>所有服务器</td>
                            <td><span class="badge badge-success">启用</span></td>
                            <td class="action-buttons">
                                <button class="btn btn-outline" onclick="viewSuppression('临时抑制警告')">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    

    <!-- 新增告警事件模态框 -->
    <div class="modal-overlay" id="event-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新增告警事件</h3>
                <button class="modal-close" id="event-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="event-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventId">事件ID <span class="required-mark">*</span></label>
                            <input type="text" id="eventId" required placeholder="例如：EVT-2023-001">
                        </div>
                        <div class="form-group">
                            <label for="eventLevel">事件等级 <span class="required-mark">*</span></label>
                            <select id="eventLevel" required>
                                <option value="">请选择</option>
                                <option value="info">信息</option>
                                <option value="warning">警告</option>
                                <option value="error">错误</option>
                                <option value="critical">严重</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventType">事件类型 <span class="required-mark">*</span></label>
                            <select id="eventType" required>
                                <option value="">请选择</option>
                                <option value="system">系统错误</option>
                                <option value="network">网络问题</option>
                                <option value="application">应用错误</option>
                                <option value="security">安全事件</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="eventStatus">事件状态 <span class="required-mark">*</span></label>
                            <select id="eventStatus" required>
                                <option value="">请选择</option>
                                <option value="pending">待处理</option>
                                <option value="processing">处理中</option>
                                <option value="resolved">已解决</option>
                                <option value="ignored">已忽略</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="eventDescription">事件描述</label>
                        <textarea id="eventDescription" rows="3" placeholder="请输入事件详细描述"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventSource">事件源</label>
                            <input type="text" id="eventSource" placeholder="例如：server-01">
                        </div>
                        <div class="form-group">
                            <label for="eventTime">事件时间 <span class="required-mark">*</span></label>
                            <input type="datetime-local" id="eventTime" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="event-modal-cancel">取消</button>
                <button class="btn btn-accent" id="save-event-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 新增告警内容定制模态框 -->
    <div class="modal-overlay" id="content-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新增告警内容模板</h3>
                <button class="modal-close" id="content-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="content-form">
                    <div class="form-group">
                        <label for="templateTitle">模板标题 <span class="required-mark">*</span></label>
                        <input type="text" id="templateTitle" required placeholder="请输入模板标题">
                    </div>

                    <div class="form-group">
                        <label for="templateType">模板类型 <span class="required-mark">*</span></label>
                        <select id="templateType" required>
                            <option value="">请选择模板类型</option>
                            <option value="email">邮件模板</option>
                            <option value="sms">短信模板</option>
                            <option value="system">系统消息模板</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="templateContent">内容模板 <span class="required-mark">*</span></label>
                        <textarea id="templateContent" rows="6" required placeholder="请输入内容模板，支持变量如${eventId}, ${eventName}, ${eventTime}"></textarea>
                        <small style="color: var(--text-light); margin-top: 5px; display: block;">支持变量：${eventId}, ${eventName}, ${eventTime}, ${severity}, ${location}</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="content-modal-cancel">取消</button>
                <button class="btn btn-accent" id="save-content-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 新增告警聚合策略模态框 -->
    <div class="modal-overlay" id="aggregation-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新增告警聚合策略</h3>
                <button class="modal-close" id="aggregation-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="aggregation-form">
                    <div class="form-group">
                        <label for="strategyName">策略名称 <span class="required-mark">*</span></label>
                        <input type="text" id="strategyName" required placeholder="请输入策略名称">
                    </div>

                    <div class="form-group">
                        <label for="aggregationField">聚合字段 <span class="required-mark">*</span></label>
                        <select id="aggregationField" required>
                            <option value="">请选择聚合字段</option>
                            <option value="eventType">事件类型</option>
                            <option value="eventSource">事件源</option>
                            <option value="eventLevel">事件等级</option>
                            <option value="affectedObject">影响对象</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="aggregationCycle">聚合周期 <span class="required-mark">*</span></label>
                            <input type="number" id="aggregationCycle" min="1" required placeholder="请输入聚合周期">
                        </div>
                        <div class="form-group">
                            <label for="cycleUnit">周期单位 <span class="required-mark">*</span></label>
                            <select id="cycleUnit" required>
                                <option value="minute">分钟</option>
                                <option value="hour">小时</option>
                                <option value="day">天</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="aggregationThreshold">阈值 <span class="required-mark">*</span></label>
                        <input type="number" id="aggregationThreshold" min="1" required placeholder="请输入阈值">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="aggregation-modal-cancel">取消</button>
                <button class="btn btn-accent" id="save-aggregation-btn">保存</button>
            </div>
        </div>
    </div>

    <!-- 新增告警抑制规则模态框 -->
    <div class="modal-overlay" id="suppression-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新增告警抑制规则</h3>
                <button class="modal-close" id="suppression-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="suppression-form">
                    <div class="form-group">
                        <label for="ruleName">规则名称 <span class="required-mark">*</span></label>
                        <input type="text" id="ruleName" required placeholder="请输入规则名称">
                    </div>

                    <div class="form-group">
                        <label for="suppressionCondition">抑制条件 <span class="required-mark">*</span></label>
                        <textarea id="suppressionCondition" rows="3" required placeholder="请输入抑制条件，如 eventLevel='warning' AND eventType='system'""></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="suppressionDuration">抑制时长 <span class="required-mark">*</span></label>
                            <input type="number" id="suppressionDuration" min="1" required placeholder="请输入抑制时长">
                        </div>
                        <div class="form-group">
                            <label for="durationUnit">时长单位 <span class="required-mark">*</span></label>
                            <select id="durationUnit" required>
                                <option value="minute">分钟</option>
                                <option value="hour">小时</option>
                                <option value="day">天</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="applicableObject">适用对象 <span class="required-mark">*</span></label>
                        <select id="applicableObject" required>
                            <option value="">请选择适用对象</option>
                            <option value="all">所有对象</option>
                            <option value="servers">服务器</option>
                            <option value="network">网络设备</option>
                            <option value="applications">应用系统</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="suppression-modal-cancel">取消</button>
                <button class="btn btn-accent" id="save-suppression-btn">保存</button>
            </div>
        </div>
    </div>
    <script src="js/common.js"></script>
    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有活动状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));

                // 添加当前活动状态
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab') + '-tab';
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 模态框控制函数
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 新增事件按钮
        document.getElementById('add-event-btn').addEventListener('click', () => {
            openModal('event-modal');
        });

        // 关闭事件模态框
        document.getElementById('event-modal-close').addEventListener('click', () => {
            closeModal('event-modal');
        });

        document.getElementById('event-modal-cancel').addEventListener('click', () => {
            closeModal('event-modal');
        });

        // 保存事件
        document.getElementById('save-event-btn').addEventListener('click', () => {
            // 获取表单数据
            const eventId = document.getElementById('eventId').value;
            const eventLevel = document.getElementById('eventLevel').value;
            const eventType = document.getElementById('eventType').value;
            const eventStatus = document.getElementById('eventStatus').value;

            // 简单验证
            if (!eventId || !eventLevel || !eventType || !eventStatus) {
                alert('请填写必填字段');
                return;
            }

            // 模拟保存到数据库
            alert('告警事件已保存到数据库');
            closeModal('event-modal');
            document.getElementById('event-form').reset();

            // 在实际应用中，这里会有AJAX请求将数据发送到后端
        });

        // 新增内容模板按钮
        document.getElementById('add-content-btn').addEventListener('click', () => {
            openModal('content-modal');
        });

        // 关闭内容模板模态框
        document.getElementById('content-modal-close').addEventListener('click', () => {
            closeModal('content-modal');
        });

        document.getElementById('content-modal-cancel').addEventListener('click', () => {
            closeModal('content-modal');
        });

        // 保存内容模板
        document.getElementById('save-content-btn').addEventListener('click', () => {
            // 获取表单数据
            const templateTitle = document.getElementById('templateTitle').value;
            const templateType = document.getElementById('templateType').value;
            const templateContent = document.getElementById('templateContent').value;

            // 简单验证
            if (!templateTitle || !templateType || !templateContent) {
                alert('请填写必填字段');
                return;
            }

            // 模拟保存到数据库
            alert('告警内容模板已保存到数据库');
            closeModal('content-modal');
            document.getElementById('content-form').reset();
        });

        // 新增聚合策略按钮
        document.getElementById('add-aggregation-btn').addEventListener('click', () => {
            openModal('aggregation-modal');
        });

        // 关闭聚合策略模态框
        document.getElementById('aggregation-modal-close').addEventListener('click', () => {
            closeModal('aggregation-modal');
        });

        document.getElementById('aggregation-modal-cancel').addEventListener('click', () => {
            closeModal('aggregation-modal');
        });

        // 保存聚合策略
        document.getElementById('save-aggregation-btn').addEventListener('click', () => {
            // 获取表单数据
            const strategyName = document.getElementById('strategyName').value;
            const aggregationField = document.getElementById('aggregationField').value;
            const aggregationCycle = document.getElementById('aggregationCycle').value;
            const cycleUnit = document.getElementById('cycleUnit').value;
            const aggregationThreshold = document.getElementById('aggregationThreshold').value;

            // 简单验证
            if (!strategyName || !aggregationField || !aggregationCycle || !cycleUnit || !aggregationThreshold) {
                alert('请填写必填字段');
                return;
            }

            // 模拟保存到数据库
            alert('告警聚合策略已保存到数据库');
            closeModal('aggregation-modal');
            document.getElementById('aggregation-form').reset();
        });

        // 新增抑制规则按钮
        document.getElementById('add-suppression-btn').addEventListener('click', () => {
            openModal('suppression-modal');
        });

        // 关闭抑制规则模态框
        document.getElementById('suppression-modal-close').addEventListener('click', () => {
            closeModal('suppression-modal');
        });

        document.getElementById('suppression-modal-cancel').addEventListener('click', () => {
            closeModal('suppression-modal');
        });

        // 保存抑制规则
        document.getElementById('save-suppression-btn').addEventListener('click', () => {
            // 获取表单数据
            const ruleName = document.getElementById('ruleName').value;
            const suppressionCondition = document.getElementById('suppressionCondition').value;
            const suppressionDuration = document.getElementById('suppressionDuration').value;
            const durationUnit = document.getElementById('durationUnit').value;
            const applicableObject = document.getElementById('applicableObject').value;

            // 简单验证
            if (!ruleName || !suppressionCondition || !suppressionDuration || !durationUnit || !applicableObject) {
                alert('请填写必填字段');
                return;
            }

            // 模拟保存到数据库
            alert('告警抑制规则已保存到数据库');
            closeModal('suppression-modal');
            document.getElementById('suppression-form').reset();
        });

        // 查看事件详情
        function viewEvent(eventId) {
            alert(`查看事件 ${eventId} 的详细信息`);
            // 在实际应用中，这里会打开详情模态框并加载数据
        }

        // 查看内容模板
        function viewContent(templateId) {
            alert(`查看模板 ${templateId} 的详细信息`);
        }

        // 编辑内容模板
        function editContent(templateId) {
            alert(`编辑模板 ${templateId}`);
            openModal('content-modal');
            // 在实际应用中，这里会加载模板数据到表单
        }

        // 查看聚合策略
        function viewAggregation(strategyName) {
            alert(`查看聚合策略 ${strategyName} 的详细信息`);
        }

        // 查看抑制规则
        function viewSuppression(ruleName) {
            alert(`查看抑制规则 ${ruleName} 的详细信息`);
        }
    </script>
</body>
</html>