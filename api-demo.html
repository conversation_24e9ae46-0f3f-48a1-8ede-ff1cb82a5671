<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API交互演示 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
    <h1 style="text-align: center; margin-bottom: 30px; color: var(--text-primary);">
      <i class="fas fa-rocket"></i> DevOps平台 API交互演示
    </h1>
    
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
      <h2 style="margin: 0 0 10px 0;"><i class="fas fa-info-circle"></i> 功能说明</h2>
      <p style="margin: 0; line-height: 1.6;">
        本演示展示了DevOps平台所有页面的按钮交互功能。每个按钮都会发送真实的HTTP请求到 
        <code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">http://127.0.0.1/api/v1</code>
        ，您可以在浏览器开发者工具的Network标签中查看所有网络请求。
      </p>
    </div>

    <div class="row">
      <!-- DevOps Dashboard -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">
            <i class="fas fa-tachometer-alt"></i> DevOps 总览
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testNotifications(this)">
              <i class="fas fa-bell"></i> 测试通知加载
            </button>
            <button class="btn" onclick="testUserProfile(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-user"></i> 测试用户信息
            </button>
            <button class="btn" onclick="testDashboardData(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-chart-line"></i> 测试仪表板数据
            </button>
            <button class="btn" onclick="testExportData(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-download"></i> 测试数据导出
            </button>
          </div>
        </div>
      </div>

      <!-- Deployment Management -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--success-color); margin-bottom: 15px;">
            <i class="fas fa-cube"></i> 容器部署
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testCreateDeployment(this)">
              <i class="fas fa-plus"></i> 测试创建部署
            </button>
            <button class="btn" onclick="testScaleApplication(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-expand-arrows-alt"></i> 测试应用扩缩容
            </button>
            <button class="btn" onclick="testRestartApplication(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-redo"></i> 测试应用重启
            </button>
            <button class="btn" onclick="testStopApplication(this)" style="border: 1px solid var(--danger-color); color: var(--danger-color);">
              <i class="fas fa-stop"></i> 测试应用停止
            </button>
          </div>
        </div>
      </div>

      <!-- Monitoring Center -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--warning-color); margin-bottom: 15px;">
            <i class="fas fa-chart-line"></i> 监控中心
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testMonitoringData(this)">
              <i class="fas fa-sync-alt"></i> 测试监控数据刷新
            </button>
            <button class="btn" onclick="testAlerts(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-exclamation-triangle"></i> 测试告警管理
            </button>
            <button class="btn" onclick="testResolveAlert(this)" style="border: 1px solid var(--success-color); color: var(--success-color);">
              <i class="fas fa-check"></i> 测试解决告警
            </button>
            <button class="btn" onclick="testMuteAlert(this)" style="border: 1px solid var(--text-tertiary); color: var(--text-tertiary);">
              <i class="fas fa-bell-slash"></i> 测试静音告警
            </button>
          </div>
        </div>
      </div>

      <!-- Pipeline Management -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--info-color); margin-bottom: 15px;">
            <i class="fas fa-code-branch"></i> CI/CD 流水线
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testCreatePipeline(this)">
              <i class="fas fa-plus"></i> 测试创建流水线
            </button>
            <button class="btn" onclick="testRunPipeline(this)" style="border: 1px solid var(--success-color); color: var(--success-color);">
              <i class="fas fa-play"></i> 测试运行流水线
            </button>
            <button class="btn" onclick="testStopPipeline(this)" style="border: 1px solid var(--danger-color); color: var(--danger-color);">
              <i class="fas fa-stop"></i> 测试停止流水线
            </button>
            <button class="btn" onclick="testSearchPipelines(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-search"></i> 测试搜索流水线
            </button>
          </div>
        </div>
      </div>

      <!-- Service Topology -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--secondary-color); margin-bottom: 15px;">
            <i class="fas fa-project-diagram"></i> 服务拓扑
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testRefreshTopology(this)">
              <i class="fas fa-sync-alt"></i> 测试刷新拓扑
            </button>
            <button class="btn" onclick="testToggleLayout(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-expand-arrows-alt"></i> 测试切换布局
            </button>
            <button class="btn" onclick="testServiceLogs(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-file-alt"></i> 测试查看日志
            </button>
            <button class="btn" onclick="testFilterServices(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-filter"></i> 测试服务过滤
            </button>
          </div>
        </div>
      </div>

      <!-- API Logs -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--text-primary); margin-bottom: 15px;">
            <i class="fas fa-terminal"></i> API 请求日志
          </h3>
          <div style="background: #f8f9fa; border-radius: 4px; padding: 15px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;" id="apiLogs">
            <div style="color: var(--text-tertiary);">等待API请求...</div>
          </div>
          <button class="btn" onclick="clearLogs()" style="margin-top: 10px; border: 1px solid var(--border-color); width: 100%;">
            <i class="fas fa-trash"></i> 清空日志
          </button>
        </div>
      </div>
    </div>

    <div style="text-align: center; margin-top: 30px; padding: 20px; background: var(--bg-color); border-radius: 8px;">
      <h3 style="color: var(--text-primary); margin-bottom: 10px;">
        <i class="fas fa-tools"></i> 开发者工具提示
      </h3>
      <p style="color: var(--text-secondary); margin: 0;">
        按 <kbd style="background: #e9ecef; padding: 2px 6px; border-radius: 3px;">F12</kbd> 打开开发者工具，
        切换到 <strong>Network</strong> 标签页，然后点击上方按钮查看真实的API请求。
      </p>
    </div>
  </div>

  <script src="js/api-client.js"></script>
  <script>
    // 重写console.log以显示在页面上
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      originalConsoleLog.apply(console, args);
      
      const logsContainer = document.getElementById('apiLogs');
      const logEntry = document.createElement('div');
      logEntry.style.marginBottom = '5px';
      logEntry.style.color = args[0].includes('🚀') ? '#007bff' : 
                            args[0].includes('✅') ? '#28a745' : 
                            args[0].includes('❌') ? '#dc3545' : '#6c757d';
      logEntry.textContent = args.join(' ');
      
      logsContainer.appendChild(logEntry);
      logsContainer.scrollTop = logsContainer.scrollHeight;
    };

    // 测试函数
    async function testNotifications(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/notifications');
      });
    }

    async function testUserProfile(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/user/profile');
      });
    }

    async function testDashboardData(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/dashboard/stats');
      });
    }

    async function testExportData(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/dashboard/deployment-trend');
      });
    }

    async function testCreateDeployment(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/deployments/applications', {
          name: 'demo-service',
          environment: 'prod',
          image: 'demo:latest'
        });
      });
    }

    async function testScaleApplication(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.put('/deployments/applications/demo-service/scale', {
          instances: 3
        });
      });
    }

    async function testRestartApplication(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/deployments/applications/demo-service/restart');
      });
    }

    async function testStopApplication(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/deployments/applications/demo-service/stop');
      });
    }

    async function testMonitoringData(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/monitoring/metrics', { timeRange: '1h' });
      });
    }

    async function testAlerts(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/monitoring/alerts');
      });
    }

    async function testResolveAlert(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/monitoring/alerts/alert-123/resolve');
      });
    }

    async function testMuteAlert(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/monitoring/alerts/alert-123/mute');
      });
    }

    async function testCreatePipeline(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/pipelines', {
          name: 'demo-pipeline',
          repository: 'https://github.com/demo/repo.git'
        });
      });
    }

    async function testRunPipeline(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/pipelines/pipeline-123/run');
      });
    }

    async function testStopPipeline(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/pipelines/pipeline-123/stop');
      });
    }

    async function testSearchPipelines(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/pipelines', { search: 'demo', status: 'running' });
      });
    }

    async function testRefreshTopology(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/topology/services');
      });
    }

    async function testToggleLayout(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.post('/user/preferences', { topologyLayout: 'circular' });
      });
    }

    async function testServiceLogs(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/services/demo-service/logs');
      });
    }

    async function testFilterServices(button) {
      await apiClient.callWithLoading(button, async () => {
        return await apiClient.get('/topology/services', { status: 'healthy' });
      });
    }

    function clearLogs() {
      document.getElementById('apiLogs').innerHTML = '<div style="color: var(--text-tertiary);">日志已清空...</div>';
    }
  </script>
</body>
</html>
