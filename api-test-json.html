<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JSON API测试 - DevOps平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
    <h1 style="text-align: center; margin-bottom: 30px; color: var(--text-primary);">
      <i class="fas fa-code"></i> JSON API格式测试
    </h1>
    
    <div style="background: linear-gradient(135deg, #10B981 0%, #059669 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
      <h2 style="margin: 0 0 10px 0;"><i class="fas fa-check-circle"></i> 更新完成</h2>
      <p style="margin: 0; line-height: 1.6;">
        所有页面的API交互已更新为标准JSON格式：<br>
        <code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">{"code": 200, "message": "成功", "data": {...}}</code><br>
        API基础路径已更改为：<code style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">http://127.0.0.1/api</code>
      </p>
    </div>

    <div class="row">
      <!-- 通知测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">
            <i class="fas fa-bell"></i> 通知系统测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testNotifications(this)">
              <i class="fas fa-bell"></i> 测试获取通知 (/notifications)
            </button>
            <button class="btn" onclick="testMarkRead(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-check"></i> 测试标记已读 (/mark_notification_read)
            </button>
          </div>
        </div>
      </div>

      <!-- 用户管理测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--success-color); margin-bottom: 15px;">
            <i class="fas fa-user"></i> 用户管理测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testUserProfile(this)">
              <i class="fas fa-user"></i> 测试用户信息 (/user_profile)
            </button>
            <button class="btn" onclick="testUserPreferences(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-cog"></i> 测试用户偏好 (/user_preferences)
            </button>
          </div>
        </div>
      </div>

      <!-- 部署管理测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--warning-color); margin-bottom: 15px;">
            <i class="fas fa-cube"></i> 部署管理测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testDeploymentApps(this)">
              <i class="fas fa-list"></i> 测试应用列表 (/deployment_applications)
            </button>
            <button class="btn" onclick="testCreateDeployment(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-plus"></i> 测试创建部署 (/create_deployment)
            </button>
            <button class="btn" onclick="testScaleApp(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-expand-arrows-alt"></i> 测试扩缩容 (/scale_application)
            </button>
          </div>
        </div>
      </div>

      <!-- 监控中心测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--danger-color); margin-bottom: 15px;">
            <i class="fas fa-chart-line"></i> 监控中心测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testMonitoringMetrics(this)">
              <i class="fas fa-chart-bar"></i> 测试监控指标 (/monitoring_metrics)
            </button>
            <button class="btn" onclick="testAlerts(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-exclamation-triangle"></i> 测试告警列表 (/monitoring_alerts)
            </button>
            <button class="btn" onclick="testResolveAlert(this)" style="border: 1px solid var(--success-color); color: var(--success-color);">
              <i class="fas fa-check"></i> 测试解决告警 (/resolve_alert)
            </button>
          </div>
        </div>
      </div>

      <!-- 流水线测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--info-color); margin-bottom: 15px;">
            <i class="fas fa-code-branch"></i> 流水线测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testPipelines(this)">
              <i class="fas fa-list"></i> 测试流水线列表 (/pipelines)
            </button>
            <button class="btn" onclick="testCreatePipeline(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-plus"></i> 测试创建流水线 (/create_pipeline)
            </button>
            <button class="btn" onclick="testRunPipeline(this)" style="border: 1px solid var(--success-color); color: var(--success-color);">
              <i class="fas fa-play"></i> 测试运行流水线 (/run_pipeline)
            </button>
          </div>
        </div>
      </div>

      <!-- 服务拓扑测试 -->
      <div class="col col-6">
        <div class="card" style="margin-bottom: 20px;">
          <h3 style="color: var(--secondary-color); margin-bottom: 15px;">
            <i class="fas fa-project-diagram"></i> 服务拓扑测试
          </h3>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <button class="btn btn-primary" onclick="testTopologyServices(this)">
              <i class="fas fa-sitemap"></i> 测试服务列表 (/topology_services)
            </button>
            <button class="btn" onclick="testServiceLogs(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-file-alt"></i> 测试服务日志 (/service_logs)
            </button>
            <button class="btn" onclick="testFilterServices(this)" style="border: 1px solid var(--border-color);">
              <i class="fas fa-filter"></i> 测试服务过滤 (/filter_services)
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- API响应显示区域 -->
    <div class="card" style="margin-top: 30px;">
      <h3 style="color: var(--text-primary); margin-bottom: 15px;">
        <i class="fas fa-terminal"></i> API响应结果
      </h3>
      <div style="background: #f8f9fa; border-radius: 4px; padding: 15px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;" id="apiResponse">
        <div style="color: var(--text-tertiary);">点击上方按钮测试API响应...</div>
      </div>
      <button class="btn" onclick="clearResponse()" style="margin-top: 10px; border: 1px solid var(--border-color); width: 100%;">
        <i class="fas fa-trash"></i> 清空响应
      </button>
    </div>

    <div style="text-align: center; margin-top: 30px; padding: 20px; background: var(--bg-color); border-radius: 8px;">
      <h3 style="color: var(--text-primary); margin-bottom: 10px;">
        <i class="fas fa-info-circle"></i> JSON格式说明
      </h3>
      <p style="color: var(--text-secondary); margin: 0;">
        所有API响应都采用统一的JSON格式：<br>
        <code style="background: #e9ecef; padding: 4px 8px; border-radius: 3px; color: #495057;">
          {"code": 200, "message": "成功", "data": {...}}
        </code><br>
        <small>code: 状态码 | message: 响应消息 | data: 业务数据</small>
      </p>
    </div>
  </div>

  <script src="js/api-client.js"></script>
  <script>
    // 显示API响应
    function showApiResponse(title, response) {
      const container = document.getElementById('apiResponse');
      const timestamp = new Date().toLocaleTimeString();
      const responseHtml = `
        <div style="margin-bottom: 15px; padding: 10px; border-left: 3px solid var(--primary-color); background: white;">
          <div style="font-weight: bold; color: var(--primary-color); margin-bottom: 5px;">
            [${timestamp}] ${title}
          </div>
          <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word;">${JSON.stringify(response, null, 2)}</pre>
        </div>
      `;
      container.innerHTML = responseHtml + container.innerHTML;
    }

    function clearResponse() {
      document.getElementById('apiResponse').innerHTML = '<div style="color: var(--text-tertiary);">响应已清空...</div>';
    }

    // 测试函数
    async function testNotifications(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/notifications');
        showApiResponse('获取通知列表', response);
        return response;
      });
    }

    async function testMarkRead(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/mark_notification_read', { notificationId: 'notif_001' });
        showApiResponse('标记通知已读', response);
        return response;
      });
    }

    async function testUserProfile(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/user_profile');
        showApiResponse('获取用户信息', response);
        return response;
      });
    }

    async function testUserPreferences(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/user_preferences', { topologyLayout: 'circular' });
        showApiResponse('保存用户偏好', response);
        return response;
      });
    }

    async function testDeploymentApps(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/deployment_applications', { env: 'prod' });
        showApiResponse('获取应用列表', response);
        return response;
      });
    }

    async function testCreateDeployment(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/create_deployment', {
          applicationName: 'test-service',
          environment: 'prod',
          image: 'test:latest'
        });
        showApiResponse('创建新部署', response);
        return response;
      });
    }

    async function testScaleApp(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/scale_application', {
          applicationId: 'user-service',
          targetInstances: 5
        });
        showApiResponse('应用扩缩容', response);
        return response;
      });
    }

    async function testMonitoringMetrics(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/monitoring_metrics', { timeRange: '1h' });
        showApiResponse('获取监控指标', response);
        return response;
      });
    }

    async function testAlerts(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/monitoring_alerts');
        showApiResponse('获取告警列表', response);
        return response;
      });
    }

    async function testResolveAlert(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/resolve_alert', { alertId: 'alert_001' });
        showApiResponse('解决告警', response);
        return response;
      });
    }

    async function testPipelines(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/pipelines');
        showApiResponse('获取流水线列表', response);
        return response;
      });
    }

    async function testCreatePipeline(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/create_pipeline', {
          pipelineName: 'test-pipeline',
          repository: 'https://github.com/test/repo.git'
        });
        showApiResponse('创建流水线', response);
        return response;
      });
    }

    async function testRunPipeline(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post('/run_pipeline', { pipelineId: 'pipeline_001' });
        showApiResponse('运行流水线', response);
        return response;
      });
    }

    async function testTopologyServices(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/topology_services');
        showApiResponse('获取服务拓扑', response);
        return response;
      });
    }

    async function testServiceLogs(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/service_logs', { serviceId: 'user-service' });
        showApiResponse('获取服务日志', response);
        return response;
      });
    }

    async function testFilterServices(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/filter_services', { status: 'healthy' });
        showApiResponse('过滤服务列表', response);
        return response;
      });
    }
  </script>
</body>
</html>
