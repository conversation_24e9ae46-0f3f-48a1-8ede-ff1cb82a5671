<!DOCTYPE html>
<html lang="zh-CN">
<head>
    
 <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营通报管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
   <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: var(--primary-color);
            --primary-light: rgba(22, 93, 255, 0.1);
            --secondary-color: #4080FF;
            --success-color: #00B42A;
            --warning-color: #FF7D00;
            --danger-color: #F53F3F;
            --text-primary: #1D2129;
            --text-secondary: #4E5969;
            --text-tertiary: #86909C;
            --border-color: #D9D9D9;
            --border-light: #E5E6EB;
            --bg-color: #F2F3F5;
            --bg-white: #FFFFFF;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
            --primary-color: #1890ff;
  --secondary-color: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #ff4d4f;
  --info-color: #1890ff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e8e8e8;
  --hover-color: #f0f0f0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #F7F8FA;
            color: var(--text-primary);
            line-height: 1.5;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            margin-bottom: 24px;
        }
        
        /* .page-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .page-title i {
            color: var(--primary-color);
            margin-right: 8px;
        } */
        
        .breadcrumb {
            display: flex;
            font-size: 14px;
            color: var(--text-tertiary);
        }
/*         
        .breadcrumb-item:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
        } */
        
        .breadcrumb-item:last-child {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .card {
            background-color: var(--bg-white);
            border-radius: 6px;
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .card:hover {
            box-shadow: var(--shadow-hover);
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
        }
        
        .tab {
            padding: 14px 24px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: var(--transition);
            color: var(--text-secondary);
        }
        
        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .tab:hover:not(.active) {
            background-color: var(--bg-color);
            color: var(--text-primary);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .search-section {
            padding: 20px;
            border-bottom: 1px solid var(--border-light);
        }
        
        .search-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        .search-fields {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .search-group {
            flex: 1;
            min-width: 200px;
        }
        
        .search-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .search-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: var(--transition);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px var(--primary-light);
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-separator {
            color: var(--text-tertiary);
            white-space: nowrap;
        }
        
        .search-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0E4BDB;
        }
        
        .btn-default {
            background-color: var(--bg-white);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .btn-default:hover {
            background-color: var(--bg-color);
            color: var(--text-primary);
        }
        
        .result-section {
            padding: 20px;
        }
        
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 768px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }
        
        .data-table th {
            background-color: var(--bg-color);
            font-weight: 500;
            font-size: 14px;
            color: var(--text-secondary);
            white-space: nowrap;
        }
        
        .data-table tr:hover {
            background-color: var(--primary-light);
        }
        
        .data-table td {
            font-size: 14px;
        }
        
        .action-btn {
            color: var(--primary-color);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 8px;
            border-radius: 2px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            background-color: var(--primary-light);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 20px;
        }
        
        .page-btn {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            background-color: var(--bg-white);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .page-btn:hover:not(.active) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .page-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .stats-charts {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            flex: 1;
            min-width: 300px;
            height: 300px;
            position: relative;
        }
        
        .chart-title {
            position: absolute;
            top: -30px;
            left: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        /* 详情模态框 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }
        
        .modal-backdrop.show {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background-color: var(--bg-white);
            border-radius: 6px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            transform: translateY(-20px);
            transition: var(--transition);
            margin: 20px auto;
        }
        
        .modal-backdrop.show .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-tertiary);
            transition: var(--transition);
        }
        
        .modal-close:hover {
            color: var(--danger-color);
        }
        
        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }
        
        .detail-item {
            margin-bottom: 16px;
            display: flex;
        }
        
        .detail-label {
            width: 120px;
            flex-shrink: 0;
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .detail-value {
            flex: 1;
        }
        
        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-light);
            display: flex;
            justify-content: flex-end;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .search-fields {
                flex-direction: column;
            }
            
            .search-actions {
                flex-wrap: wrap;
            }
            
            .btn {
                flex: 1;
                min-width: 120px;
                justify-content: center;
            }
            
            .stats-charts {
                flex-direction: column;
            }
            
            .chart-container {
                height: 250px;
            }
        }
    </style>

</head>
<body>
      <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
   <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="report_management.html">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin_management.html">运营通报管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;"  data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-generation.html">运营通报生成与审核</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
      <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

  
      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child " data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child " data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child active" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

     <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-bullhorn page-title-icon"></i>
      运营通报浏览查询统计
    </div>

   <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页 </a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营通报浏览查询统计</div>
    </div>
    <div class="container">
        <div class="card">
            <!-- 标签页导航 -->
            <div class="tabs">
                <div class="tab active" data-tab="browse-query">浏览情况查询</div>
                <div class="tab" data-tab="browse-stats">浏览情况统计</div>
            </div>
            
            <!-- 浏览情况查询 -->
            <div class="tab-content active" id="browse-query">
                <div class="search-section">
                    <div class="search-title">查询条件</div>
                    <div class="search-fields">
                        <div class="search-group">
                            <label class="search-label">运营通报标题</label>
                            <input type="text" class="search-input" id="query-title" placeholder="请输入运营通报标题">
                        </div>
                        <div class="search-group">
                            <label class="search-label">浏览人</label>
                            <input type="text" class="search-input" id="query-person" placeholder="请输入浏览人姓名或工号">
                        </div>
                        <div class="search-group date-range">
                            <div style="flex: 1;">
                                <label class="search-label">浏览时间范围</label>
                                <input type="date" class="search-input" id="query-start-date">
                            </div>
                            <div class="date-separator">至</div>
                            <div style="flex: 1;">
                                <label class="search-label" style="visibility: hidden;">结束时间</label>
                                <input type="date" class="search-input" id="query-end-date">
                            </div>
                        </div>
                        <div class="search-group">
                            <label class="search-label">浏览人机构</label>
                            <input type="text" class="search-input" id="query-org" placeholder="请输入浏览人机构">
                        </div>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-primary" id="query-search">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button class="btn btn-default" id="query-reset">
                            <i class="fas fa-sync-alt"></i> 重置
                        </button>
                        <button class="btn btn-default" id="query-export">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
                
                <div class="result-section">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>运营通报标题</th>
                                    <th>浏览时间</th>
                                    <th>浏览人工号</th>
                                    <th>浏览人机构</th>
                                    <th>浏览时长</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="browse-records-table">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <div class="page-btn">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="page-btn active">1</div>
                        <div class="page-btn">2</div>
                        <div class="page-btn">3</div>
                        <div class="page-btn">4</div>
                        <div class="page-btn">5</div>
                        <div class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 浏览情况统计 -->
            <div class="tab-content" id="browse-stats">
                <div class="search-section">
                    <div class="search-title">统计条件</div>
                    <div class="search-fields">
                        <div class="search-group">
                            <label class="search-label">运营通报标题</label>
                            <input type="text" class="search-input" id="stats-title" placeholder="请输入运营通报标题">
                        </div>
                        <div class="search-group date-range">
                            <div style="flex: 1;">
                                <label class="search-label">浏览时间范围</label>
                                <input type="date" class="search-input" id="stats-start-date">
                            </div>
                            <div class="date-separator">至</div>
                            <div style="flex: 1;">
                                <label class="search-label" style="visibility: hidden;">结束时间</label>
                                <input type="date" class="search-input" id="stats-end-date">
                            </div>
                        </div>
                        <div class="search-group">
                            <label class="search-label">浏览人机构</label>
                            <input type="text" class="search-input" id="stats-org" placeholder="请输入浏览人机构">
                        </div>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-primary" id="stats-search">
                            <i class="fas fa-search"></i> 查询统计
                        </button>
                        <button class="btn btn-default" id="stats-reset">
                            <i class="fas fa-sync-alt"></i> 重置
                        </button>
                        <button class="btn btn-default" id="stats-export">
                            <i class="fas fa-download"></i> 导出统计
                        </button>
                    </div>
                </div>
                
                <div class="result-section">
                    <div class="stats-charts">
                        <div class="chart-container">
                            <div class="chart-title">通报浏览量TOP5</div>
                            <canvas id="top5-chart"></canvas>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">各机构浏览量分布</div>
                            <canvas id="org-distribution-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>运营通报标题</th>
                                    <th>浏览次数</th>
                                    <th>浏览时间范围</th>
                                    <th>浏览人机构</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="browse-stats-table">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="pagination">
                        <div class="page-btn">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="page-btn active">1</div>
                        <div class="page-btn">2</div>
                        <div class="page-btn">3</div>
                        <div class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 浏览详情模态框 -->
    <div class="modal-backdrop" id="browse-detail-modal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-eye"></i> 浏览详情
                </div>
                <button class="modal-close" id="close-detail-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-item">
                    <div class="detail-label">浏览记录ID：</div>
                    <div class="detail-value" id="detail-record-id"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">运营通报标题：</div>
                    <div class="detail-value" id="detail-bulletin-title"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">通报实例ID：</div>
                    <div class="detail-value" id="detail-instance-id"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览时间：</div>
                    <div class="detail-value" id="detail-browse-time"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览人工号：</div>
                    <div class="detail-value" id="detail-browse-id"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览人姓名：</div>
                    <div class="detail-value" id="detail-browse-name"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览人机构：</div>
                    <div class="detail-value" id="detail-browse-org"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览时长：</div>
                    <div class="detail-value" id="detail-browse-duration"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="confirm-detail-modal">关闭</button>
            </div>
        </div>
    </div>
    
    <!-- 统计详情模态框 -->
    <div class="modal-backdrop" id="stats-detail-modal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-chart-bar"></i> 统计详情
                </div>
                <button class="modal-close" id="close-stats-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-item">
                    <div class="detail-label">运营通报标题：</div>
                    <div class="detail-value" id="stats-detail-title"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">通报实例ID：</div>
                    <div class="detail-value" id="stats-detail-instance-id"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">总浏览次数：</div>
                    <div class="detail-value" id="stats-detail-total"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">独立浏览人数：</div>
                    <div class="detail-value" id="stats-detail-users"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览时间范围：</div>
                    <div class="detail-value" id="stats-detail-time-range"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">主要浏览机构：</div>
                    <div class="detail-value" id="stats-detail-orgs"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">浏览趋势：</div>
                    <div class="detail-value">
                        <div style="height: 200px; margin-top: 8px;">
                            <canvas id="trend-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="confirm-stats-modal">关闭</button>
            </div>
        </div>
    </div>
     <script src="js/common.js"></script>
    <script>
        // API基础URL
        const API_BASE_URL = 'http://localhost:8000/api';
        
        // API请求函数
        async function apiRequest(endpoint, options = {}) {
          try {
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
              method: options.method || 'POST',
              headers: {
                'Content-Type': 'application/json',
                ...options.headers
              },
              body: options.body ? JSON.stringify(options.body) : undefined,
              ...options
            });
            
            const data = await response.json();
            console.log(`API调用成功: ${endpoint}`, data);
            return data;
          } catch (error) {
            console.error(`API调用失败: ${endpoint}`, error);
            throw error;
          }
        }
        
        // 页面加载时初始化API调用
        window.addEventListener('load', async () => {
          try {
            await apiRequest('/bulletin-browse-statistics/page-visit', {
              method: 'POST',
              body: {
                page: 'bulletin-browse-statistics-v4',
                timestamp: new Date().toISOString()
              }
            });
          } catch (error) {
            // 静默处理，不显示提示
          }
        });

        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', async () => {
                // 移除所有活动状态
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前活动状态
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
                
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/tab-switch', {
                        method: 'POST',
                        body: {
                            tab: tabId,
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                // 如果切换到统计标签，初始化图表
                if (tabId === 'browse-stats') {
                    initCharts();
                }
            });
        });
        
        // 模拟浏览记录数据
        const browseRecords = [
            {
                recordId: 'BR-202308001',
                bulletinTitle: '2023年8月商客业务发展通报',
                instanceId: 'BUL-INS-202308001',
                browseTime: '2023-08-01 09:15:30',
                browseId: 'EMP001',
                browseName: '张三',
                browseOrg: '商客业务部',
                duration: '3分20秒'
            },
            {
                recordId: 'BR-202308002',
                bulletinTitle: '2023年8月商客业务发展通报',
                instanceId: 'BUL-INS-202308001',
                browseTime: '2023-08-01 10:05:12',
                browseId: 'EMP002',
                browseName: '李四',
                browseOrg: '商客业务部',
                duration: '5分10秒'
            },
            {
                recordId: 'BR-202308003',
                bulletinTitle: '2023年8月商客业务发展通报',
                instanceId: 'BUL-INS-202308001',
                browseTime: '2023-08-01 14:20:45',
                browseId: 'EMP003',
                browseName: '王五',
                browseOrg: '支撑部',
                duration: '2分45秒'
            },
            {
                recordId: 'BR-202308004',
                bulletinTitle: '7月支撑响应效能分析通报',
                instanceId: 'BUL-INS-202308002',
                browseTime: '2023-08-01 11:30:22',
                browseId: 'EMP003',
                browseName: '王五',
                browseOrg: '支撑部',
                duration: '6分30秒'
            },
            {
                recordId: 'BR-202308005',
                bulletinTitle: '7月支撑响应效能分析通报',
                instanceId: 'BUL-INS-202308002',
                browseTime: '2023-08-01 15:40:18',
                browseId: 'EMP004',
                browseName: '赵六',
                browseOrg: '技术部',
                duration: '4分15秒'
            },
            {
                recordId: 'BR-202308006',
                bulletinTitle: '第二季度智能工具使用情况通报',
                instanceId: 'BUL-INS-202307045',
                browseTime: '2023-08-02 09:50:33',
                browseId: 'EMP005',
                browseName: '钱七',
                browseOrg: '技术部',
                duration: '8分20秒'
            },
            {
                recordId: 'BR-202308007',
                bulletinTitle: '第二季度智能工具使用情况通报',
                instanceId: 'BUL-INS-202307045',
                browseTime: '2023-08-02 13:15:10',
                browseId: 'EMP006',
                browseName: '孙八',
                browseOrg: '市场部',
                duration: '3分50秒'
            }
        ];
        
        // 模拟统计数据
        const statsData = [
            {
                bulletinTitle: '2023年8月商客业务发展通报',
                instanceId: 'BUL-INS-202308001',
                viewCount: 28,
                userCount: 15,
                timeRange: '2023-08-01 至 2023-08-05',
                orgs: '商客业务部、支撑部、市场部'
            },
            {
                bulletinTitle: '7月支撑响应效能分析通报',
                instanceId: 'BUL-INS-202308002',
                viewCount: 15,
                userCount: 9,
                timeRange: '2023-08-01 至 2023-08-05',
                orgs: '支撑部、技术部、商客业务部'
            },
            {
                bulletinTitle: '第二季度智能工具使用情况通报',
                instanceId: 'BUL-INS-202307045',
                viewCount: 42,
                userCount: 23,
                timeRange: '2023-07-05 至 2023-07-31',
                orgs: '技术部、商客业务部、市场部'
            },
            {
                bulletinTitle: '6月业务发展问题通报',
                instanceId: 'BUL-INS-202307032',
                viewCount: 18,
                userCount: 12,
                timeRange: '2023-07-01 至 2023-07-15',
                orgs: '商客业务部、市场部'
            }
        ];
        
        // 填充浏览记录表格
        function populateBrowseRecords(records) {
            const tableBody = document.getElementById('browse-records-table');
            tableBody.innerHTML = '';
            
            if (records.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
                    <td colspan="6" style="text-align: center; padding: 30px;">
                        <i class="fas fa-search" style="font-size: 24px; color: var(--text-tertiary); margin-bottom: 8px; display: block;"></i>
                        没有找到匹配的记录
                    </td>
                `;
                tableBody.appendChild(emptyRow);
                return;
            }
            
            records.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.bulletinTitle}</td>
                    <td>${record.browseTime}</td>
                    <td>${record.browseId}</td>
                    <td>${record.browseOrg}</td>
                    <td>${record.duration}</td>
                    <td>
                        <button class="action-btn" onclick="showBrowseDetail('${record.recordId}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        // 填充统计表格
        function populateStatsTable(stats) {
            const tableBody = document.getElementById('browse-stats-table');
            tableBody.innerHTML = '';
            
            if (stats.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
                    <td colspan="5" style="text-align: center; padding: 30px;">
                        <i class="fas fa-chart-bar" style="font-size: 24px; color: var(--text-tertiary); margin-bottom: 8px; display: block;"></i>
                        没有找到匹配的统计数据
                    </td>
                `;
                tableBody.appendChild(emptyRow);
                return;
            }
            
            stats.forEach(stat => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${stat.bulletinTitle}</td>
                    <td>${stat.viewCount}</td>
                    <td>${stat.timeRange}</td>
                    <td>${stat.orgs}</td>
                    <td>
                        <button class="action-btn" onclick="showStatsDetail('${stat.instanceId}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        // 初始化图表
        function initCharts() {
            // 通报浏览量TOP5图表
            const top5Ctx = document.getElementById('top5-chart').getContext('2d');
            if (window.top5Chart) {
                window.top5Chart.destroy();
            }
            
            // 按浏览量排序取前5
            const sortedStats = [...statsData].sort((a, b) => b.viewCount - a.viewCount).slice(0, 5);
            
            window.top5Chart = new Chart(top5Ctx, {
                type: 'bar',
                data: {
                    labels: sortedStats.map(item => {
                        // 标题过长时截断显示
                        return item.bulletinTitle.length > 15 
                            ? item.bulletinTitle.substring(0, 15) + '...' 
                            : item.bulletinTitle;
                    }),
                    datasets: [{
                        label: '浏览次数',
                        data: sortedStats.map(item => item.viewCount),
                        backgroundColor: 'rgba(22, 93, 255, 0.7)',
                        borderColor: 'rgba(22, 93, 255, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
            
            // 机构浏览量分布图表
            const orgCtx = document.getElementById('org-distribution-chart').getContext('2d');
            if (window.orgChart) {
                window.orgChart.destroy();
            }
            
            window.orgChart = new Chart(orgCtx, {
                type: 'doughnut',
                data: {
                    labels: ['商客业务部', '支撑部', '技术部', '市场部', '其他部门'],
                    datasets: [{
                        data: [45, 20, 18, 12, 5],
                        backgroundColor: [
                            'rgba(22, 93, 255, 0.7)',
                            'rgba(0, 180, 42, 0.7)',
                            'rgba(255, 125, 0, 0.7)',
                            'rgba(245, 63, 63, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    },
                    cutout: '60%'
                }
            });
        }
        
        // 初始化趋势图表（用于统计详情）
        function initTrendChart() {
            const trendCtx = document.getElementById('trend-chart').getContext('2d');
            if (window.trendChart) {
                window.trendChart.destroy();
            }
            
            // 生成过去7天的日期
            const dates = [];
            const today = new Date();
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(today.getDate() - i);
                dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
            }
            
            window.trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: '浏览次数',
                        data: [5, 8, 12, 6, 15, 10, 8],
                        fill: true,
                        backgroundColor: 'rgba(22, 93, 255, 0.1)',
                        borderColor: 'rgba(22, 93, 255, 1)',
                        tension: 0.3,
                        pointBackgroundColor: 'rgba(22, 93, 255, 1)',
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
        
        // 显示浏览详情
        async function showBrowseDetail(recordId) {
            // API调用
            try {
                await apiRequest('/bulletin-browse-statistics/show-browse-detail', {
                    method: 'POST',
                    body: {
                        recordId: recordId,
                        timestamp: new Date().toISOString()
                    }
                });
            } catch (error) {
                // 静默处理，不显示提示
            }
            
            const record = browseRecords.find(r => r.recordId === recordId);
            if (!record) return;
            
            document.getElementById('detail-record-id').textContent = record.recordId;
            document.getElementById('detail-bulletin-title').textContent = record.bulletinTitle;
            document.getElementById('detail-instance-id').textContent = record.instanceId;
            document.getElementById('detail-browse-time').textContent = record.browseTime;
            document.getElementById('detail-browse-id').textContent = record.browseId;
            document.getElementById('detail-browse-name').textContent = record.browseName;
            document.getElementById('detail-browse-org').textContent = record.browseOrg;
            document.getElementById('detail-browse-duration').textContent = record.duration;
            
            document.getElementById('browse-detail-modal').classList.add('show');
        }
        
        // 显示统计详情
        async function showStatsDetail(instanceId) {
            // API调用
            try {
                await apiRequest('/bulletin-browse-statistics/show-stats-detail', {
                    method: 'POST',
                    body: {
                        instanceId: instanceId,
                        timestamp: new Date().toISOString()
                    }
                });
            } catch (error) {
                // 静默处理，不显示提示
            }
            
            const stat = statsData.find(s => s.instanceId === instanceId);
            if (!stat) return;
            
            document.getElementById('stats-detail-title').textContent = stat.bulletinTitle;
            document.getElementById('stats-detail-instance-id').textContent = stat.instanceId;
            document.getElementById('stats-detail-total').textContent = stat.viewCount;
            document.getElementById('stats-detail-users').textContent = stat.userCount;
            document.getElementById('stats-detail-time-range').textContent = stat.timeRange;
            document.getElementById('stats-detail-orgs').textContent = stat.orgs;
            
            // 初始化趋势图表
            initTrendChart();
            
            document.getElementById('stats-detail-modal').classList.add('show');
        }
        
        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
        
        // 初始化事件监听
        function initEventListeners() {
            // 查询按钮
            document.getElementById('query-search').addEventListener('click', async () => {
                // 获取查询条件
                const title = document.getElementById('query-title').value.toLowerCase();
                const person = document.getElementById('query-person').value.toLowerCase();
                const startDate = document.getElementById('query-start-date').value;
                const endDate = document.getElementById('query-end-date').value;
                const org = document.getElementById('query-org').value.toLowerCase();
                
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/query-search', {
                        method: 'POST',
                        body: {
                            title: title,
                            person: person,
                            startDate: startDate,
                            endDate: endDate,
                            org: org,
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                // 筛选数据
                const filteredRecords = browseRecords.filter(record => {
                    const matchesTitle = !title || record.bulletinTitle.toLowerCase().includes(title);
                    const matchesPerson = !person || 
                                        record.browseName.toLowerCase().includes(person) || 
                                        record.browseId.toLowerCase().includes(person);
                    const matchesOrg = !org || record.browseOrg.toLowerCase().includes(org);
                    
                    // 处理日期筛选
                    let matchesDate = true;
                    if (startDate || endDate) {
                        const recordDate = new Date(record.browseTime).toISOString().split('T')[0];
                        if (startDate && recordDate < startDate) matchesDate = false;
                        if (endDate && recordDate > endDate) matchesDate = false;
                    }
                    
                    return matchesTitle && matchesPerson && matchesOrg && matchesDate;
                });
                
                // 填充表格
                populateBrowseRecords(filteredRecords);
            });
            
            // 重置按钮
            document.getElementById('query-reset').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/query-reset', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                document.getElementById('query-title').value = '';
                document.getElementById('query-person').value = '';
                document.getElementById('query-start-date').value = '';
                document.getElementById('query-end-date').value = '';
                document.getElementById('query-org').value = '';
            });
            
            // 导出按钮
            document.getElementById('query-export').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/query-export', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                alert('浏览记录导出成功，已生成Excel文件');
            });
            
            // 统计查询按钮
            document.getElementById('stats-search').addEventListener('click', async () => {
                // 获取统计条件
                const title = document.getElementById('stats-title').value.toLowerCase();
                const startDate = document.getElementById('stats-start-date').value;
                const endDate = document.getElementById('stats-end-date').value;
                const org = document.getElementById('stats-org').value.toLowerCase();
                
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/stats-search', {
                        method: 'POST',
                        body: {
                            title: title,
                            startDate: startDate,
                            endDate: endDate,
                            org: org,
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                // 筛选数据
                const filteredStats = statsData.filter(stat => {
                    const matchesTitle = !title || stat.bulletinTitle.toLowerCase().includes(title);
                    const matchesOrg = !org || stat.orgs.toLowerCase().includes(org);
                    
                    // 这里简化处理日期筛选，实际应用中需要更复杂的逻辑
                    let matchesDate = true;
                    if (startDate || endDate) {
                        // 这里只是简单判断，实际应用中需要解析时间范围
                        matchesDate = true;
                    }
                    
                    return matchesTitle && matchesOrg && matchesDate;
                });
                
                // 填充表格和图表
                populateStatsTable(filteredStats);
                
                // 更新图表
                if (window.top5Chart) {
                    initCharts(filteredStats);
                }
            });
            
            // 统计重置按钮
            document.getElementById('stats-reset').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/stats-reset', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                document.getElementById('stats-title').value = '';
                document.getElementById('stats-start-date').value = '';
                document.getElementById('stats-end-date').value = '';
                document.getElementById('stats-org').value = '';
            });
            
            // 统计导出按钮
            document.getElementById('stats-export').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/stats-export', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                alert('统计数据导出成功，已生成Excel文件和图表');
            });
            
            // 关闭详情模态框
            document.getElementById('close-detail-modal').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/close-detail-modal', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                closeModal('browse-detail-modal');
            });
            
            document.getElementById('confirm-detail-modal').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/confirm-detail-modal', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                closeModal('browse-detail-modal');
            });
            
            // 关闭统计详情模态框
            document.getElementById('close-stats-modal').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/close-stats-modal', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                closeModal('stats-detail-modal');
            });
            
            document.getElementById('confirm-stats-modal').addEventListener('click', async () => {
                // API调用
                try {
                    await apiRequest('/bulletin-browse-statistics/confirm-stats-modal', {
                        method: 'POST',
                        body: {
                            timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    // 静默处理，不显示提示
                }
                
                closeModal('stats-detail-modal');
            });
            
            // 点击模态框外部关闭
            document.getElementById('browse-detail-modal').addEventListener('click', (e) => {
                if (e.target === document.getElementById('browse-detail-modal')) {
                    closeModal('browse-detail-modal');
                }
            });
            
            document.getElementById('stats-detail-modal').addEventListener('click', (e) => {
                if (e.target === document.getElementById('stats-detail-modal')) {
                    closeModal('stats-detail-modal');
                }
            });
            
            // 分页按钮点击事件
            document.querySelectorAll('.page-btn').forEach(btn => {
                btn.addEventListener('click', async () => {
                    if (!btn.classList.contains('active')) {
                        // API调用
                        try {
                            const pageText = btn.textContent.trim();
                            let page = pageText;
                            
                            // 处理特殊按钮（上一页、下一页）
                            if (btn.querySelector('.fa-chevron-left')) {
                                page = 'prev';
                            } else if (btn.querySelector('.fa-chevron-right')) {
                                page = 'next';
                            }
                            
                            await apiRequest('/bulletin-browse-statistics/change-page', {
                                method: 'POST',
                                body: {
                                    page: page,
                                    timestamp: new Date().toISOString()
                                }
                            });
                        } catch (error) {
                            // 静默处理，不显示提示
                        }
                        
                        document.querySelectorAll('.page-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        // 实际应用中这里应该加载对应页的数据
                    }
                });
            });
        }
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', () => {
            // 初始填充数据
            populateBrowseRecords(browseRecords);
            populateStatsTable(statsData);
            
            // 初始化事件监听
            initEventListeners();
            
            // 添加输入框变化监听
            const searchInputs = document.querySelectorAll('.search-input');
            searchInputs.forEach(input => {
                input.addEventListener('input', async (e) => {
                    // API调用
                    try {
                        await apiRequest('/bulletin-browse-statistics/input-change', {
                            method: 'POST',
                            body: {
                                field: e.target.id,
                                value: e.target.value,
                                timestamp: new Date().toISOString()
                            }
                        });
                    } catch (error) {
                        // 静默处理，不显示提示
                    }
                });
            });
        });
    </script>
</body>
</html>
    