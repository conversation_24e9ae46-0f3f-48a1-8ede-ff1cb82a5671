<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 画布管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      /* 通知提示样式 */
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      .notification.success {
        background-color: #52c41a;
      }
      .notification.error {
        background-color: #ff4d4f;
      }
      .notification.info {
        background-color: #1890ff;
      }
      .notification.warning {
        background-color: #faad14;
      }

      /* 右键菜单样式 */
      .context-menu {
        position: absolute;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        padding: 8px 0;
        z-index: 1000;
        display: none;
      }

      .context-menu-item {
        padding: 6px 16px;
        cursor: pointer;
        font-size: 14px;
        white-space: nowrap;
      }

      .context-menu-item:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }

      .context-menu-separator {
        height: 1px;
        background-color: var(--border-color);
        margin: 4px 0;
      }

      /* 二级菜单样式 */
      .submenu {
        position: absolute;
        left: 100%;
        top: 0;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        padding: 8px 0;
        display: none;
        z-index: 1001;
      }

      .context-menu-item.has-submenu {
        position: relative;
      }

      .context-menu-item.has-submenu:hover .submenu {
        display: block;
      }

      .submenu-item {
        padding: 6px 16px;
        cursor: pointer;
        font-size: 14px;
        white-space: nowrap;
      }

      .submenu-item:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }
      /* 画布管理页面特有样式 */
      .component-control-btn {
        background: none;
        border: none;
        cursor: pointer;
        color: var(--text-secondary);
        transition: color 0.3s;
      }
      .component-control-btn:hover {
        color: var(--primary-color);
      }
      .canvas-container {
        display: flex;
        height: calc(100vh - 150px);
        margin-top: 20px;
      }

      /* 左侧组件库面板 */
      .components-panel {
        width: 260px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 16px;
        margin-right: 16px;
        overflow-y: auto;
      }

      .panel-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .search-container {
        position: relative;
        margin-bottom: 16px;
        display: none;
      }

      .search-container.active {
        display: block;
      }

      .search-input {
        width: 100%;
        padding: 8px 12px 8px 36px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
      }

      .grid-settings-panel {
        position: absolute;
        bottom: 80px;
        right: 20px;
        width: 260px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 16px;
        z-index: 20;
        display: none;
      }

      .grid-settings-panel.active {
        display: block;
      }

      .settings-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
      }

      .settings-group {
        margin-bottom: 16px;
      }

      .settings-label {
        font-size: 12px;
        color: var(--text-tertiary);
        margin-bottom: 8px;
        display: block;
      }

      .guidelines {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 3;
      }

      .guideline-h {
        position: absolute;
        width: 100%;
        height: 1px;
        background-color: rgba(255, 0, 0, 0.5);
        transform: translateY(-50%);
        transition: all 0.3s ease;
        z-index: 10;
      }

      .guideline-h:hover {
        background-color: rgba(255, 0, 0, 0.8);
        height: 2px;
      }

      .guideline-v {
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: rgba(0, 0, 255, 0.5);
        transform: translateX(-50%);
        transition: all 0.3s ease;
        z-index: 10;
      }

      .guideline-v:hover {
        background-color: rgba(0, 0, 255, 0.8);
        width: 2px;
      }

      .component-category {
        margin-bottom: 16px;
      }

      .category-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .component-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
      }

      .component-item {
        padding: 8px;
        background-color: var(--hover-color);
        border-radius: 4px;
        text-align: center;
        cursor: move;
        transition: all 0.3s;
      }

      .component-item:hover {
        background-color: rgba(24, 144, 255, 0.1);
        transform: scale(1.05);
      }

      .component-icon {
        font-size: 24px;
        margin-bottom: 4px;
        color: var(--primary-color);
      }

      .component-name {
        font-size: 12px;
      }

      /* 中央画布区域 */
      .canvas-area {
        flex-grow: 1;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .canvas-grid {
        width: 100%;
        height: 100%;
        background-image: linear-gradient(#e8e8e8 1px, transparent 1px), linear-gradient(90deg, #e8e8e8 1px, transparent 1px);
        background-size: 20px 20px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .canvas-content {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
      }

      .canvas-controls {
        position: absolute;
        bottom: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .control-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: white;
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
      }

      .control-btn:hover {
        background-color: var(--primary-color);
        color: white;
      }

      /* 右侧属性面板 */
      .properties-panel {
        width: 280px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-left: 16px;
        display: flex;
        flex-direction: column;
      }

      .property-tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
      }

      .property-tab {
        padding: 12px 16px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
      }

      .property-tab.active {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
      }

      .property-content {
        padding: 16px;
        flex-grow: 1;
        overflow-y: auto;
      }

      .property-group {
        margin-bottom: 16px;
      }

      .property-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: var(--text-secondary);
      }

      .property-item {
        margin-bottom: 12px;
      }

      .property-label {
        font-size: 12px;
        color: var(--text-tertiary);
        margin-bottom: 4px;
      }

      /* 底部状态栏 */
      .status-bar {
        margin-top: 16px;
        padding: 12px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .version-timeline {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .version-item {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: var(--border-color);
        cursor: pointer;
        transition: all 0.3s;
      }

      .version-item.active {
        background-color: var(--primary-color);
        transform: scale(1.2);
      }

      .version-line {
        width: 30px;
        height: 2px;
        background-color: var(--border-color);
      }

      .save-status {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .save-progress {
        width: 20px;
        height: 20px;
        border: 2px solid var(--primary-color);
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .theme-button {
        padding: 8px 12px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
      }

      .theme-button:hover {
        background-color: #1e88e5;
      }

      .theme-dropdown {
        display: none;
        position: absolute;
        right: 20px;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 10px;
        z-index: 100;
      }

      .theme-dropdown.active {
        display: block;
      }

      .theme-option {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        cursor: pointer;
        border: 2px solid transparent;
        margin: 5px;
        display: inline-block;
      }

      .theme-option.active {
        border-color: var(--primary-color);
      }
    </style>
  </head>
  <body>
    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu">
      <div class="context-menu-item" data-action="cut">剪切(T)</div>
      <div class="context-menu-item" data-action="copy">复制(C)</div>
      <div class="context-menu-item" data-action="paste">粘贴(P)</div>
      <div class="context-menu-separator"></div>
      <div class="context-menu-item" data-action="hide">设为隐藏</div>
      <div class="context-menu-item has-submenu">
        层级(O)
        <div class="submenu" id="levelSubmenu">
          <div class="submenu-item" data-action="bringForward">上移一层</div>
          <div class="submenu-item" data-action="sendBackward">下移一层</div>
          <div class="submenu-item" data-action="bringToFront">置于顶层</div>
          <div class="submenu-item" data-action="sendToBack">置于底层</div>
        </div>
      </div>
      <div class="context-menu-item" data-action="editText">编辑文本</div>
      <div class="context-menu-item" data-action="fillRandomData">填充乱数数据</div>
      <div class="context-menu-item" data-action="selectShape">选择形状</div>
      <div class="context-menu-item" data-action="transformShape">变换形状</div>
      <div class="context-menu-item" data-action="interactiveStyle">交互样式...</div>
      <div class="context-menu-item" data-action="disable">禁用</div>
      <div class="context-menu-item" data-action="select">选中</div>
      <div class="context-menu-item" data-action="options">选项组...</div>
      <div class="context-menu-item" data-action="toolTips">工具提示...</div>
      <div class="context-menu-separator"></div>
      <div class="context-menu-item" data-action="group">组合(G)</div>
      <div class="context-menu-item" data-action="lock">锁定(K)</div>
    </div>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child  active" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-project-diagram page-title-icon"></i>
        运营视图 - 画布管理 - 画布
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item"><a href="canvas_management.html" style="text-decoration: none; color: inherit">画布管理</a></div>
        <div class="breadcrumb-item active">画布</div>
      </div>

      <!-- 顶部操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
        <div style="display: flex">
          <!-- <button class="btn btn-primary" id="createCanvasBtn" style="margin-right: 12px">
            <i class="fas fa-plus"></i>
            新建画布
          </button> -->
          <button class="btn btn-primary" id="saveBtn" style="margin-right: 12px">
            <i class="fas fa-save"></i>
            保存
          </button>
          <button class="btn btn-primary" id="deleteBtn" style="margin-right: 12px">
            <i class="fas fa-trash-alt"></i>
            删除
          </button>
          <button class="btn btn-primary" id="copyBtn" style="margin-right: 12px">
            <i class="fas fa-copy"></i>
            复制
          </button>
          <button class="btn btn-primary" style="margin-right: 12px">
            <i class="fas fa-download"></i>
            导出
          </button>
        </div>
        <div style="position: relative">
          <button class="theme-button" id="themeButton">主题选择</button>
          <div class="theme-dropdown" id="themeDropdown">
            <div class="theme-option active" style="background-color: #ffffff" title="默认主题"></div>
            <div class="theme-option" style="background-color: #1e293b" title="深色主题"></div>
            <div class="theme-option" style="background-color: #f8fafc" title="浅色主题"></div>
            <div class="theme-option" style="background-color: #f0fdf4" title="绿色主题"></div>
          </div>
        </div>

        <script>
          // 主题选择按钮交互
          document.getElementById('themeButton').addEventListener('click', function () {
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('active');
          });

          // 点击页面其他地方关闭下拉菜单
          document.addEventListener('click', function (event) {
            const button = document.getElementById('themeButton');
            const dropdown = document.getElementById('themeDropdown');
            if (!button.contains(event.target) && !dropdown.contains(event.target)) {
              dropdown.classList.remove('active');
            }
          });

          // 主题选择功能
          document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function () {
              // 移除所有active类
              document.querySelectorAll('.theme-option').forEach(opt => {
                opt.classList.remove('active');
              });
              // 给当前选中项添加active类
              this.classList.add('active');
              // 这里可以添加实际切换主题的代码
              // 关闭下拉菜单
              document.getElementById('themeDropdown').classList.remove('active');
            });
          });

          // 保存按钮点击事件
          document.getElementById('saveBtn').addEventListener('click', function () {
            alert('保存成功！');
            setTimeout(function () {
              window.location.href = 'theme_management.html';
            }, 1000);
          });

          // 删除按钮点击事件
          document.getElementById('deleteBtn').addEventListener('click', function () {
            alert('删除成功！');
            setTimeout(function () {
              window.location.href = 'theme_management.html';
            }, 1000);
          });

          // 复制按钮点击事件
          document.getElementById('copyBtn').addEventListener('click', function () {
            alert('复制成功！');
            setTimeout(function () {
              window.location.href = 'theme_management.html';
            }, 1000);
          });
        </script>
      </div>

      <!-- 画布容器 -->
      <div class="canvas-container">
        <!-- 左侧组件库面板 -->
        <div class="components-panel">
          <div class="panel-title">
            组件库
            <div style="display: flex; gap: 8px">
              <button class="control-btn" id="searchToggleBtn" style="width: 28px; height: 28px; font-size: 12px">
                <i class="fas fa-search"></i>
              </button>
              <button class="control-btn" style="width: 28px; height: 28px; font-size: 12px">
                <i class="fas fa-sliders-h"></i>
              </button>
            </div>
          </div>

          <div class="search-container" id="searchContainer">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" id="componentSearchInput" placeholder="搜索组件..." />
          </div>

          <!-- 3D组件 -->
          <div class="component-category">
            <div class="category-title">
              3D组件
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="component-list">
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-globe-asia"></i></div>
                <div class="component-name">3D中国地图组件</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-map-marked-alt"></i></div>
                <div class="component-name">3D省分地图组件</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-map-signs"></i></div>
                <div class="component-name">3D区域地图组件</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-map-marker-alt"></i></div>
                <div class="component-name">3D地市组件</div>
              </div>
            </div>
          </div>

          <!-- 图表组件 -->
          <div class="component-category">
            <div class="category-title">
              图表组件
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="component-list">
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-bar"></i></div>
                <div class="component-name">柱状图</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-line"></i></div>
                <div class="component-name">折线图</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-pie"></i></div>
                <div class="component-name">饼图</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-area"></i></div>
                <div class="component-name">面积图</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-scatter"></i></div>
                <div class="component-name">散点图</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-chart-polar-area"></i></div>
                <div class="component-name">雷达图</div>
              </div>
            </div>
          </div>

          <!-- 信息组件 -->
          <div class="component-category">
            <div class="category-title">
              信息组件
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="component-list">
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-font"></i></div>
                <div class="component-name">标题</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-align-left"></i></div>
                <div class="component-name">文本</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-list"></i></div>
                <div class="component-name">列表</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-table"></i></div>
                <div class="component-name">表格</div>
              </div>
            </div>
          </div>

          <!-- 边框组件 -->
          <div class="component-category">
            <div class="category-title">
              边框组件
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="component-list">
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-border"></i></div>
                <div class="component-name">直角边框</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-border-radius"></i></div>
                <div class="component-name">圆角边框</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-border-style"></i></div>
                <div class="component-name">虚线边框</div>
              </div>
            </div>
          </div>

          <!-- 装饰组件 -->
          <div class="component-category">
            <div class="category-title">
              装饰组件
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="component-list">
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-square"></i></div>
                <div class="component-name">矩形</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-circle"></i></div>
                <div class="component-name">圆形</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-image"></i></div>
                <div class="component-name">图片</div>
              </div>
              <div class="component-item" draggable="true">
                <div class="component-icon"><i class="fas fa-border-style"></i></div>
                <div class="component-name">分割线</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中央画布区域 -->
        <div class="canvas-area">
          <div class="canvas-grid"></div>
          <div class="canvas-content" id="canvasContent">
            <!-- 这里将放置拖入的组件 -->
            <div style="position: absolute; top: 100px; left: 100px; width: 300px; height: 200px; border: 1px dashed var(--primary-color); display: flex; align-items: center; justify-content: center">
              <div style="text-align: center">
                <i class="fas fa-upload" style="font-size: 36px; color: var(--primary-color); margin-bottom: 12px"></i>
                <div style="color: var(--text-secondary)">拖放组件到画布</div>
              </div>
            </div>
          </div>

          <!-- 画布控制按钮 -->
          <div class="canvas-controls">
            <button class="control-btn" title="放大"><i class="fas fa-search-plus"></i></button>
            <button class="control-btn" title="缩小"><i class="fas fa-search-minus"></i></button>
            <button class="control-btn" title="适应屏幕"><i class="fas fa-expand"></i></button>
            <button class="control-btn" id="gridToggleBtn" title="网格开关"><i class="fas fa-th"></i></button>
            <button class="control-btn" id="gridSettingsBtn" title="网格设置"><i class="fas fa-cog"></i></button>
            <button class="control-btn" id="guidelinesToggleBtn" title="辅助线开关"><i class="fas fa-ruler-combined"></i></button>
          </div>

          <!-- 网格设置面板 -->
          <div class="grid-settings-panel" id="gridSettingsPanel">
            <div class="settings-title">网格设置</div>

            <div class="settings-group">
              <div class="settings-label">网格可见性</div>
              <label class="switch">
                <input type="checkbox" id="gridVisibility" checked />
                <span class="slider round"></span>
              </label>
            </div>

            <div class="settings-group">
              <div class="settings-label">网格大小</div>
              <input type="range" id="gridSize" min="10" max="100" value="20" />
              <div style="display: flex; justify-content: space-between; font-size: 12px; color: var(--text-tertiary)">
                <span>10px</span>
                <span id="gridSizeValue">20px</span>
                <span>100px</span>
              </div>
            </div>

            <div class="settings-group">
              <div class="settings-label">网格颜色</div>
              <input type="color" id="gridColor" value="#e8e8e8" />
            </div>

            <div class="settings-group">
              <div class="settings-label">对齐到网格</div>
              <label class="switch">
                <input type="checkbox" id="snapToGrid" checked />
                <span class="slider round"></span>
              </label>
            </div>

            <div class="settings-group">
              <div class="settings-label">辅助线颜色</div>
              <input type="color" id="guidelineColor" value="#ff0000" />
            </div>

            <div style="display: flex; justify-content: flex-end; gap: 8px">
              <button class="btn" id="resetGridSettings">重置</button>
              <button class="btn btn-primary" id="applyGridSettings">应用</button>
            </div>
          </div>

          <!-- 辅助线容器 -->
          <div class="guidelines" id="guidelinesContainer"></div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="properties-panel">
          <div class="property-tabs">
            <div class="property-tab active" data-tab="layout">布局</div>
            <div class="property-tab" data-tab="style">样式</div>
            <div class="property-tab" data-tab="data">数据</div>
          </div>

          <div class="property-content">
            <!-- 布局属性 -->
            <div class="property-group" id="layoutProperties">
              <div class="property-title">位置与大小</div>
              <div class="property-item">
                <div class="property-label">X 坐标</div>
                <input type="number" placeholder="0" name="x-coord" />
              </div>
              <div class="property-item">
                <div class="property-label">Y 坐标</div>
                <input type="number" placeholder="0" name="y-coord" />
              </div>
              <div class="property-item">
                <div class="property-label">宽度</div>
                <input type="number" placeholder="0" name="width" />
              </div>
              <div class="property-item">
                <div class="property-label">高度</div>
                <input type="number" placeholder="0" name="height" />
              </div>
            </div>

            <div class="property-group">
              <div class="property-title">对齐方式</div>
              <div style="display: flex; gap: 8px">
                <button class="btn" style="flex: 1"><i class="fas fa-align-left"></i></button>
                <button class="btn" style="flex: 1"><i class="fas fa-align-center"></i></button>
                <button class="btn" style="flex: 1"><i class="fas fa-align-right"></i></button>
              </div>
              <div style="display: flex; gap: 8px; margin-top: 8px">
                <button class="btn" style="flex: 1"><i class="fas fa-align-top"></i></button>
                <button class="btn" style="flex: 1"><i class="fas fa-align-middle"></i></button>
                <button class="btn" style="flex: 1"><i class="fas fa-align-bottom"></i></button>
              </div>
            </div>

            <div class="property-group">
              <div class="property-title">分布方式</div>
              <div style="display: flex; gap: 8px">
                <button class="btn" style="flex: 1">
                  <i class="fas fa-arrows-alt-h"></i>
                  水平
                </button>
                <button class="btn" style="flex: 1">
                  <i class="fas fa-arrows-alt-v"></i>
                  垂直
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部状态栏 -->
      <div class="status-bar">
        <div style="display: flex; align-items: center; gap: 16px">
          <div style="font-size: 14px; color: var(--text-secondary)">
            <i class="fas fa-users"></i>
            协作编辑:
            <span style="color: var(--primary-color)">管理员</span>
          </div>
          <div style="width: 1px; height: 20px; background-color: var(--border-color)"></div>
          <div style="font-size: 14px; color: var(--text-secondary)">
            <i class="fas fa-expand-arrows-alt"></i>
            100%
          </div>
        </div>

        <div class="version-timeline">
          <div class="version-item active" title="当前版本"></div>
          <div class="version-line"></div>
          <div class="version-item" title="10:30"></div>
          <div class="version-line"></div>
          <div class="version-item" title="10:15"></div>
          <div class="version-line"></div>
          <div class="version-item" title="09:45"></div>
          <div class="version-line"></div>
          <div class="version-item" title="09:30"></div>
        </div>

        <div class="save-status">
          <div class="save-progress"></div>
          <div style="font-size: 14px; color: var(--success-color)">已保存</div>
        </div>
      </div>
    </div>

    <!-- 新建画布模态框 -->
    <div class="modal" id="createCanvasModal">
      <div class="modal-content" style="width: 480px">
        <div class="modal-header">
          <h3 style="margin: 0">新建画布</h3>
          <button class="modal-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>画布名称</label>
            <input type="text" id="canvasName" placeholder="请输入画布名称" />
          </div>
          <div class="form-group">
            <label>画布类型</label>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-top: 12px">
              <div class="card canvas-type-option" data-type="infographic" style="text-align: center; cursor: pointer; transition: all 0.3s; border: 2px solid transparent">
                <i class="fas fa-chart-line" style="font-size: 36px; color: var(--primary-color); margin: 16px 0"></i>
                <div style="font-weight: 500">信息图</div>
                <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px">适合展示数据关系</div>
              </div>
              <div class="card canvas-type-option" data-type="dashboard" style="text-align: center; cursor: pointer; transition: all 0.3s; border: 2px solid var(--primary-color)">
                <i class="fas fa-tachometer-alt" style="font-size: 36px; color: var(--primary-color); margin: 16px 0"></i>
                <div style="font-weight: 500">仪表盘</div>
                <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px">适合多指标监控</div>
              </div>
              <div class="card canvas-type-option" data-type="mindmap" style="text-align: center; cursor: pointer; transition: all 0.3s; border: 2px solid transparent">
                <i class="fas fa-project-diagram" style="font-size: 36px; color: var(--primary-color); margin: 16px 0"></i>
                <div style="font-weight: 500">思维导图</div>
                <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px">适合展示层级关系</div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label>画布尺寸</label>
            <select style="margin-top: 8px">
              <option value="16:9">16:9 (宽屏)</option>
              <option value="4:3">4:3 (标准)</option>
              <option value="21:9">21:9 (超宽屏)</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="form-group">
            <label>模板选择</label>
            <select style="margin-top: 8px">
              <option value="blank">空白模板</option>
              <option value="sales">销售数据分析</option>
              <option value="operation">运营监控</option>
              <option value="user">用户分析</option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" style="margin-right: 12px">取消</button>
          <button class="btn btn-primary">确认创建</button>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 右键菜单功能实现
      document.addEventListener('DOMContentLoaded', function () {
        const contextMenu = document.getElementById('contextMenu');
        let selectedComponent = null;

        // 显示右键菜单
        function showContextMenu(event, component) {
          event.preventDefault();
          selectedComponent = component;

          // 更新菜单位置
          contextMenu.style.left = `${event.clientX}px`;
          contextMenu.style.top = `${event.clientY}px`;
          contextMenu.style.display = 'block';
        }

        // 隐藏右键菜单
        function hideContextMenu() {
          contextMenu.style.display = 'none';
        }

        // 为画布上的组件添加右键菜单事件
        document.getElementById('canvasContent').addEventListener('contextmenu', function (event) {
          const component = event.target.closest('[style*="position: absolute"]');
          if (component) {
            showContextMenu(event, component);
          }
        });

        // 点击页面其他地方关闭右键菜单
        document.addEventListener('click', function () {
          hideContextMenu();
        });

        // 阻止右键菜单内部点击事件冒泡
        contextMenu.addEventListener('click', function (event) {
          event.stopPropagation();
        });

        // 处理右键菜单点击事件
        contextMenu.addEventListener('click', function (event) {
          const action = event.target.dataset.action;
          if (action && selectedComponent) {
            handleContextMenuAction(action, selectedComponent);
          }
        });

        // 处理二级菜单点击事件
        document.getElementById('levelSubmenu').addEventListener('click', function (event) {
          const action = event.target.dataset.action;
          if (action && selectedComponent) {
            handleOrderAction(action, selectedComponent);
          }
        });

        // 处理右键菜单操作
        function handleContextMenuAction(action, component) {
          switch (action) {
            case 'cut':
              // 剪切功能实现
              console.log('剪切组件');
              break;
            case 'copy':
              // 复制功能实现
              console.log('复制组件');
              break;
            case 'paste':
              // 粘贴功能实现
              console.log('粘贴组件');
              break;
            case 'hide':
              // 隐藏功能实现
              component.style.display = component.style.display === 'none' ? 'block' : 'none';
              break;
            // 其他菜单项的处理...
            default:
              console.log('未知操作:', action);
          }
          hideContextMenu();
        }

        // 处理顺序调整操作
        function handleOrderAction(action, component) {
          const canvasContent = document.getElementById('canvasContent');
          const components = Array.from(canvasContent.querySelectorAll('[style*="position: absolute"]'));
          const index = components.indexOf(component);

          switch (action) {
            case 'bringForward':
              // 上移一层
              if (index < components.length - 1) {
                canvasContent.insertBefore(component, components[index + 1].nextSibling);
              }
              break;
            case 'sendBackward':
              // 下移一层
              if (index > 0) {
                canvasContent.insertBefore(component, components[index - 1]);
              }
              break;
            case 'bringToFront':
              // 置于顶层
              canvasContent.appendChild(component);
              break;
            case 'sendToBack':
              // 置于底层
              canvasContent.insertBefore(component, canvasContent.firstChild);
              break;
            default:
              console.log('未知层级操作:', action);
          }
          hideContextMenu();
        }
      });
    </script>
    <!-- 引入Chart.js用于数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      // 画布管理页面特有脚本
      document.addEventListener('DOMContentLoaded', function () {
        // 初始化标签页切换
        const propertyTabs = document.querySelectorAll('.property-tab');
        propertyTabs.forEach(tab => {
          tab.addEventListener('click', () => {
            propertyTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // 这里可以添加切换不同属性面板的逻辑
          });
        });

        // 初始化主题切换
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
          option.addEventListener('click', () => {
            themeOptions.forEach(o => o.classList.remove('active'));
            option.classList.add('active');

            // 这里可以添加切换主题的逻辑
          });
        });

        // 初始化组件拖拽
        const componentItems = document.querySelectorAll('.component-item');
        const canvasContent = document.getElementById('canvasContent');

        componentItems.forEach(item => {
          item.addEventListener('dragstart', function (e) {
            e.dataTransfer.setData('text/plain', this.querySelector('.component-name').textContent);
            this.style.opacity = '0.5';
          });

          item.addEventListener('dragend', function () {
            this.style.opacity = '1';
          });
        });

        // 取消拖放默认提示图案
        canvasContent.addEventListener('dragover', function (e) {
          e.preventDefault();
          e.stopPropagation();
          this.style.backgroundColor = 'rgba(24, 144, 255, 0.05)';
        });

        canvasContent.addEventListener('dragenter', function (e) {
          e.preventDefault();
          e.stopPropagation();
        });

        canvasContent.addEventListener('dragleave', function () {
          this.style.backgroundColor = 'transparent';
        });

        // 搜索功能实现
        const searchToggleBtn = document.getElementById('searchToggleBtn');
        const searchContainer = document.getElementById('searchContainer');
        const componentSearchInput = document.getElementById('componentSearchInput');

        searchToggleBtn.addEventListener('click', function () {
          searchContainer.classList.toggle('active');
          if (searchContainer.classList.contains('active')) {
            componentSearchInput.focus();
          }
        });

        componentSearchInput.addEventListener('input', function () {
          const searchTerm = this.value.toLowerCase();
          componentItems.forEach(item => {
            const componentName = item.querySelector('.component-name').textContent.toLowerCase();
            if (componentName.includes(searchTerm)) {
              item.style.display = 'block';
            } else {
              item.style.display = 'none';
            }
          });
        });

        // 删除按钮功能实现
        canvasContent.addEventListener('click', function (e) {
          if (e.target.closest('.delete-component-btn')) {
            const component = e.target.closest('[style*="position: absolute"]');
            if (component) {
              // 添加删除动画
              component.style.opacity = '0';
              component.style.transform = 'scale(0.9)';
              component.style.transition = 'all 0.3s ease-out';

              setTimeout(() => {
                component.remove();
              }, 300);
            }
          }
        });

        // 网格和辅助线功能实现
        const gridToggleBtn = document.getElementById('gridToggleBtn');
        const gridSettingsBtn = document.getElementById('gridSettingsBtn');
        const gridSettingsPanel = document.getElementById('gridSettingsPanel');
        const guidelinesToggleBtn = document.getElementById('guidelinesToggleBtn');
        const guidelinesContainer = document.getElementById('guidelinesContainer');
        const canvasGrid = document.querySelector('.canvas-grid');
        const gridVisibility = document.getElementById('gridVisibility');
        const gridSize = document.getElementById('gridSize');
        const gridSizeValue = document.getElementById('gridSizeValue');
        const gridColor = document.getElementById('gridColor');
        const snapToGrid = document.getElementById('snapToGrid');
        const guidelineColor = document.getElementById('guidelineColor');
        const resetGridSettings = document.getElementById('resetGridSettings');
        const applyGridSettings = document.getElementById('applyGridSettings');

        // 网格开关
        gridToggleBtn.addEventListener('click', function () {
          canvasGrid.style.display = canvasGrid.style.display === 'none' ? 'block' : 'none';
          gridVisibility.checked = canvasGrid.style.display !== 'none';
        });

        // 网格设置面板开关
        gridSettingsBtn.addEventListener('click', function () {
          gridSettingsPanel.classList.toggle('active');
        });

        // 辅助线开关
        guidelinesToggleBtn.addEventListener('click', function () {
          if (guidelinesContainer.style.display === 'none') {
            guidelinesContainer.style.display = 'block';
            this.style.backgroundColor = 'var(--primary-color)';
            this.style.color = 'white';
          } else {
            guidelinesContainer.style.display = 'none';
            this.style.backgroundColor = '';
            this.style.color = '';
          }
        });

        // 网格大小滑块
        gridSize.addEventListener('input', function () {
          gridSizeValue.textContent = `${this.value}px`;
        });

        // 应用网格设置
        applyGridSettings.addEventListener('click', function () {
          // 更新网格可见性
          canvasGrid.style.display = gridVisibility.checked ? 'block' : 'none';

          // 更新网格大小
          const size = gridSize.value;
          canvasGrid.style.backgroundSize = `${size}px ${size}px`;

          // 更新网格颜色
          canvasGrid.style.backgroundImage = `linear-gradient(${gridColor.value} 1px, transparent 1px), linear-gradient(90deg, ${gridColor.value} 1px, transparent 1px)`;

          // 可以在这里添加对齐到网格的逻辑
        });

        // 重置网格设置
        resetGridSettings.addEventListener('click', function () {
          gridVisibility.checked = true;
          gridSize.value = 20;
          gridSizeValue.textContent = '20px';
          gridColor.value = '#e8e8e8';
          snapToGrid.checked = true;
          guidelineColor.value = '#ff0000';

          // 立即应用重置后的设置
          canvasGrid.style.display = 'block';
          canvasGrid.style.backgroundSize = '20px 20px';
          canvasGrid.style.backgroundImage = 'linear-gradient(#e8e8e8 1px, transparent 1px), linear-gradient(90deg, #e8e8e8 1px, transparent 1px)';
        });

        // 添加辅助线
        canvasContent.addEventListener('click', function (e) {
          if (e.ctrlKey && guidelinesContainer.style.display !== 'none') {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 创建水平辅助线
            const hGuideline = document.createElement('div');
            hGuideline.className = 'guideline-h';
            hGuideline.style.top = `${y}px`;
            hGuideline.style.backgroundColor = guidelineColor.value;
            guidelinesContainer.appendChild(hGuideline);

            // 创建垂直辅助线
            const vGuideline = document.createElement('div');
            vGuideline.className = 'guideline-v';
            vGuideline.style.left = `${x}px`;
            vGuideline.style.backgroundColor = guidelineColor.value;
            guidelinesContainer.appendChild(vGuideline);

            // 为辅助线添加双击删除功能
            [hGuideline, vGuideline].forEach(guideline => {
              guideline.addEventListener('dblclick', function () {
                this.remove();
              });
            });

            // 为辅助线添加拖拽功能
            makeGuidelineDraggable(hGuideline, 'horizontal');
            makeGuidelineDraggable(vGuideline, 'vertical');
          }
        });

        // 使辅助线可拖拽的函数
        function makeGuidelineDraggable(guideline, direction) {
          let isDragging = false;
          let startPos = 0;

          guideline.style.pointerEvents = 'auto';
          guideline.style.cursor = 'move';

          guideline.addEventListener('mousedown', function (e) {
            isDragging = true;
            startPos = direction === 'horizontal' ? e.clientY : e.clientX;
            document.body.style.userSelect = 'none';
          });

          document.addEventListener('mousemove', function (e) {
            if (!isDragging) return;

            const rect = canvasArea.getBoundingClientRect();
            let newPos;

            if (direction === 'horizontal') {
              newPos = e.clientY - rect.top;
              guideline.style.top = `${newPos}px`;
            } else {
              newPos = e.clientX - rect.left;
              guideline.style.left = `${newPos}px`;
            }

            e.preventDefault();
          });

          document.addEventListener('mouseup', function () {
            if (isDragging) {
              isDragging = false;
              document.body.style.userSelect = '';
            }
          });
        }

        // 组件拖拽放置事件
        canvasContent.addEventListener('drop', function (e) {
          e.preventDefault();
          this.style.backgroundColor = 'transparent';

          const componentName = e.dataTransfer.getData('text/plain');
          const rect = this.getBoundingClientRect();
          let x = e.clientX - rect.left; // 不再减去组件宽度的一半
          let y = e.clientY - rect.top; // 不再减去组件高度的一半

          // 如果启用了对齐到网格
          if (snapToGrid.checked && canvasGrid.style.display !== 'none') {
            const gridSizeValue = parseInt(gridSize.value);
            x = Math.round(x / gridSizeValue) * gridSizeValue;
            y = Math.round(y / gridSizeValue) * gridSizeValue;
          }

          // 创建新组件容器
          const newComponent = document.createElement('div');
          newComponent.style.position = 'absolute';
          newComponent.style.left = `${x}px`;
          newComponent.style.top = `${y}px`;
          newComponent.style.width = '600px';
          newComponent.style.height = '400px';
          newComponent.style.backgroundColor = 'white';
          newComponent.style.border = '1px solid var(--primary-color)';
          newComponent.style.borderRadius = '4px';
          newComponent.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
          newComponent.style.overflow = 'hidden';
          newComponent.style.cursor = 'move';
          newComponent.style.transition = 'all 0.3s ease';

          // 组件标题栏
          const componentHeader = document.createElement('div');
          componentHeader.style.padding = '8px 12px';
          componentHeader.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
          componentHeader.style.display = 'flex';
          componentHeader.style.justifyContent = 'space-between';
          componentHeader.style.alignItems = 'center';
          componentHeader.innerHTML = `
          <div style="font-weight: 500;">${componentName}</div>
          <div style="display: flex; gap: 8px;">
            <button class="component-control-btn copy-component-btn" title="复制组件"><i class="fas fa-copy"></i></button>
            <button class="component-control-btn rotate-component-btn" title="旋转组件"><i class="fas fa-rotate-right"></i></button>
            <button class="component-control-btn"><i class="fas fa-expand-alt"></i></button>
            <button class="component-control-btn delete-component-btn"><i class="fas fa-trash-alt"></i></button>
          </div>
        `;
          newComponent.appendChild(componentHeader);

          // 组件内容区域
          const componentBody = document.createElement('div');
          componentBody.style.padding = '12px';
          componentBody.style.height = 'calc(100% - 40px)';
          componentBody.style.boxSizing = 'border-box';

          // 根据组件类型创建不同内容
          if (componentName.includes('柱状')) {
            // 柱状图
            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            componentBody.appendChild(canvas);
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);

            // 初始化图表
            setTimeout(() => {
              const ctx = canvas.getContext('2d');
              new Chart(ctx, {
                type: 'bar',
                data: {
                  labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
                  datasets: [
                    {
                      label: '数据',
                      data: [12, 19, 3, 5, 2, 3],
                      backgroundColor: ['rgba(255, 99, 132, 0.2)', 'rgba(54, 162, 235, 0.2)', 'rgba(255, 206, 86, 0.2)', 'rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)', 'rgba(255, 159, 64, 0.2)'],
                      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],
                      borderWidth: 1,
                    },
                  ],
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                },
              });
            }, 100);
          } else if (componentName.includes('折线')) {
            // 折线图
            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            componentBody.appendChild(canvas);
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);

            // 初始化图表
            setTimeout(() => {
              const ctx = canvas.getContext('2d');
              new Chart(ctx, {
                type: 'line',
                data: {
                  labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
                  datasets: [
                    {
                      label: '数据',
                      data: [65, 59, 80, 81, 56, 55],
                      fill: false,
                      backgroundColor: 'rgba(54, 162, 235, 0.2)',
                      borderColor: 'rgba(54, 162, 235, 1)',
                      borderWidth: 2,
                      tension: 0.1,
                    },
                  ],
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                },
              });
            }, 100);
          } else if (componentName.includes('饼')) {
            // 饼图
            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            componentBody.appendChild(canvas);
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);

            // 初始化图表
            setTimeout(() => {
              const ctx = canvas.getContext('2d');
              new Chart(ctx, {
                type: 'pie',
                data: {
                  labels: ['红色', '蓝色', '黄色', '绿色', '紫色', '橙色'],
                  datasets: [
                    {
                      label: '数据',
                      data: [30, 25, 20, 15, 5, 5],
                      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'],
                      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],
                      borderWidth: 1,
                    },
                  ],
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                },
              });
            }, 100);
          } else if (componentName.includes('面积')) {
            // 面积图
            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            componentBody.appendChild(canvas);
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);

            // 初始化图表
            setTimeout(() => {
              const ctx = canvas.getContext('2d');
              new Chart(ctx, {
                type: 'line',
                data: {
                  labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
                  datasets: [
                    {
                      label: '数据',
                      data: [12, 19, 3, 5, 2, 3],
                      fill: true,
                      backgroundColor: 'rgba(75, 192, 192, 0.2)',
                      borderColor: 'rgba(75, 192, 192, 1)',
                      borderWidth: 1,
                    },
                  ],
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                },
              });
            }, 100);
          } else if (componentName.includes('标题')) {
            // 标题组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<div style="font-size: 24px; font-weight: 600; color: var(--text-primary);">示例标题</div>`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('文本')) {
            // 文本组件
            componentBody.style.overflow = 'auto';
            componentBody.innerHTML = `<div style="font-size: 14px; color: var(--text-secondary);">这是一个文本组件的示例内容。您可以在此处添加详细的描述文本、说明或其他相关信息。</div>`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('表格')) {
            // 表格组件
            componentBody.style.overflow = 'auto';
            componentBody.innerHTML = `
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background-color: rgba(24, 144, 255, 0.1);">
                  <th style="border: 1px solid var(--border-color); padding: 8px; text-align: left;">列1</th>
                  <th style="border: 1px solid var(--border-color); padding: 8px; text-align: left;">列2</th>
                  <th style="border: 1px solid var(--border-color); padding: 8px; text-align: left;">列3</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据1</td>
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据2</td>
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据3</td>
                </tr>
                <tr style="background-color: rgba(0, 0, 0, 0.02);">
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据4</td>
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据5</td>
                  <td style="border: 1px solid var(--border-color); padding: 8px;">数据6</td>
                </tr>
              </tbody>
            </table>
          `;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('矩形')) {
            // 矩形组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<div style="width: 100%; height: 100%; background-color: rgba(24, 144, 255, 0.1); border: 1px solid var(--primary-color);"></div>`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('圆形')) {
            // 圆形组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<div style="width: 80px; height: 80px; border-radius: 50%; background-color: rgba(24, 144, 255, 0.1); border: 1px solid var(--primary-color);"></div>`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('3D中国地图组件')) {
            // 3D中国地图组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<img src="images/3D-中国地图组件.png" alt="3D中国地图组件" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('3D省分地图组件')) {
            // 3D省分地图组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<img src="images/3D-省分地图组件.png" alt="3D省分地图组件" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('3D区域地图组件')) {
            // 3D区域地图组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<img src="images/3D-区域地图组件.png" alt="3D区域地图组件" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('3D地市组件')) {
            // 3D地市组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<img src="images/3D-地市地图组件.png" alt="3D地市组件" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else if (componentName.includes('图片')) {
            // 图片组件
            componentBody.style.display = 'flex';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';
            componentBody.innerHTML = `<img src="https://picsum.photos/200/100" alt="示例图片" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          } else {
            // 默认组件
            componentBody.style.display = 'flex';
            componentBody.style.flexDirection = 'column';
            componentBody.style.alignItems = 'center';
            componentBody.style.justifyContent = 'center';

            let iconClass = 'fa-square';
            if (componentName.includes('散点')) iconClass = 'fa-chart-scatter';
            else if (componentName.includes('雷达')) iconClass = 'fa-chart-polar-area';
            else if (componentName.includes('列表')) iconClass = 'fa-list';
            else if (componentName.includes('分割线')) iconClass = 'fa-border-style';

            componentBody.innerHTML = `
            <i class="fas ${iconClass}" style="font-size: 24px; color: var(--primary-color); margin-bottom: 8px;"></i>
            <div>${componentName}</div>
          `;
            newComponent.appendChild(componentBody);
            this.appendChild(newComponent);
          }

          // 设置组件初始大小（不设置固定位置，由拖放事件决定）
          newComponent.style.width = '200px';
          newComponent.style.height = '150px';

          // 实现组件拖动
          makeDraggable(newComponent);

          // 选中新添加的组件
          if (selectedComponent) {
            selectedComponent.style.border = '1px solid var(--primary-color)';
          }
          selectedComponent = newComponent;
          newComponent.style.border = '2px solid var(--primary-color)';
          // 确保DOM更新后再调用updatePropertyPanel
          setTimeout(() => {
            updatePropertyPanel(newComponent);
          }, 100);
        });

        // 获取属性面板输入框
        let selectedComponent = null;

        // 等待DOM完全加载后再获取输入框
        function getPropertyInputs() {
          const xCoordInput = document.querySelector('input[name="x-coord"]');
          const yCoordInput = document.querySelector('input[name="y-coord"]');
          const widthInput = document.querySelector('input[name="width"]');
          const heightInput = document.querySelector('input[name="height"]');

          // 如果找不到具名输入框，尝试使用类选择器
          if (!xCoordInput) {
            // 尝试查找位置与大小相关的输入框
            const propertyInputs = document.querySelectorAll('.property-content input');
            if (propertyInputs.length >= 4) {
              return {
                xCoordInput: propertyInputs[0],
                yCoordInput: propertyInputs[1],
                widthInput: propertyInputs[2],
                heightInput: propertyInputs[3],
              };
            } else {
              // 如果还是找不到，创建临时输入框（这种情况应该不会发生）
              console.error('无法找到属性面板输入框');
              return {
                xCoordInput: document.createElement('input'),
                yCoordInput: document.createElement('input'),
                widthInput: document.createElement('input'),
                heightInput: document.createElement('input'),
              };
            }
          }

          return {
            xCoordInput,
            yCoordInput,
            widthInput,
            heightInput,
          };
        }

        // 更新属性面板值
        function updatePropertyPanel(component) {
          const { xCoordInput, yCoordInput, widthInput, heightInput } = getPropertyInputs();

          if (!component) {
            xCoordInput.value = '0';
            yCoordInput.value = '0';
            widthInput.value = '0';
            heightInput.value = '0';
            return;
          }

          xCoordInput.value = (parseFloat(component.style.left) || 0).toFixed(1);
          yCoordInput.value = (parseFloat(component.style.top) || 0).toFixed(1);
          widthInput.value = (parseFloat(component.style.width) || 0).toFixed(1);
          heightInput.value = (parseFloat(component.style.height) || 0).toFixed(1);
        }

        // 画布点击事件处理
        canvasContent.addEventListener('click', function (e) {
          // 选中组件
          if (e.target.closest('[style*="position: absolute"]') && !e.target.closest('.component-control-btn')) {
            const component = e.target.closest('[style*="position: absolute"]');
            // 移除之前选中组件的边框高亮
            if (selectedComponent) {
              selectedComponent.style.border = '1px solid var(--primary-color)';
            }
            // 设置当前选中组件并高亮
            selectedComponent = component;
            component.style.border = '2px solid var(--primary-color)';
            // 更新属性面板
            updatePropertyPanel(component);
          }

          // 删除组件
          if (e.target.closest('.delete-component-btn')) {
            const component = e.target.closest('[style*="position: absolute"]');
            if (component) {
              component.remove();
              // 如果删除的是选中组件，更新选中状态
              if (component === selectedComponent) {
                selectedComponent = null;
                updatePropertyPanel(null);
              }
            }
          }
        });

        // 使元素可拖动的函数
        function makeDraggable(element) {
          let pos1 = 0,
            pos2 = 0,
            pos3 = 0,
            pos4 = 0;

          element.onmousedown = dragMouseDown;

          function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            // 获取鼠标位置
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;

            // 选中当前拖动的组件
            if (selectedComponent !== element) {
              if (selectedComponent) {
                selectedComponent.style.border = '1px solid var(--primary-color)';
              }
              selectedComponent = element;
              element.style.border = '2px solid var(--primary-color)';
            }
          }

          function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            // 计算新位置
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            // 设置新位置
            element.style.top = element.offsetTop - pos2 + 'px';
            element.style.left = element.offsetLeft - pos1 + 'px';
          }

          function closeDragElement() {
            // 停止移动
            document.onmouseup = null;
            document.onmousemove = null;
            // 拖动结束后更新属性面板
            updatePropertyPanel(element);
          }
        }

        // 初始化画布类型选择
        const canvasTypeOptions = document.querySelectorAll('.canvas-type-option');
        let selectedCanvasType = 'dashboard'; // 默认选择仪表盘

        // 从localStorage加载用户偏好的画布类型
        const savedCanvasType = localStorage.getItem('preferredCanvasType');
        if (savedCanvasType) {
          selectedCanvasType = savedCanvasType;
          // 更新UI以反映保存的偏好
          canvasTypeOptions.forEach(option => {
            if (option.dataset.type === savedCanvasType) {
              option.style.borderColor = 'var(--primary-color)';
            } else {
              option.style.borderColor = 'transparent';
            }
          });
        }

        // 为画布类型选项添加点击事件
        canvasTypeOptions.forEach(option => {
          option.addEventListener('click', function () {
            // 更新选中状态
            canvasTypeOptions.forEach(opt => {
              opt.style.borderColor = 'transparent';
            });
            this.style.borderColor = 'var(--primary-color)';
            selectedCanvasType = this.dataset.type;
          });
        });

        // 初始化新建画布按钮
        const createCanvasBtn = document.getElementById('createCanvasBtn');
        if (createCanvasBtn) {
          createCanvasBtn.addEventListener('click', function () {
            const modal = document.getElementById('createCanvasModal');
            if (modal) {
              modal.classList.add('show');
            }
          });
        }

        // 为模态框的确认按钮添加事件
        const confirmCreateBtn = document.querySelector('#createCanvasModal .btn-primary');
        if (confirmCreateBtn) {
          confirmCreateBtn.addEventListener('click', function () {
            const canvasNameInput = document.getElementById('canvasName');
            const canvasName = canvasNameInput.value.trim() || '未命名画布';

            // 保存用户偏好的画布类型到localStorage
            localStorage.setItem('preferredCanvasType', selectedCanvasType);

            // 创建画布
            createCanvas(canvasName, selectedCanvasType);

            // 关闭模态框
            const modal = document.getElementById('createCanvasModal');
            modal.classList.remove('show');

            // 重置输入框
            canvasNameInput.value = '';
          });
        }

        // 创建画布函数
        function createCanvas(name, type) {
          const canvasContent = document.getElementById('canvasContent');
          // 清空画布
          canvasContent.innerHTML = '';

          // 根据画布类型初始化不同的布局
          if (type === 'mindmap') {
            // 思维导图布局
            const mindmapContainer = document.createElement('div');
            mindmapContainer.style.position = 'absolute';
            mindmapContainer.style.top = '50%';
            mindmapContainer.style.left = '50%';
            mindmapContainer.style.transform = 'translate(-50%, -50%)';
            mindmapContainer.style.textAlign = 'center';

            const centerNode = document.createElement('div');
            centerNode.style.padding = '16px 24px';
            centerNode.style.backgroundColor = 'var(--primary-color)';
            centerNode.style.color = 'white';
            centerNode.style.borderRadius = '8px';
            centerNode.style.marginBottom = '40px';
            centerNode.textContent = name;
            mindmapContainer.appendChild(centerNode);

            // 添加一些示例分支
            const branchesContainer = document.createElement('div');
            branchesContainer.style.display = 'flex';
            branchesContainer.style.justifyContent = 'center';
            branchesContainer.style.gap = '40px';

            for (let i = 1; i <= 3; i++) {
              const branch = document.createElement('div');
              branch.style.display = 'flex';
              branch.style.flexDirection = 'column';
              branch.style.alignItems = 'center';

              const line = document.createElement('div');
              line.style.width = '2px';
              line.style.height = '30px';
              line.style.backgroundColor = 'var(--border-color)';
              branch.appendChild(line);

              const node = document.createElement('div');
              node.style.padding = '12px 16px';
              node.style.backgroundColor = 'white';
              node.style.border = '1px solid var(--primary-color)';
              node.style.borderRadius = '6px';
              node.textContent = `分支 ${i}`;
              branch.appendChild(node);

              branchesContainer.appendChild(branch);
            }

            mindmapContainer.appendChild(branchesContainer);
            canvasContent.appendChild(mindmapContainer);
          } else if (type === 'dashboard') {
            // 仪表盘布局
            const dashboardContainer = document.createElement('div');
            dashboardContainer.style.width = '100%';
            dashboardContainer.style.height = '100%';
            dashboardContainer.style.padding = '20px';
            dashboardContainer.style.boxSizing = 'border-box';

            // 创建网格布局
            dashboardContainer.style.display = 'grid';
            dashboardContainer.style.gridTemplateColumns = 'repeat(2, 1fr)';
            dashboardContainer.style.gridTemplateRows = 'repeat(2, 1fr)';
            dashboardContainer.style.gap = '20px';

            // 添加四个图表容器
            for (let i = 1; i <= 4; i++) {
              const chartContainer = document.createElement('div');
              chartContainer.style.backgroundColor = 'white';
              chartContainer.style.borderRadius = '8px';
              chartContainer.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
              chartContainer.style.padding = '16px';
              chartContainer.style.position = 'relative';
              chartContainer.style.overflow = 'hidden';
              chartContainer.dataset.chartType = ['bar', 'line', 'pie', 'doughnut'][i - 1];

              const chartTitle = document.createElement('div');
              chartTitle.style.fontSize = '16px';
              chartTitle.style.fontWeight = '500';
              chartTitle.style.marginBottom = '16px';
              chartTitle.textContent = `图表 ${i}`;
              chartContainer.appendChild(chartTitle);

              const canvas = document.createElement('canvas');
              canvas.style.width = '100%';
              canvas.style.height = 'calc(100% - 40px)';
              chartContainer.appendChild(canvas);

              dashboardContainer.appendChild(chartContainer);
            }

            canvasContent.appendChild(dashboardContainer);

            // 初始化图表
            setTimeout(() => {
              const chartContainers = dashboardContainer.querySelectorAll('[data-chart-type]');
              chartContainers.forEach(container => {
                const canvas = container.querySelector('canvas');
                const ctx = canvas.getContext('2d');
                const chartType = container.dataset.chartType;

                // 模拟数据
                const data = {
                  labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
                  datasets: [
                    {
                      label: '数据',
                      data: [12, 19, 3, 5, 2, 3],
                      backgroundColor: ['rgba(255, 99, 132, 0.2)', 'rgba(54, 162, 235, 0.2)', 'rgba(255, 206, 86, 0.2)', 'rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)', 'rgba(255, 159, 64, 0.2)'],
                      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],
                      borderWidth: 1,
                    },
                  ],
                };

                // 创建图表
                new Chart(ctx, {
                  type: chartType,
                  data: data,
                  options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales:
                      chartType !== 'pie' && chartType !== 'doughnut'
                        ? {
                            y: {
                              beginAtZero: true,
                            },
                          }
                        : {},
                  },
                });
              });
            }, 100);
          } else if (type === 'infographic') {
            // 信息图布局
            const infographicContainer = document.createElement('div');
            infographicContainer.style.position = 'absolute';
            infographicContainer.style.top = '50%';
            infographicContainer.style.left = '50%';
            infographicContainer.style.transform = 'translate(-50%, -50%)';
            infographicContainer.style.width = '80%';
            infographicContainer.style.maxWidth = '800px';

            // 标题
            const title = document.createElement('div');
            title.style.fontSize = '24px';
            title.style.fontWeight = '600';
            title.style.textAlign = 'center';
            title.style.marginBottom = '30px';
            title.textContent = name;
            infographicContainer.appendChild(title);

            // 主要内容区域
            const contentArea = document.createElement('div');
            contentArea.style.display = 'flex';
            contentArea.style.flexDirection = 'column';
            contentArea.style.gap = '20px';

            // 添加一些信息卡片
            for (let i = 1; i <= 3; i++) {
              const card = document.createElement('div');
              card.style.display = 'flex';
              card.style.alignItems = 'center';
              card.style.backgroundColor = 'white';
              card.style.borderRadius = '8px';
              card.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
              card.style.padding = '20px';
              card.style.gap = '20px';

              const iconContainer = document.createElement('div');
              iconContainer.style.width = '60px';
              iconContainer.style.height = '60px';
              iconContainer.style.borderRadius = '50%';
              iconContainer.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
              iconContainer.style.display = 'flex';
              iconContainer.style.alignItems = 'center';
              iconContainer.style.justifyContent = 'center';
              iconContainer.style.flexShrink = '0';

              const icon = document.createElement('i');
              icon.className = ['fas fa-chart-pie', 'fas fa-users', 'fas fa-line-chart'][i - 1];
              icon.style.fontSize = '24px';
              icon.style.color = 'var(--primary-color)';
              iconContainer.appendChild(icon);

              const textContainer = document.createElement('div');
              textContainer.style.flexGrow = '1';

              const cardTitle = document.createElement('div');
              cardTitle.style.fontSize = '16px';
              cardTitle.style.fontWeight = '500';
              cardTitle.textContent = `信息卡片 ${i}`;
              textContainer.appendChild(cardTitle);

              const cardDesc = document.createElement('div');
              cardDesc.style.fontSize = '14px';
              cardDesc.style.color = 'var(--text-secondary)';
              cardDesc.textContent = '这是一个示例信息卡片，用于展示关键数据和信息。';
              textContainer.appendChild(cardDesc);

              card.appendChild(iconContainer);
              card.appendChild(textContainer);
              contentArea.appendChild(card);
            }

            infographicContainer.appendChild(contentArea);
            canvasContent.appendChild(infographicContainer);
          }
        }

        // 自动保存功能
        let saveProgress = 0;
        const saveIntervalSeconds = 30;
        const saveProgressElement = document.querySelector('.save-progress');
        const saveStatusText = document.querySelector('.save-status div:nth-child(2)');

        // 创建环形进度条动画
        function updateSaveProgress() {
          saveProgress += 1 / saveIntervalSeconds;
          if (saveProgress >= 1) {
            saveProgress = 0;
            saveCanvasToCache();
          }

          // 更新环形进度条
          if (saveProgressElement) {
            const circumference = 2 * Math.PI * (saveProgressElement.clientWidth / 2 - 2); // 减去边框宽度
            const offset = circumference - saveProgress * circumference;
            saveProgressElement.style.transition = 'transform 0.1s linear';
            saveProgressElement.style.transform = `rotate(-90deg) stroke-dasharray(${circumference} ${circumference}) stroke-dashoffset(${offset})`;
          }
        }

        // 保存画布到缓存
        function saveCanvasToCache() {
          if (saveStatusText) {
            saveStatusText.textContent = '正在保存...';
            saveStatusText.style.color = 'var(--warning-color)';
          }

          // 模拟保存操作
          setTimeout(() => {
            // 获取画布内容
            const canvasContent = document.getElementById('canvasContent');
            const canvasData = {
              html: canvasContent.innerHTML,
              timestamp: new Date().getTime(),
            };

            // 保存到localStorage
            localStorage.setItem('canvasCache', JSON.stringify(canvasData));

            if (saveStatusText) {
              saveStatusText.textContent = '已保存';
              saveStatusText.style.color = 'var(--success-color)';
            }
          }, 500);
        }

        // 从缓存加载画布
        function loadCanvasFromCache() {
          const canvasData = localStorage.getItem('canvasCache');
          if (canvasData) {
            try {
              const parsedData = JSON.parse(canvasData);
              const canvasContent = document.getElementById('canvasContent');
              canvasContent.innerHTML = parsedData.html;

              // 重新初始化拖动功能
              const canvasElements = canvasContent.querySelectorAll('[style*="position: absolute"]');
              canvasElements.forEach(element => {
                makeDraggable(element);
              });
            } catch (e) {
              console.error('Failed to load canvas from cache:', e);
            }
          }
        }

        // 启动自动保存计时器
        setInterval(updateSaveProgress, 1000);

        // 页面加载时从缓存加载画布
        loadCanvasFromCache();
      });
    </script>
    <!-- 通知提示元素 -->
    <div class="notification" id="notification"></div>
  </body>
</html>
