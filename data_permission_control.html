<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数据权限控制 - 数智化运营平台</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
      }
      .tab {
        padding: 14px 24px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: var(--transition);
        color: var(--text-secondary);
      }
      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
      }
      .tab:hover:not(.active) {
        background-color: var(--bg-color);
        color: var(--text-primary);
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: bold">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: bold">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: bold">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
           <div class="menu-item child " data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child  active" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

         <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-lock page-title-icon"></i>
        数据权限控制
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">自定义报表</a></div>
        <div class="breadcrumb-item active">数据权限控制</div>
      </div>

      <!-- 标签页切换 -->
      <div class="tabs">
        <div class="tab active" data-tab-target="#permissionControl">权限控制</div>
        <div class="tab" data-tab-target="#personnelPermission">角色权限管理</div>
        <div class="tab" data-tab-target="#permissionChange">权限变更跟踪</div>
      </div>

      <!-- 权限控制 -->
      <div id="permissionControl" class="tab-content active">
        <div class="card">
          <div class="card-header">
            <div>
              <i class="fas fa-user-shield"></i>
              报表权限设置
            </div>
          </div>
          <div class="card-body">
            <div style="display: flex; gap: 20px; margin-bottom: 20px">
              <div style="flex: 1">
                <div style="margin-bottom: 10px; font-weight: bold">选择角色/用户组</div>
                <select style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                  <option value="admin">管理员</option>
                  <option value="analyst">数据分析员</option>
                  <option value="business">业务人员</option>
                  <option value="auditor">审计员</option>
                  <option value="custom">自定义角色</option>
                </select>
              </div>
              <div style="flex: 1">
                <div style="margin-bottom: 10px; font-weight: bold">选择报表</div>
                <select style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                  <option value="all">所有报表</option>
                  <option value="sales">销售报表</option>
                  <option value="finance">财务报表</option>
                  <option value="operation">运营报表</option>
                  <option value="user">用户报表</option>
                </select>
              </div>
            </div>

            <div style="margin-bottom: 20px">
              <div style="margin-bottom: 10px; font-weight: bold">设置权限</div>
              <div style="display: flex; gap: 20px">
                <div style="display: flex; align-items: center">
                  <input type="checkbox" id="viewPermission" checked style="margin-right: 8px" />
                  <label for="viewPermission">查看权限</label>
                </div>
                <div style="display: flex; align-items: center">
                  <input type="checkbox" id="editPermission" style="margin-right: 8px" />
                  <label for="editPermission">编辑权限</label>
                </div>
                <div style="display: flex; align-items: center">
                  <input type="checkbox" id="exportPermission" style="margin-right: 8px" />
                  <label for="exportPermission">导出权限</label>
                </div>
                <div style="display: flex; align-items: center">
                  <input type="checkbox" id="sharePermission" style="margin-right: 8px" />
                  <label for="sharePermission">分享权限</label>
                </div>
              </div>
            </div>

            <div style="margin-bottom: 20px">
              <div style="margin-bottom: 10px; font-weight: bold">数据范围限制</div>
              <div style="display: flex; gap: 20px; flex-wrap: wrap">
                <div style="flex: 1; min-width: 200px">
                  <div style="margin-bottom: 5px; font-size: 14px">区域限制</div>
                  <select style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                    <option value="all">全部区域</option>
                    <option value="north">华北区</option>
                    <option value="east">华东区</option>
                    <option value="south">华南区</option>
                    <option value="central">华中区</option>
                    <option value="west">西区</option>
                  </select>
                </div>
                <div style="flex: 1; min-width: 200px">
                  <div style="margin-bottom: 5px; font-size: 14px">行业限制</div>
                  <select style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                    <option value="all">全部行业</option>
                    <option value="finance">金融行业</option>
                    <option value="retail">零售行业</option>
                    <option value="manufacture">制造业</option>
                    <option value="service">服务业</option>
                  </select>
                </div>
                <div style="flex: 1; min-width: 200px">
                  <div style="margin-bottom: 5px; font-size: 14px">时间范围限制</div>
                  <select style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                    <option value="all">全部时间</option>
                    <option value="currentMonth">本月</option>
                    <option value="currentQuarter">本季度</option>
                    <option value="currentYear">本年度</option>
                    <option value="custom">自定义</option>
                  </select>
                </div>
              </div>
            </div>

            <div style="display: flex; justify-content: flex-end">
              <button class="btn btn-sm btn-primary" onclick="alert('保存成功！')">保存</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限操作管理部分已合并到人员角色权限标签页 -->

      <!-- 人员角色权限 -->
      <div id="personnelPermission" class="tab-content">
        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div>
                <i class="fas fa-users-cog"></i>
                角色权限管理
              </div>
              <div style="display: flex; gap: 10px">
                <div style="display: flex; align-items: center">
                  <input type="text" placeholder="搜索人员..." style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); margin-right: 10px" />
                  <button class="btn btn-sm btn-primary" style="width:100px">
                    <i class="fas fa-search"></i>
                    搜索
                  </button>
                </div>
                <button class="btn btn-sm btn-primary" style="width:120px">
                  <i class="fas fa-plus"></i>
                  分配角色
                </button>
              </div>
            </div>
          </div>
          <div class="card-body" style="margin-top:10px">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 5%"><input type="checkbox" /></th>
                  <th style="width: 10%">人员姓名</th>
                  <th style="width: 10%">工号</th>
                  <th style="width: 10%">所属部门</th>
                  <th style="width: 15%">角色</th>
                  <th style="width: 30%">拥有权限</th>
                  <th style="width: 20%">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>张三</td>
                  <td>EMP001</td>
                  <td>数据分析部</td>
                  <td>数据分析员</td>
                  <td>销售报表(查看、编辑、导出), 运营报表(查看)</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs btn-primary">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-danger">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>李四</td>
                  <td>EMP002</td>
                  <td>财务部</td>
                  <td>审计员</td>
                  <td>财务报表(查看、导出), 销售报表(查看)</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs btn-primary">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-danger">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>王五</td>
                  <td>EMP003</td>
                  <td>销售部</td>
                  <td>业务人员</td>
                  <td>销售报表(查看、导出), 运营报表(查看)</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs btn-primary">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-danger">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>赵六</td>
                  <td>EMP004</td>
                  <td>管理层</td>
                  <td>管理员</td>
                  <td>所有报表(查看、编辑、导出、分享)</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs btn-primary">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-danger">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px">
              <div style="color: var(--text-tertiary)">共25条记录，显示1-4条</div>
              <div class="pagination">
                <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn">4</button>
                <button class="pagination-btn">5</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限变更跟踪 -->
      <div id="permissionChange" class="tab-content">
        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div>
                <i class="fas fa-history"></i>
                权限变更记录
              </div>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-download"></i>
                导出历史
              </button>
            </div>
          </div>
          <div class="card-body">
            <div style="display: flex; gap: 20px; margin-bottom: 20px">
              <div style="flex: 1">
                <div style="margin-bottom: 10px; font-weight: bold">操作人</div>
                <input type="text" placeholder="搜索操作人..." style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
              </div>
              <div style="flex: 1">
                <div style="margin-bottom: 10px; font-weight: bold">时间范围</div>
                <input type="date" style="width: 100%; padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
              </div>
            </div>

            <table class="table">
              <thead>
                <tr>
                  <th style="width: 5%"><input type="checkbox" /></th>
                  <th style="width: 15%">操作时间</th>
                  <th style="width: 15%">操作人</th>
                  <th style="width: 15%">角色</th>
                  <th style="width: 15%">变更类型</th>
                  <th style="width: 35%">变更详情</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023-09-10 14:30</td>
                  <td>管理员</td>
                  <td>业务人员</td>
                  <td><span style="color: var(--primary-color)">权限新增</span></td>
                  <td>为业务人员角色添加了销售报表的查看权限</td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023-09-08 09:45</td>
                  <td>张三</td>
                  <td>数据分析员</td>
                  <td><span style="color: var(--warning-color)">权限修改</span></td>
                  <td>修改了数据分析员角色的财务报表导出权限</td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023-09-05 16:20</td>
                  <td>李四</td>
                  <td>审计员</td>
                  <td><span style="color: var(--danger-color)">权限删除</span></td>
                  <td>移除了审计员角色的运营报表编辑权限</td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023-09-01 11:15</td>
                  <td>王五</td>
                  <td>管理员</td>
                  <td><span style="color: var(--primary-color)">权限新增</span></td>
                  <td>为管理员角色添加了所有报表的管理权限</td>
                </tr>
              </tbody>
            </table>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px">
              <div style="color: var(--text-tertiary)">共18条记录，显示1-4条</div>
              <div class="pagination">
                <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn">4</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分配角色弹窗 -->
    <div id="assignRoleModal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h2>
            <i class="fas fa-user-plus"></i>
            分配角色
          </h2>
          <span class="close-btn">&times;</span>
        </div>
        <div class="modal-body">
          <div style="margin-bottom: 15px">
            <label style="display: block; margin-bottom: 5px; font-weight: bold">人员姓名</label>
            <input type="text" id="personName" placeholder="请输入人员姓名" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px" />
          </div>
          <div style="margin-bottom: 15px">
            <label style="display: block; margin-bottom: 5px; font-weight: bold">工号</label>
            <input type="text" id="employeeId" placeholder="请输入工号" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px" />
          </div>
          <div style="margin-bottom: 15px">
            <label style="display: block; margin-bottom: 5px; font-weight: bold">所属部门</label>
            <select id="department" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px">
              <option value="">请选择部门</option>
              <option value="数据分析部">数据分析部</option>
              <option value="财务部">财务部</option>
              <option value="销售部">销售部</option>
              <option value="管理层">管理层</option>
            </select>
          </div>
          <div style="margin-bottom: 15px">
            <label style="display: block; margin-bottom: 5px; font-weight: bold">角色</label>
            <select id="role" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px">
              <option value="">请选择角色</option>
              <option value="数据分析员">数据分析员</option>
              <option value="审计员">审计员</option>
              <option value="业务人员">业务人员</option>
              <option value="管理员">管理员</option>
            </select>
          </div>
          <div style="margin-bottom: 15px">
            <label style="display: block; margin-bottom: 5px; font-weight: bold">拥有权限</label>
            <div style="display: flex; flex-wrap: wrap; gap: 10px">
              <div style="display: flex; align-items: center">
                <input type="checkbox" id="viewPermission" style="margin-right: 5px" />
                <label for="viewPermission">查看权限</label>
              </div>
              <div style="display: flex; align-items: center">
                <input type="checkbox" id="editPermission" style="margin-right: 5px" />
                <label for="editPermission">编辑权限</label>
              </div>
              <div style="display: flex; align-items: center">
                <input type="checkbox" id="exportPermission" style="margin-right: 5px" />
                <label for="exportPermission">导出权限</label>
              </div>
              <div style="display: flex; align-items: center">
                <input type="checkbox" id="sharePermission" style="margin-right: 5px" />
                <label for="sharePermission">分享权限</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="cancelBtn" class="btn btn-sm btn-secondary">取消</button>
          <button id="confirmBtn" class="btn btn-sm btn-primary">确认分配</button>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      // API基础URL
      const API_BASE_URL = 'http://localhost:8000/api';

      // 封装fetch请求函数
      async function apiRequest(url, method, data = null) {
        try {
          const options = {
            method: method,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
            }
          };

          if (data) {
            options.body = JSON.stringify(data);
          }

          console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
          const response = await fetch(url, options);
          const result = await response.json();
          console.log(`接口响应:`, result);

          if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
          }

          return result;
        } catch (error) {
          console.error('API请求错误:', error);
          // 不处理响应结果，静默处理错误
          throw error;
        }
      }

      // 页面加载时调用接口
      window.addEventListener('load', async function() {
        try {
          await apiRequest(`${API_BASE_URL}/data-permission-control/page-visit`, 'POST', {
            action: 'page_visit',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('页面访问接口调用成功');
        } catch (error) {
          console.error('页面访问接口调用失败:', error);
        }
      });

      // 弹窗通用样式
      const modalStyle = document.createElement('style');
      modalStyle.textContent = `
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
        justify-content: center;
        align-items: center;
      }
      .modal-content {
        background-color: #fefefe;
        margin: auto;
        padding: 20px;
        border: 1px solid #888;
        width: 80%;
        max-width: 500px;
        border-radius: 8px;
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
        animation-name: modalopen;
        animation-duration: 0.4s;
      }
      .modal-header {
        padding: 10px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #eee;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .modal-header h2 {
        margin: 0;
        font-size: 18px;
      }
      .modal-body {
        padding: 16px;
      }
      .modal-footer {
        padding: 10px 16px;
        background-color: #f5f7fa;
        border-top: 1px solid #eee;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }
      .close-btn, .edit-close-btn {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }
      .close-btn:hover,
      .close-btn:focus,
      .edit-close-btn:hover,
      .edit-close-btn:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
      }
      @keyframes modalopen {
        from {opacity: 0}
        to {opacity: 1}
      }
    `;
      document.head.appendChild(modalStyle);

      // 获取弹窗元素
      const modal = document.getElementById('assignRoleModal');
      const closeBtn = document.querySelector('.close-btn');
      const cancelBtn = document.getElementById('cancelBtn');
      const confirmBtn = document.getElementById('confirmBtn');

      // 打开弹窗的函数
      function openModal() {
        modal.style.display = 'flex';
      }

      // 关闭弹窗的函数
      function closeModal() {
        modal.style.display = 'none';
      }

      // 为分配角色按钮添加点击事件
      document.querySelectorAll('.fa-plus').forEach(icon => {
        if (icon.closest('button') && icon.closest('button').textContent.includes('分配角色')) {
          icon.closest('button').addEventListener('click', async function() {
            try {
              await apiRequest(`${API_BASE_URL}/data-permission-control/assign-role`, 'POST', {
                action: 'assign_role',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
              });
              console.log('分配角色接口调用成功');
            } catch (error) {
              console.error('分配角色接口调用失败:', error);
            }
            openModal();
          });
        }
      });

      // 为关闭按钮添加点击事件
      closeBtn.addEventListener('click', closeModal);
      cancelBtn.addEventListener('click', closeModal);

      // 点击弹窗外部关闭弹窗
      window.addEventListener('click', function (event) {
        if (event.target === modal) {
          closeModal();
        }
      });

      // 确认分配按钮点击事件
      confirmBtn.addEventListener('click', function () {
        const personName = document.getElementById('personName').value;
        if (personName) {
          alert(`已成功为 ${personName} 分配角色`);
          closeModal();
          // 这里可以添加实际提交表单的代码
        } else {
          alert('请输入人员姓名');
        }
      });

      // 标签页切换
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', async function () {
          const target = this.getAttribute('data-tab-target');
          const tabContents = document.querySelectorAll('.tab-content');
          const tabs = document.querySelectorAll('.tab');

          // 调用标签切换接口
          try {
            await apiRequest(`${API_BASE_URL}/data-permission-control/tab-switch`, 'POST', {
              action: 'tab_switch',
              tabName: this.textContent.trim(),
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('标签切换接口调用成功');
          } catch (error) {
            console.error('标签切换接口调用失败:', error);
          }

          // 更新标签状态
          tabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');

          // 更新内容区域
          tabContents.forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none';
          });

          const activeContent = document.querySelector(target);
          activeContent.classList.add('active');
          activeContent.style.display = 'block';
        });
      });

      // 编辑角色弹窗
      const editModal = document.createElement('div');
      editModal.id = 'editRoleModal';
      editModal.className = 'modal';
      editModal.style.display = 'none';
      editModal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="fas fa-user-edit"></i> 编辑角色</h2>
          <span class="edit-close-btn">&times;</span>
        </div>
        <div class="modal-body">
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">人员姓名</label>
            <input type="text" id="editPersonName" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" readonly>
          </div>
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">工号</label>
            <input type="text" id="editEmployeeId" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" readonly>
          </div>
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">所属部门</label>
            <select id="editDepartment" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="">请选择部门</option>
              <option value="数据分析部">数据分析部</option>
              <option value="财务部">财务部</option>
              <option value="销售部">销售部</option>
              <option value="管理层">管理层</option>
            </select>
          </div>
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">角色</label>
            <select id="editRole" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="">请选择角色</option>
              <option value="数据分析员">数据分析员</option>
              <option value="审计员">审计员</option>
              <option value="业务人员">业务人员</option>
              <option value="管理员">管理员</option>
            </select>
          </div>
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">拥有权限</label>
            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
              <div style="display: flex; align-items: center;">
                <input type="checkbox" id="editViewPermission" style="margin-right: 5px;">
                <label for="editViewPermission">查看权限</label>
              </div>
              <div style="display: flex; align-items: center;">
                <input type="checkbox" id="editEditPermission" style="margin-right: 5px;">
                <label for="editEditPermission">编辑权限</label>
              </div>
              <div style="display: flex; align-items: center;">
                <input type="checkbox" id="editExportPermission" style="margin-right: 5px;">
                <label for="editExportPermission">导出权限</label>
              </div>
              <div style="display: flex; align-items: center;">
                <input type="checkbox" id="editSharePermission" style="margin-right: 5px;">
                <label for="editSharePermission">分享权限</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="editCancelBtn" class="btn btn-sm btn-secondary">取消</button>
          <button id="editConfirmBtn" class="btn btn-sm btn-primary">确认修改</button>
        </div>
      </div>
    `;
      document.body.appendChild(editModal);

      // 打开编辑弹窗的函数
      function openEditModal(row) {
        // 填充表单数据
        document.getElementById('editPersonName').value = row.querySelector('td:nth-child(2)').textContent;
        document.getElementById('editEmployeeId').value = row.querySelector('td:nth-child(3)').textContent;
        document.getElementById('editDepartment').value = row.querySelector('td:nth-child(4)').textContent;
        document.getElementById('editRole').value = row.querySelector('td:nth-child(5)').textContent;

        // 解析权限并勾选对应的复选框
        const permissions = row.querySelector('td:nth-child(6)').textContent;
        document.getElementById('editViewPermission').checked = permissions.includes('查看');
        document.getElementById('editEditPermission').checked = permissions.includes('编辑');
        document.getElementById('editExportPermission').checked = permissions.includes('导出');
        document.getElementById('editSharePermission').checked = permissions.includes('分享');

        // 显示弹窗
        editModal.style.display = 'flex';
      }

      // 关闭编辑弹窗的函数
      function closeEditModal() {
        editModal.style.display = 'none';
      }

      // 为编辑按钮添加点击事件
      document.querySelectorAll('.fa-edit').forEach(icon => {
        if (icon.closest('button') && icon.closest('button').textContent.includes('编辑')) {
          icon.closest('button').addEventListener('click', async function () {
            // 获取当前行的人员信息
            const row = this.closest('tr');
            const personName = row.querySelector('td:nth-child(2)').textContent;
            
            try {
              await apiRequest(`${API_BASE_URL}/data-permission-control/edit-role`, 'POST', {
                action: 'edit_role',
                personName: personName,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
              });
              console.log('编辑角色接口调用成功');
            } catch (error) {
              console.error('编辑角色接口调用失败:', error);
            }
            
            openEditModal(row);
          });
        }
      });

      // 为编辑弹窗的关闭按钮添加点击事件
      editModal.querySelector('.edit-close-btn').addEventListener('click', closeEditModal);
      document.getElementById('editCancelBtn').addEventListener('click', closeEditModal);

      // 点击编辑弹窗外部关闭弹窗
      window.addEventListener('click', function (event) {
        if (event.target === editModal) {
          closeEditModal();
        }
      });

      // 确认修改按钮点击事件
      document.getElementById('editConfirmBtn').addEventListener('click', function () {
        const personName = document.getElementById('editPersonName').value;
        alert(`已成功修改 ${personName} 的角色信息`);
        closeEditModal();
        // 这里可以添加实际提交修改的代码
      });

      // 删除按钮点击事件
      document.querySelectorAll('.fa-trash').forEach(icon => {
        if (icon.closest('button') && icon.closest('button').textContent.includes('删除')) {
          icon.closest('button').addEventListener('click', async function () {
            // 获取当前行的人员信息
            const row = this.closest('tr');
            const name = row.querySelector('td:nth-child(2)').textContent;
            
            try {
              await apiRequest(`${API_BASE_URL}/data-permission-control/delete-role`, 'POST', {
                action: 'delete_role',
                personName: name,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
              });
              console.log('删除角色接口调用成功');
            } catch (error) {
              console.error('删除角色接口调用失败:', error);
            }
            
            // 模拟删除操作
            if (confirm(`确定要删除 ${name} 的记录吗？`)) {
              alert(`已成功删除 ${name} 的记录`);
              // 这里可以添加实际删除行的代码
              // row.remove();
            }
          });
        }
      });

      // 分配角色按钮点击事件已在上方实现
      // 此处保留空行以保持代码结构清晰

      // 搜索按钮事件处理
      document.querySelectorAll('.fa-search').forEach(icon => {
        if (icon.closest('button') && icon.closest('button').textContent.includes('搜索')) {
          icon.closest('button').addEventListener('click', async function() {
            const searchInput = this.closest('div').querySelector('input[placeholder="搜索人员..."]');
            const searchTerm = searchInput ? searchInput.value : '';
            
            try {
              await apiRequest(`${API_BASE_URL}/data-permission-control/search`, 'POST', {
                action: 'search',
                searchTerm: searchTerm,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
              });
              console.log('搜索接口调用成功');
            } catch (error) {
              console.error('搜索接口调用失败:', error);
            }
          });
        }
      });

      // 分页按钮事件处理
      document.querySelectorAll('.pagination-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
          if (this.disabled) return;

          try {
            await apiRequest(`${API_BASE_URL}/data-permission-control/pagination`, 'POST', {
              action: 'pagination',
              pageNumber: this.textContent.trim(),
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('分页接口调用成功');
          } catch (error) {
            console.error('分页接口调用失败:', error);
          }
        });
      });

      // 复选框选择事件处理
      document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', async function() {
          try {
            await apiRequest(`${API_BASE_URL}/data-permission-control/checkbox-select`, 'POST', {
              action: 'checkbox_select',
              isChecked: this.checked,
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('复选框选择接口调用成功');
          } catch (error) {
            console.error('复选框选择接口调用失败:', error);
          }
        });
      });

      // 下拉框选择事件处理
      document.querySelectorAll('select').forEach(select => {
        select.addEventListener('change', async function() {
          try {
            await apiRequest(`${API_BASE_URL}/data-permission-control/select-change`, 'POST', {
              action: 'select_change',
              selectValue: this.value,
              selectName: this.id || this.name || 'unknown',
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('下拉框选择接口调用成功');
          } catch (error) {
            console.error('下拉框选择接口调用失败:', error);
          }
        });
      });

      // 弹窗确认按钮事件处理
      document.getElementById('confirmBtn').addEventListener('click', async function () {
        const personName = document.getElementById('personName').value;
        
        try {
          await apiRequest(`${API_BASE_URL}/data-permission-control/confirm-assign`, 'POST', {
            action: 'confirm_assign',
            personName: personName,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('确认分配接口调用成功');
        } catch (error) {
          console.error('确认分配接口调用失败:', error);
        }
        
        if (personName) {
          alert(`已成功为 ${personName} 分配角色`);
          closeModal();
          // 这里可以添加实际提交表单的代码
        } else {
          alert('请输入人员姓名');
        }
      });

      // 编辑弹窗确认按钮事件处理
      document.getElementById('editConfirmBtn').addEventListener('click', async function () {
        const personName = document.getElementById('editPersonName').value;
        
        try {
          await apiRequest(`${API_BASE_URL}/data-permission-control/confirm-edit`, 'POST', {
            action: 'confirm_edit',
            personName: personName,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('确认编辑接口调用成功');
        } catch (error) {
          console.error('确认编辑接口调用失败:', error);
        }
        
        alert(`已成功修改 ${personName} 的角色信息`);
        closeEditModal();
        // 这里可以添加实际提交修改的代码
      });

      // 初始化第一个标签内容
      document.querySelector('.tab-content.active').style.display = 'block';

      // 保存设置
      document.querySelector('.btn-primary').addEventListener('click', async function () {
        try {
          await apiRequest(`${API_BASE_URL}/data-permission-control/save-settings`, 'POST', {
            action: 'save_settings',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('保存设置接口调用成功');
        } catch (error) {
          console.error('保存设置接口调用失败:', error);
        }

        // 保持原有逻辑
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        this.disabled = true;

        setTimeout(() => {
          this.innerHTML = '保存设置';
          this.disabled = false;
          // 静默处理，不显示提示
        }, 1500);
      });

      // 导出历史
      document.querySelectorAll('.fa-download').forEach(icon => {
        icon.closest('button').addEventListener('click', async function () {
          try {
            await apiRequest(`${API_BASE_URL}/data-permission-control/export-history`, 'POST', {
              action: 'export_history',
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('导出历史接口调用成功');
          } catch (error) {
            console.error('导出历史接口调用失败:', error);
          }

          // 保持原有逻辑
          this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
          this.disabled = true;

          setTimeout(() => {
            this.innerHTML = '<i class="fas fa-download"></i> 导出历史';
            this.disabled = false;
            alert('权限变更历史已成功导出');
          }, 1500);
        });
      });
    </script>
  </body>
</html>
