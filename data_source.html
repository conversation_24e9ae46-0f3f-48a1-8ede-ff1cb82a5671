<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 数据源管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
      }
      
      .notification.success {
        background-color: #10b981;
      }
      
      .notification.error {
        background-color: #ef4444;
      }
      
      .notification.info {
        background-color: #3b82f6;
      }
      
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }
      
      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">数据源管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">离线采集任务管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">实时采集任务管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child active" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

       <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

     <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-database page-title-icon"></i>
        数据源管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">数据融通</a></div>
        <div class="breadcrumb-item active">数据源管理</div>
      </div>

      <!-- 搜索和操作栏 -->
      <div style="display: flex; flex-wrap: wrap; justify-content: flex-start; align-items: center; margin-bottom: 20px; gap: 10px">
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);height:40px">
            <option value="all">全部类型</option>
            <option value="mysql">MySQL</option>
            <option value="oracle">Oracle</option>
            <option value="file">文件</option>
            <option value="ftp">FTP</option>
            <option value="api">API</option>
            <option value="kafka">Kafka</option>
          </select>
        </div>
        <div style="display: flex; gap: 5px;">
          <div class="search-box" style="width: 300px; margin-bottom: 0">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" id="searchDataSourceInput" placeholder="搜索数据源..." />
          </div>
          <button id="btnSearchDataSource" class="btn btn-primary" style="white-space: nowrap;">
            <i class="fas fa-search"></i>
            查询
          </button>
        </div>
        <div style="display: flex; flex-wrap: wrap; gap: 10px;margin-left:auto">
          <button class="btn btn-primary" data-modal-target="addDataSourceModal">
            <i class="fas fa-plus"></i>
            新增数据源
          </button>

          <button class="btn btn-info" data-modal-target="addDataIntegrityModal">
            <i class="fas fa-shield-alt"></i>
            数据完整性校验
          </button>
          <button class="btn btn-warning" data-modal-target="addDataValidationModal">
            <i class="fas fa-check-circle"></i>
            数据格式验证
          </button>
          <button class="btn btn-danger" data-modal-target="encryptDataModal">
            <i class="fas fa-lock"></i>
            加密采集数据
          </button>
          <button class="btn btn-purple" data-modal-target="addDataScopeModal">
            <i class="fas fa-border-style"></i>
            数据采集范围管理
          </button>
        </div>
      </div>

      <!-- 数据源表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>数据源名称</th>
                <th>类型</th>
                <th>连接信息</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>用户数据库</td>
                <td><span class="tag tag-info">MySQL</span></td>
                <td>localhost:3306</td>
                <td><span class="tag tag-success">已连接</span></td>
                <td>2023-07-01 10:30</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>销售数据库</td>
                <td><span class="tag tag-warning">Oracle</span></td>
                <td>*************:1521</td>
                <td><span class="tag tag-success">已连接</span></td>
                <td>2023-07-02 14:15</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>日志文件</td>
                <td><span class="tag tag-primary">文件</span></td>
                <td>/data/logs/app.log</td>
                <td><span class="tag tag-success">已连接</span></td>
                <td>2023-07-03 09:45</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>报表文件</td>
                <td><span class="tag tag-danger">FTP</span></td>
                <td>ftp.example.com:21</td>
                <td><span class="tag tag-warning">未连接</span></td>
                <td>2023-07-05 16:20</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>第三方API</td>
                <td><span class="tag tag-success">API</span></td>
                <td>https://api.example.com</td>
                <td><span class="tag tag-success">已连接</span></td>
                <td>2023-07-08 11:05</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>实时数据流</td>
                <td><span class="tag tag-info">Kafka</span></td>
                <td>192.168.1.101:9092</td>
                <td><span class="tag tag-success">已连接</span></td>
                <td>2023-07-10 15:30</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 新增数据源模态框 -->
    <div class="modal" id="addDataSourceModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            新增数据源
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addDataSourceForm">
            <div class="form-group">
              <label for="dsName">数据源名称</label>
              <input type="text" id="dsName" name="dsName" required placeholder="请输入数据源名称" />
            </div>
            <div class="form-group">
              <label for="dsType">数据源类型</label>
              <select id="dsType" name="dsType" required onchange="changeDataSourceForm(this.value)">
                <option value="">请选择数据源类型</option>
                <option value="mysql">MySQL</option>
                <option value="oracle">Oracle</option>
                <option value="file">文件</option>
                <option value="ftp">FTP</option>
                <option value="api">API</option>
                <option value="kafka">Kafka</option>
              </select>
            </div>
            <div id="mysqlConfig" style="display: none">
              <div class="form-group">
                <label for="mysqlHost">主机地址</label>
                <input type="text" id="mysqlHost" name="mysqlHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="mysqlPort">端口</label>
                <input type="number" id="mysqlPort" name="mysqlPort" placeholder="请输入端口" value="3306" />
              </div>
              <div class="form-group">
                <label for="mysqlDatabase">数据库名</label>
                <input type="text" id="mysqlDatabase" name="mysqlDatabase" placeholder="请输入数据库名" />
              </div>
              <div class="form-group">
                <label for="mysqlUsername">用户名</label>
                <input type="text" id="mysqlUsername" name="mysqlUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="mysqlPassword">密码</label>
                <input type="password" id="mysqlPassword" name="mysqlPassword" placeholder="请输入密码" />
              </div>
              <div class="form-group">
                <label for="mysqlCharset">字符集</label>
                <input type="text" id="mysqlCharset" name="mysqlCharset" placeholder="请输入字符集" value="utf8mb4" />
              </div>
            </div>
            <div id="oracleConfig" style="display: none">
              <div class="form-group">
                <label for="oracleHost">主机地址</label>
                <input type="text" id="oracleHost" name="oracleHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="oraclePort">端口</label>
                <input type="number" id="oraclePort" name="oraclePort" placeholder="请输入端口" value="1521" />
              </div>
              <div class="form-group">
                <label for="oracleServiceName">服务名</label>
                <input type="text" id="oracleServiceName" name="oracleServiceName" placeholder="请输入服务名" />
              </div>
              <div class="form-group">
                <label for="oracleUsername">用户名</label>
                <input type="text" id="oracleUsername" name="oracleUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="oraclePassword">密码</label>
                <input type="password" id="oraclePassword" name="oraclePassword" placeholder="请输入密码" />
              </div>
            </div>
            <div id="fileConfig" style="display: none">
              <div class="form-group">
                <label for="filePath">文件路径</label>
                <input type="text" id="filePath" name="filePath" placeholder="请输入文件路径" />
              </div>
              <div class="form-group">
                <label for="fileFormat">文件格式</label>
                <select id="fileFormat" name="fileFormat">
                  <option value="csv">CSV</option>
                  <option value="json">JSON</option>
                  <option value="excel">Excel</option>
                  <option value="txt">文本</option>
                </select>
              </div>
              <div class="form-group">
                <label for="fileEncoding">编码</label>
                <input type="text" id="fileEncoding" name="fileEncoding" placeholder="请输入编码" value="utf-8" />
              </div>
            </div>
            <div id="ftpConfig" style="display: none">
              <div class="form-group">
                <label for="ftpHost">主机地址</label>
                <input type="text" id="ftpHost" name="ftpHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="ftpPort">端口</label>
                <input type="number" id="ftpPort" name="ftpPort" placeholder="请输入端口" value="21" />
              </div>
              <div class="form-group">
                <label for="ftpUsername">用户名</label>
                <input type="text" id="ftpUsername" name="ftpUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="ftpPassword">密码</label>
                <input type="password" id="ftpPassword" name="ftpPassword" placeholder="请输入密码" />
              </div>
              <div class="form-group">
                <label for="ftpPath">文件路径</label>
                <input type="text" id="ftpPath" name="ftpPath" placeholder="请输入文件路径" />
              </div>
            </div>
            <div id="apiConfig" style="display: none">
              <div class="form-group">
                <label for="apiUrl">API地址</label>
                <input type="text" id="apiUrl" name="apiUrl" placeholder="请输入API地址" />
              </div>
              <div class="form-group">
                <label for="apiMethod">请求方法</label>
                <select id="apiMethod" name="apiMethod">
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                </select>
              </div>
              <div class="form-group">
                <label for="apiHeaders">请求头</label>
                <textarea id="apiHeaders" name="apiHeaders" rows="3" placeholder="请输入请求头，JSON格式"></textarea>
              </div>
              <div class="form-group">
                <label for="apiParams">请求参数</label>
                <textarea id="apiParams" name="apiParams" rows="3" placeholder="请输入请求参数，JSON格式"></textarea>
              </div>
              <div class="form-group">
                <label for="apiAuthType">认证方式</label>
                <select id="apiAuthType" name="apiAuthType">
                  <option value="none">无认证</option>
                  <option value="basic">Basic Auth</option>
                  <option value="token">Token</option>
                </select>
              </div>
              <div id="apiBasicAuth" style="display: none">
                <div class="form-group">
                  <label for="apiUsername">用户名</label>
                  <input type="text" id="apiUsername" name="apiUsername" placeholder="请输入用户名" />
                </div>
                <div class="form-group">
                  <label for="apiPassword">密码</label>
                  <input type="password" id="apiPassword" name="apiPassword" placeholder="请输入密码" />
                </div>
              </div>
              <div id="apiTokenAuth" style="display: none">
                <div class="form-group">
                  <label for="apiToken">Token</label>
                  <input type="text" id="apiToken" name="apiToken" placeholder="请输入Token" />
                </div>
              </div>
            </div>
            <div id="kafkaConfig" style="display: none">
              <div class="form-group">
                <label for="kafkaBrokers">Broker列表</label>
                <input type="text" id="kafkaBrokers" name="kafkaBrokers" placeholder="请输入Broker列表，多个用逗号分隔" />
              </div>
              <div class="form-group">
                <label for="kafkaTopic">Topic</label>
                <input type="text" id="kafkaTopic" name="kafkaTopic" placeholder="请输入Topic" />
              </div>
              <div class="form-group">
                <label for="kafkaGroupId">消费组ID</label>
                <input type="text" id="kafkaGroupId" name="kafkaGroupId" placeholder="请输入消费组ID" />
              </div>
              <div class="form-group">
                <label for="kafkaAutoOffsetReset">Offset重置策略</label>
                <select id="kafkaAutoOffsetReset" name="kafkaAutoOffsetReset">
                  <option value="latest">latest</option>
                  <option value="earliest">earliest</option>
                  <option value="none">none</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="dsDescription">数据源描述</label>
              <textarea id="dsDescription" name="dsDescription" rows="3" placeholder="请输入数据源描述"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addDataSourceModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" id="testConnectionBtn" onclick="testConnectionAndSave()">测试连接并保存</button>
        </div>
      </div>
    </div>

    <!-- 编辑数据源模态框 -->
    <div class="modal" id="editDataSourceModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-edit"></i>
            编辑数据源
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="editDataSourceForm">
            <input type="hidden" id="editDataSourceId" name="editDataSourceId" />
            <div class="form-group">
              <label for="editDsName">数据源名称</label>
              <input type="text" id="editDsName" name="editDsName" required placeholder="请输入数据源名称" />
            </div>
            <div class="form-group">
              <label for="editDsType">数据源类型</label>
              <select id="editDsType" name="editDsType" required onchange="changeEditDataSourceForm(this.value)">
                <option value="mysql">MySQL</option>
                <option value="oracle">Oracle</option>
                <option value="file">文件</option>
                <option value="ftp">FTP</option>
                <option value="api">API</option>
                <option value="kafka">Kafka</option>
              </select>
            </div>
            <div id="editMysqlConfig" style="display: none">
              <div class="form-group">
                <label for="editMysqlHost">主机地址</label>
                <input type="text" id="editMysqlHost" name="editMysqlHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="editMysqlPort">端口</label>
                <input type="number" id="editMysqlPort" name="editMysqlPort" placeholder="请输入端口" value="3306" />
              </div>
              <div class="form-group">
                <label for="editMysqlDatabase">数据库名</label>
                <input type="text" id="editMysqlDatabase" name="editMysqlDatabase" placeholder="请输入数据库名" />
              </div>
              <div class="form-group">
                <label for="editMysqlUsername">用户名</label>
                <input type="text" id="editMysqlUsername" name="editMysqlUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="editMysqlPassword">密码</label>
                <input type="password" id="editMysqlPassword" name="editMysqlPassword" placeholder="请输入密码" />
              </div>
              <div class="form-group">
                <label for="editMysqlCharset">字符集</label>
                <input type="text" id="editMysqlCharset" name="editMysqlCharset" placeholder="请输入字符集" value="utf8mb4" />
              </div>
            </div>
            <div id="editOracleConfig" style="display: none">
              <div class="form-group">
                <label for="editOracleHost">主机地址</label>
                <input type="text" id="editOracleHost" name="editOracleHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="editOraclePort">端口</label>
                <input type="number" id="editOraclePort" name="editOraclePort" placeholder="请输入端口" value="1521" />
              </div>
              <div class="form-group">
                <label for="editOracleServiceName">服务名</label>
                <input type="text" id="editOracleServiceName" name="editOracleServiceName" placeholder="请输入服务名" />
              </div>
              <div class="form-group">
                <label for="editOracleUsername">用户名</label>
                <input type="text" id="editOracleUsername" name="editOracleUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="editOraclePassword">密码</label>
                <input type="password" id="editOraclePassword" name="editOraclePassword" placeholder="请输入密码" />
              </div>
            </div>
            <div id="editFileConfig" style="display: none">
              <div class="form-group">
                <label for="editFilePath">文件路径</label>
                <input type="text" id="editFilePath" name="editFilePath" placeholder="请输入文件路径" />
              </div>
              <div class="form-group">
                <label for="editFileFormat">文件格式</label>
                <select id="editFileFormat" name="editFileFormat">
                  <option value="csv">CSV</option>
                  <option value="json">JSON</option>
                  <option value="excel">Excel</option>
                  <option value="txt">文本</option>
                </select>
              </div>
              <div class="form-group">
                <label for="editFileEncoding">编码</label>
                <input type="text" id="editFileEncoding" name="editFileEncoding" placeholder="请输入编码" value="utf-8" />
              </div>
            </div>
            <div id="editFtpConfig" style="display: none">
              <div class="form-group">
                <label for="editFtpHost">主机地址</label>
                <input type="text" id="editFtpHost" name="editFtpHost" placeholder="请输入主机地址" />
              </div>
              <div class="form-group">
                <label for="editFtpPort">端口</label>
                <input type="number" id="editFtpPort" name="editFtpPort" placeholder="请输入端口" value="21" />
              </div>
              <div class="form-group">
                <label for="editFtpUsername">用户名</label>
                <input type="text" id="editFtpUsername" name="editFtpUsername" placeholder="请输入用户名" />
              </div>
              <div class="form-group">
                <label for="editFtpPassword">密码</label>
                <input type="password" id="editFtpPassword" name="editFtpPassword" placeholder="请输入密码" />
              </div>
              <div class="form-group">
                <label for="editFtpPath">文件路径</label>
                <input type="text" id="editFtpPath" name="editFtpPath" placeholder="请输入文件路径" />
              </div>
            </div>
            <div id="editApiConfig" style="display: none">
              <div class="form-group">
                <label for="editApiUrl">API地址</label>
                <input type="text" id="editApiUrl" name="editApiUrl" placeholder="请输入API地址" />
              </div>
              <div class="form-group">
                <label for="editApiMethod">请求方法</label>
                <select id="editApiMethod" name="editApiMethod">
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                </select>
              </div>
              <div class="form-group">
                <label for="editApiHeaders">请求头</label>
                <textarea id="editApiHeaders" name="editApiHeaders" rows="3" placeholder="请输入请求头，JSON格式"></textarea>
              </div>
              <div class="form-group">
                <label for="editApiParams">请求参数</label>
                <textarea id="editApiParams" name="editApiParams" rows="3" placeholder="请输入请求参数，JSON格式"></textarea>
              </div>
              <div class="form-group">
                <label for="editApiAuthType">认证方式</label>
                <select id="editApiAuthType" name="editApiAuthType">
                  <option value="none">无认证</option>
                  <option value="basic">Basic Auth</option>
                  <option value="token">Token</option>
                </select>
              </div>
              <div id="editApiBasicAuth" style="display: none">
                <div class="form-group">
                  <label for="editApiUsername">用户名</label>
                  <input type="text" id="editApiUsername" name="editApiUsername" placeholder="请输入用户名" />
                </div>
                <div class="form-group">
                  <label for="editApiPassword">密码</label>
                  <input type="password" id="editApiPassword" name="editApiPassword" placeholder="请输入密码" />
                </div>
              </div>
              <div id="editApiTokenAuth" style="display: none">
                <div class="form-group">
                  <label for="editApiToken">Token</label>
                  <input type="text" id="editApiToken" name="editApiToken" placeholder="请输入Token" />
                </div>
              </div>
            </div>
            <div id="editKafkaConfig" style="display: none">
              <div class="form-group">
                <label for="editKafkaBrokers">Broker列表</label>
                <input type="text" id="editKafkaBrokers" name="editKafkaBrokers" placeholder="请输入Broker列表，多个用逗号分隔" />
              </div>
              <div class="form-group">
                <label for="editKafkaTopic">Topic</label>
                <input type="text" id="editKafkaTopic" name="editKafkaTopic" placeholder="请输入Topic" />
              </div>
              <div class="form-group">
                <label for="editKafkaGroupId">消费组ID</label>
                <input type="text" id="editKafkaGroupId" name="editKafkaGroupId" placeholder="请输入消费组ID" />
              </div>
              <div class="form-group">
                <label for="editKafkaAutoOffsetReset">Offset重置策略</label>
                <select id="editKafkaAutoOffsetReset" name="editKafkaAutoOffsetReset">
                  <option value="latest">latest</option>
                  <option value="earliest">earliest</option>
                  <option value="none">none</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="editDsDescription">数据源描述</label>
              <textarea id="editDsDescription" name="editDsDescription" rows="3" placeholder="请输入数据源描述"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editDataSourceModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" id="editTestConnectionBtn" onclick="testEditConnectionAndSave()">测试连接并保存</button>
        </div>
      </div>
    </div>

    <!-- 数据完整性保障模态框 -->
    <div class="modal" id="addDataIntegrityModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-shield-alt"></i>
            流数据完整性校验
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addDataIntegrityForm">
            <div class="form-group">
              <label for="diDataSource">数据源</label>
              <select id="diDataSource" name="diDataSource" required>
                <option value="">请选择数据源</option>
                <option value="user_db">用户数据库</option>
                <option value="sales_db">销售数据库</option>
                <option value="log_file">日志文件</option>
                <option value="report_file">报表文件</option>
                <option value="third_party_api">第三方API</option>
              </select>
            </div>
            <div class="form-group">
              <label for="diShard">数据分片</label>
              <input type="number" id="diShard" name="diShard" placeholder="请输入数据分片数量" required />
            </div>
            <div class="form-group">
              <label for="diReplication">副本数量</label>
              <input type="number" id="diReplication" name="diReplication" placeholder="请输入副本数量" required />
            </div>
            <div class="form-group">
              <label for="diCheckpoint">检查点间隔(秒)</label>
              <input type="number" id="diCheckpoint" name="diCheckpoint" placeholder="请输入检查点间隔" required />
            </div>
            <div class="form-group">
              <label for="diDetails">完整性策略详情</label>
              <textarea id="diDetails" name="diDetails" rows="3" placeholder="请输入完整性策略详情" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addDataIntegrityModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="checkDataIntegrityAndSave()">保存</button>
        </div>
      </div>
    </div>

    <!-- 数据格式验证模态框 -->
    <div class="modal" id="addDataValidationModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-check-circle"></i>
            流数据基础格式验证
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addDataValidationForm">
            <div class="form-group">
              <label for="dvDataSource">数据源</label>
              <select id="dvDataSource" name="dvDataSource" required>
                <option value="">请选择数据源</option>
                <option value="user_db">用户数据库</option>
                <option value="sales_db">销售数据库</option>
                <option value="log_file">日志文件</option>
                <option value="report_file">报表文件</option>
                <option value="third_party_api">第三方API</option>
              </select>
            </div>
            <div class="form-group">
              <label for="dvDataType">数据类型</label>
              <select id="dvDataType" name="dvDataType" required>
                <option value="">请选择数据类型</option>
                <option value="string">字符串</option>
                <option value="number">数字</option>
                <option value="date">日期</option>
                <option value="boolean">布尔值</option>
                <option value="json">JSON对象</option>
              </select>
            </div>
            <div class="form-group">
              <label for="dvRegex">正则表达式</label>
              <input type="text" id="dvRegex" name="dvRegex" placeholder="请输入正则表达式" required />
            </div>
            <div class="form-group">
              <label for="dvFeatures">特征组合</label>
              <input type="text" id="dvFeatures" name="dvFeatures" placeholder="请输入特征组合" required />
            </div>
            <div class="form-group">
              <label for="dvDetails">验证规则详情</label>
              <textarea id="dvDetails" name="dvDetails" rows="3" placeholder="请输入验证规则详情" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addDataValidationModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="checkDataValidationAndSave()">保存</button>
        </div>
      </div>
    </div>

    <!-- 加密采集数据模态框 -->
    <div class="modal" id="encryptDataModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-lock"></i>
            加密实时采集数据
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="encryptDataForm">
            <div class="form-group">
              <label for="eDataSource">数据源</label>
              <select id="eDataSource" name="eDataSource" required>
                <option value="">请选择数据源</option>
                <option value="user_db">用户数据库</option>
                <option value="sales_db">销售数据库</option>
                <option value="log_file">日志文件</option>
                <option value="report_file">报表文件</option>
                <option value="third_party_api">第三方API</option>
              </select>
            </div>
            <div class="form-group">
              <label for="eAlgorithm">加密算法</label>
              <select id="eAlgorithm" name="eAlgorithm" required>
                <option value="">请选择加密算法</option>
                <option value="aes256">AES-256</option>
                <option value="rsa">RSA</option>
                <option value="sha256">SHA-256</option>
                <option value="md5">MD5</option>
              </select>
            </div>
            <div class="form-group">
              <label for="eKeyId">密钥ID</label>
              <input type="text" id="eKeyId" name="eKeyId" placeholder="请输入密钥ID" required />
            </div>
            <div class="form-group">
              <label for="eFields">加密字段</label>
              <input type="text" id="eFields" name="eFields" placeholder="请输入要加密的字段，多个用逗号分隔" required />
            </div>
            <div class="form-group">
              <label for="eDetails">加密配置详情</label>
              <textarea id="eDetails" name="eDetails" rows="3" placeholder="请输入加密配置详情" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('encryptDataModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="checkEncryptionAndSave()">保存</button>
        </div>
      </div>
    </div>

    <!-- 数据采集范围模态框 -->
    <div class="modal" id="addDataScopeModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-border-style"></i>
            数据采集范围管理
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addDataScopeForm">
            <div class="form-group">
              <label for="dsDataSource">数据源</label>
              <select id="dsDataSource" name="dsDataSource" required>
                <option value="">请选择数据源</option>
                <option value="user_db">用户数据库</option>
                <option value="sales_db">销售数据库</option>
                <option value="log_file">日志文件</option>
                <option value="report_file">报表文件</option>
                <option value="third_party_api">第三方API</option>
              </select>
            </div>
            <div class="form-group">
              <label for="dsTables">表/集合</label>
              <input type="text" id="dsTables" name="dsTables" placeholder="请输入表或集合名称，多个用逗号分隔" required />
            </div>
            <div class="form-group">
              <label for="dsFields">字段</label>
              <input type="text" id="dsFields" name="dsFields" placeholder="请输入字段名称，多个用逗号分隔，为空表示所有字段" required />
            </div>
            <div class="form-group">
              <label for="dsFilter">过滤条件</label>
              <input type="text" id="dsFilter" name="dsFilter" placeholder="请输入过滤条件" required />
            </div>
            <div class="form-group">
              <label for="dsFrequency">采集频率(秒)</label>
              <input type="number" id="dsFrequency" name="dsFrequency" placeholder="请输入采集频率" required />
            </div>
            <div class="form-group">
              <label for="dsDetails">采集范围详情</label>
              <textarea id="dsDetails" name="dsDetails" rows="3" placeholder="请输入采集范围详情" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addDataScopeModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="checkDataScopeAndSave()">保存</button>
        </div>
      </div>
    </div>

    <!-- 测试连接进度条弹窗 -->
    <div class="modal" id="testConnectionModal" style="background-color: rgba(0, 0, 0, 0.5)">
      <div class="modal-content" style="width: 400px; text-align: center">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plug"></i>
            测试连接
          </div>
          <button class="modal-close" onclick="document.getElementById('testConnectionModal').classList.remove('show')">&times;</button>
        </div>
        <div class="modal-body">
          <div style="margin: 20px 0">
            <div style="height: 10px; background-color: #e0e0e0; border-radius: 5px; overflow: hidden">
              <div id="connectionProgressBar" style="height: 100%; width: 0%; background-color: #4caf50; transition: width 0.3s ease-in-out"></div>
            </div>
            <div id="connectionStatus" style="margin-top: 10px; font-size: 14px">正在测试连接...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 流数据完整性检查进度条弹窗 -->
    <div class="modal" id="dataIntegrityCheckModal" style="background-color: rgba(0, 0, 0, 0.5)">
      <div class="modal-content" style="width: 400px; text-align: center">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-shield-alt"></i>
            检查流数据完整性
          </div>
          <button class="modal-close" onclick="document.getElementById('dataIntegrityCheckModal').classList.remove('show')">&times;</button>
        </div>
        <div class="modal-body">
          <div style="margin: 20px 0">
            <div style="height: 10px; background-color: #e0e0e0; border-radius: 5px; overflow: hidden">
              <div id="integrityProgressBar" style="height: 100%; width: 0%; background-color: #3498db; transition: width 0.3s ease-in-out"></div>
            </div>
            <div id="integrityStatusText" style="margin-top: 10px; font-size: 14px">开始检查...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 流数据格式验证进度条弹窗 -->
    <div class="modal" id="dataValidationCheckModal" style="background-color: rgba(0, 0, 0, 0.5)">
      <div class="modal-content" style="width: 400px; text-align: center">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-check-circle"></i>
            验证流数据格式
          </div>
          <button class="modal-close" onclick="document.getElementById('dataValidationCheckModal').classList.remove('show')">&times;</button>
        </div>
        <div class="modal-body">
          <div style="margin: 20px 0">
            <div style="height: 10px; background-color: #e0e0e0; border-radius: 5px; overflow: hidden">
              <div id="validationProgressBar" style="height: 100%; width: 0%; background-color: #9b59b6; transition: width 0.3s ease-in-out"></div>
            </div>
            <div id="validationStatusText" style="margin-top: 10px; font-size: 14px">开始验证...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加密进度条弹窗 -->
    <div class="modal" id="dataEncryptionCheckModal" style="background-color: rgba(0, 0, 0, 0.5)">
      <div class="modal-content" style="width: 400px; text-align: center">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-lock"></i>
            数据加密处理
          </div>
          <button class="modal-close" onclick="document.getElementById('dataEncryptionCheckModal').classList.remove('show')">&times;</button>
        </div>
        <div class="modal-body">
          <div style="margin: 20px 0">
            <div style="height: 10px; background-color: #e0e0e0; border-radius: 5px; overflow: hidden">
              <div id="encryptionProgressBar" style="height: 100%; width: 0%; background-color: #e74c3c; transition: width 0.3s ease-in-out"></div>
            </div>
            <div id="encryptionStatusText" style="margin-top: 10px; font-size: 14px">开始加密...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据采集范围进度条弹窗 -->
    <div class="modal" id="dataScopeCheckModal">
      <div class="modal-content" style="width: 400px">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-tasks"></i>
            数据采集范围管理配置中
          </div>
        </div>
        <div class="modal-body">
          <div style="text-align: center; margin: 20px 0">
            <div style="height: 10px; width: 100%; background-color: #f1f1f1; border-radius: 5px; overflow: hidden">
              <div id="scopeProgressBar" style="height: 100%; width: 0%; background-color: #2ecc71; transition: width 0.3s ease-in-out"></div>
            </div>
            <div id="scopeStatusText" style="margin-top: 10px; font-size: 14px">开始配置...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载覆盖层 -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-spinner"></div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // API基础URL
      const API_BASE_URL = 'http://localhost:8000/api';

      // 显示通知
      function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerText = message;
        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';
        }, 10);

        // 3秒后隐藏通知
        setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-20px)';
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }

      // 显示/隐藏加载状态
      function showLoading() {
        document.getElementById('loadingOverlay').classList.add('show');
      }

      function hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
      }

      // 封装fetch请求函数
      async function apiRequest(url, method, data = null) {
        try {
          const options = {
            method: method,
            headers: {
              'Content-Type': 'application/json',
              // 添加认证令牌等其他必要的请求头
              'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
            }
          };

          if (data) {
            options.body = JSON.stringify(data);
          }

          console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
          const response = await fetch(url, options);
          const result = await response.json();
          console.log(`接口响应:`, result);

          if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
          }

          return result;
        } catch (error) {
          console.error('API请求错误:', error);
          // 不处理响应结果，静默处理错误
          throw error;
        }
      }

      // 初始化页面数据
      async function initDataSourcePage() {
        try {
          console.log('初始化数据源页面...');
          // 调用页面访问记录接口
          await apiRequest(`${API_BASE_URL}/data-sources/page-visit`, 'POST', {
            action: 'page_load',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            pageUrl: window.location.href,
            referrer: document.referrer
          });
          console.log('页面访问记录接口调用成功');
          
          // 调用获取数据源列表接口
          await apiRequest(`${API_BASE_URL}/data-sources`, 'GET');
          console.log('数据源列表接口调用成功');
          
          // 调用获取数据源统计信息接口
          await apiRequest(`${API_BASE_URL}/data-sources/statistics`, 'GET');
          console.log('数据源统计信息接口调用成功');
          
          // 调用获取数据源类型列表接口
          await apiRequest(`${API_BASE_URL}/data-sources/types`, 'GET');
          console.log('数据源类型列表接口调用成功');
          
          // 调用获取连接状态接口
          await apiRequest(`${API_BASE_URL}/data-sources/connection-status`, 'GET');
          console.log('连接状态接口调用成功');
          
          // 调用获取数据源配置模板接口
          await apiRequest(`${API_BASE_URL}/data-sources/config-templates`, 'GET');
          console.log('数据源配置模板接口调用成功');
          
          // 调用获取数据源权限接口
          await apiRequest(`${API_BASE_URL}/data-sources/permissions`, 'GET');
          console.log('数据源权限接口调用成功');
          
          // 调用获取数据源监控接口
          await apiRequest(`${API_BASE_URL}/data-sources/monitoring`, 'GET');
          console.log('数据源监控接口调用成功');
          
          // 这里可以添加代码来更新表格数据，但保持原有逻辑
          console.log('页面初始化完成，保持原有渲染逻辑');
        } catch (error) {
          console.error('初始化页面失败:', error);
          // 即使接口调用失败，也不影响页面正常显示
        }
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', initDataSourcePage);

      // 页面加载时调用接口记录访问
      window.addEventListener('load', async function() {
        try {
          await apiRequest(`${API_BASE_URL}/data-sources/page-visit`, 'POST', {
            action: 'page_load',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            pageUrl: window.location.href,
            referrer: document.referrer
          });
          console.log('页面访问记录接口调用成功');
        } catch (error) {
          console.error('页面访问记录接口调用失败:', error);
          // 即使接口调用失败，也不影响页面正常加载
        }
      });

      // 测试连接并保存函数
      async function testConnectionAndSave() {
        const form = document.getElementById('addDataSourceForm');
        if (!validateForm('addDataSourceForm')) {
          return;
        }

        // 获取表单数据
        const dsName = document.getElementById('dsName').value;
        const dsType = document.getElementById('dsType').value;
        const dsDescription = document.getElementById('dsDescription').value;

        // 根据数据源类型获取相应的配置
        let connectionConfig = {};
        if (dsType === 'mysql') {
          connectionConfig = {
            host: document.getElementById('mysqlHost').value,
            port: document.getElementById('mysqlPort').value,
            database: document.getElementById('mysqlDatabase').value,
            username: document.getElementById('mysqlUsername').value,
            password: document.getElementById('mysqlPassword').value,
            charset: document.getElementById('mysqlCharset').value
          };
        } else if (dsType === 'oracle') {
          connectionConfig = {
            host: document.getElementById('oracleHost').value,
            port: document.getElementById('oraclePort').value,
            serviceName: document.getElementById('oracleServiceName').value,
            username: document.getElementById('oracleUsername').value,
            password: document.getElementById('oraclePassword').value
          };
        } else if (dsType === 'file') {
          connectionConfig = {
            path: document.getElementById('filePath').value,
            format: document.getElementById('fileFormat').value,
            encoding: document.getElementById('fileEncoding').value
          };
        } else if (dsType === 'ftp') {
          connectionConfig = {
            host: document.getElementById('ftpHost').value,
            port: document.getElementById('ftpPort').value,
            username: document.getElementById('ftpUsername').value,
            password: document.getElementById('ftpPassword').value,
            path: document.getElementById('ftpPath').value
          };
        } else if (dsType === 'api') {
          connectionConfig = {
            url: document.getElementById('apiUrl').value,
            method: document.getElementById('apiMethod').value,
            headers: document.getElementById('apiHeaders').value ? JSON.parse(document.getElementById('apiHeaders').value) : {},
            params: document.getElementById('apiParams').value ? JSON.parse(document.getElementById('apiParams').value) : {},
            authType: document.getElementById('apiAuthType').value
          };

          if (connectionConfig.authType === 'basic') {
            connectionConfig.auth = {
              username: document.getElementById('apiUsername').value,
              password: document.getElementById('apiPassword').value
            };
          } else if (connectionConfig.authType === 'token') {
            connectionConfig.auth = {
              token: document.getElementById('apiToken').value
            };
          }
        } else if (dsType === 'kafka') {
          connectionConfig = {
            brokers: document.getElementById('kafkaBrokers').value,
            topic: document.getElementById('kafkaTopic').value,
            groupId: document.getElementById('kafkaGroupId').value,
            autoOffsetReset: document.getElementById('kafkaAutoOffsetReset').value
          };
        }

        // 构造请求数据
        const requestData = {
          name: dsName,
          type: dsType,
          description: dsDescription,
          config: connectionConfig
        };

        // 显示测试连接模态框
        const modal = document.getElementById('testConnectionModal');
        modal.classList.add('show');

        // 获取进度条和状态元素
        const progressBar = document.getElementById('connectionProgressBar');
        const statusText = document.getElementById('connectionStatus');

        // 重置进度条
        progressBar.style.width = '0%';
        statusText.textContent = '正在测试连接...';

        try {
          // 测试连接
          const testResult = await apiRequest(`${API_BASE_URL}/data-sources/test-connection`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '连接测试成功！';

          // 延迟保存数据
          setTimeout(async () => {
            // 保存数据源
            const saveResult = await apiRequest(`${API_BASE_URL}/data-sources`, 'POST', requestData);
            modal.classList.remove('show');
            alert('数据源创建成功！');
            document.getElementById('addDataSourceModal').classList.remove('show');
            // 重置表单
            form.reset();
            changeDataSourceForm('');
            // 刷新数据源列表
            initDataSourcePage();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '连接测试失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }


      // 切换编辑数据源类型表单
      function changeEditDataSourceForm(type) {
        // 隐藏所有配置表单
        document.getElementById('editMysqlConfig').style.display = 'none';
        document.getElementById('editOracleConfig').style.display = 'none';
        document.getElementById('editFileConfig').style.display = 'none';
        document.getElementById('editFtpConfig').style.display = 'none';
        document.getElementById('editApiConfig').style.display = 'none';
        document.getElementById('editKafkaConfig').style.display = 'none';
        document.getElementById('editApiBasicAuth').style.display = 'none';
        document.getElementById('editApiTokenAuth').style.display = 'none';

        // 显示选中的配置表单
        if (type) {
          document.getElementById('edit' + type.charAt(0).toUpperCase() + type.slice(1) + 'Config').style.display = 'block';
        }
      }

      // 编辑表单API认证方式切换
      document.getElementById('editApiAuthType').addEventListener('change', function () {
        const authType = this.value;
        document.getElementById('editApiBasicAuth').style.display = 'none';
        document.getElementById('editApiTokenAuth').style.display = 'none';

        if (authType === 'basic') {
          document.getElementById('editApiBasicAuth').style.display = 'block';
        } else if (authType === 'token') {
          document.getElementById('editApiTokenAuth').style.display = 'block';
        }
      });

      // 加载数据源数据到编辑表单
      function loadDataSourceData(rowIndex) {
        // 获取表格行数据
        const table = document.querySelector('.table');
        const row = table.rows[rowIndex + 1]; // +1 是因为第一行是表头
        if (!row) return;

        // 获取行中的数据
        const dsName = row.cells[0].textContent;
        const dsType = row.cells[1].querySelector('.tag').textContent;
        const connectionInfo = row.cells[2].textContent;
        const status = row.cells[3].querySelector('.tag').textContent;
        const createTime = row.cells[4].textContent;

        // 填充表单
        document.getElementById('editDataSourceId').value = rowIndex;
        document.getElementById('editDsName').value = dsName;

        // 设置数据源类型
        const typeSelect = document.getElementById('editDsType');
        for (let i = 0; i < typeSelect.options.length; i++) {
          if (typeSelect.options[i].text === dsType) {
            typeSelect.selectedIndex = i;
            break;
          }
        }

        // 触发类型切换，显示对应配置表单
        changeEditDataSourceForm(typeSelect.value);

        // 根据数据源类型填充相应的连接信息
        if (dsType === 'MySQL') {
          document.getElementById('editMysqlHost').value = connectionInfo.split(':')[0];
          document.getElementById('editMysqlPort').value = connectionInfo.split(':')[1] || 3306;
        } else if (dsType === 'Oracle') {
          document.getElementById('editOracleHost').value = connectionInfo.split(':')[0];
          document.getElementById('editOraclePort').value = connectionInfo.split(':')[1] || 1521;
        } else if (dsType === '文件') {
          document.getElementById('editFilePath').value = connectionInfo;
        } else if (dsType === 'FTP') {
          document.getElementById('editFtpHost').value = connectionInfo.split(':')[0];
          document.getElementById('editFtpPort').value = connectionInfo.split(':')[1] || 21;
        } else if (dsType === 'API') {
          document.getElementById('editApiUrl').value = connectionInfo;
        } else if (dsType === 'Kafka') {
          document.getElementById('editKafkaBrokers').value = connectionInfo;
        }

        // 显示编辑模态框
        document.getElementById('editDataSourceModal').classList.add('show');
      }

      // 测试编辑连接并保存函数
      async function testEditConnectionAndSave() {
        const form = document.getElementById('editDataSourceForm');
        if (!validateForm('editDataSourceForm')) {
          return;
        }

        // 获取表单数据
        const dsId = document.getElementById('editDataSourceId').value;
        const dsName = document.getElementById('editDsName').value;
        const dsType = document.getElementById('editDsType').value;
        const dsDescription = document.getElementById('editDsDescription').value;

        // 根据数据源类型获取相应的配置
        let connectionConfig = {};
        if (dsType === 'mysql') {
          connectionConfig = {
            host: document.getElementById('editMysqlHost').value,
            port: document.getElementById('editMysqlPort').value,
            database: document.getElementById('editMysqlDatabase').value,
            username: document.getElementById('editMysqlUsername').value,
            password: document.getElementById('editMysqlPassword').value,
            charset: document.getElementById('editMysqlCharset').value
          };
        } else if (dsType === 'oracle') {
          connectionConfig = {
            host: document.getElementById('editOracleHost').value,
            port: document.getElementById('editOraclePort').value,
            serviceName: document.getElementById('editOracleServiceName').value,
            username: document.getElementById('editOracleUsername').value,
            password: document.getElementById('editOraclePassword').value
          };
        } else if (dsType === 'file') {
          connectionConfig = {
            path: document.getElementById('editFilePath').value,
            format: document.getElementById('editFileFormat').value,
            encoding: document.getElementById('editFileEncoding').value
          };
        } else if (dsType === 'ftp') {
          connectionConfig = {
            host: document.getElementById('editFtpHost').value,
            port: document.getElementById('editFtpPort').value,
            username: document.getElementById('editFtpUsername').value,
            password: document.getElementById('editFtpPassword').value,
            path: document.getElementById('editFtpPath').value
          };
        } else if (dsType === 'api') {
          connectionConfig = {
            url: document.getElementById('editApiUrl').value,
            method: document.getElementById('editApiMethod').value,
            headers: document.getElementById('editApiHeaders').value ? JSON.parse(document.getElementById('editApiHeaders').value) : {},
            params: document.getElementById('editApiParams').value ? JSON.parse(document.getElementById('editApiParams').value) : {},
            authType: document.getElementById('editApiAuthType').value
          };

          if (connectionConfig.authType === 'basic') {
            connectionConfig.auth = {
              username: document.getElementById('editApiUsername').value,
              password: document.getElementById('editApiPassword').value
            };
          } else if (connectionConfig.authType === 'token') {
            connectionConfig.auth = {
              token: document.getElementById('editApiToken').value
            };
          }
        } else if (dsType === 'kafka') {
          connectionConfig = {
            brokers: document.getElementById('editKafkaBrokers').value,
            topic: document.getElementById('editKafkaTopic').value,
            groupId: document.getElementById('editKafkaGroupId').value,
            autoOffsetReset: document.getElementById('editKafkaAutoOffsetReset').value
          };
        }

        // 构造请求数据
        const requestData = {
          name: dsName,
          type: dsType,
          description: dsDescription,
          config: connectionConfig
        };

        // 显示测试连接模态框
        const modal = document.getElementById('testConnectionModal');
        modal.classList.add('show');

        // 获取进度条和状态元素
        const progressBar = document.getElementById('connectionProgressBar');
        const statusText = document.getElementById('connectionStatus');

        // 重置进度条
        progressBar.style.width = '0%';
        statusText.textContent = '正在测试连接...';

        try {
          // 测试连接
          const testResult = await apiRequest(`${API_BASE_URL}/data-sources/test-connection`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '连接测试成功！';

          // 延迟保存数据
          setTimeout(async () => {
            // 更新数据源
            const updateResult = await apiRequest(`${API_BASE_URL}/data-sources/${dsId}`, 'PUT', requestData);
            modal.classList.remove('show');
            alert('数据源更新成功！');
            document.getElementById('editDataSourceModal').classList.remove('show');
            // 刷新数据源列表
            initDataSourcePage();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '连接测试失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }

      // 切换数据源类型表单
      function changeDataSourceForm(type) {
        // 隐藏所有配置表单
        document.getElementById('mysqlConfig').style.display = 'none';
        document.getElementById('oracleConfig').style.display = 'none';
        document.getElementById('fileConfig').style.display = 'none';
        document.getElementById('ftpConfig').style.display = 'none';
        document.getElementById('apiConfig').style.display = 'none';
        document.getElementById('kafkaConfig').style.display = 'none';
        document.getElementById('apiBasicAuth').style.display = 'none';
        document.getElementById('apiTokenAuth').style.display = 'none';

        // 显示选中的配置表单
        if (type) {
          document.getElementById(type + 'Config').style.display = 'block';
        }
      }

      // API认证方式切换
      document.getElementById('apiAuthType').addEventListener('change', function () {
        const authType = this.value;
        document.getElementById('apiBasicAuth').style.display = 'none';
        document.getElementById('apiTokenAuth').style.display = 'none';

        if (authType === 'basic') {
          document.getElementById('apiBasicAuth').style.display = 'block';
        } else if (authType === 'token') {
          document.getElementById('apiTokenAuth').style.display = 'block';
        }
      });

      // 新增数据源表单提交
      document.getElementById('addDataSourceForm').addEventListener('submit', function (e) {
        e.preventDefault();
        if (validateForm('addDataSourceForm')) {
          // 模拟提交成功
          alert('数据源创建成功！');
          document.getElementById('addDataSourceModal').classList.remove('show');
          // 重置表单
          this.reset();
          changeDataSourceForm('');
        }
      });

      // 新增采集数据源数据表单提交
      document.getElementById('addStreamCollectionForm').addEventListener('submit', function (e) {
        e.preventDefault();
        if (validateForm('addStreamCollectionForm')) {
          // 模拟提交成功
          alert('采集数据源数据配置成功！');
          document.getElementById('addStreamCollectionModal').classList.remove('show');
          // 重置表单
          this.reset();
        }
      });

      // 流数据完整性检查并保存
      async function checkDataIntegrityAndSave() {
        const form = document.getElementById('addDataIntegrityForm');
        if (!validateForm('addDataIntegrityForm')) {
          return;
        }

        // 获取表单数据
        const dataSource = document.getElementById('diDataSource').value;
        const shard = document.getElementById('diShard').value;
        const replication = document.getElementById('diReplication').value;
        const checkpoint = document.getElementById('diCheckpoint').value;
        const details = document.getElementById('diDetails').value;

        // 构造请求数据
        const requestData = {
          dataSource: dataSource,
          shard: parseInt(shard),
          replication: parseInt(replication),
          checkpoint: parseInt(checkpoint),
          details: details
        };

        const modal = document.getElementById('dataIntegrityCheckModal');
        const progressBar = document.getElementById('integrityProgressBar');
        const statusText = document.getElementById('integrityStatusText');

        // 显示进度条弹窗
        modal.classList.add('show');
        progressBar.style.width = '0%';
        statusText.textContent = '开始检查...';

        try {
          // 调用接口检查数据完整性
          const result = await apiRequest(`${API_BASE_URL}/data-integrity/check`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '流数据完整性检查通过！';

          // 延迟保存数据
          setTimeout(async () => {
            // 保存完整性配置
            const saveResult = await apiRequest(`${API_BASE_URL}/data-integrity/configs`, 'POST', requestData);
            modal.classList.remove('show');
            alert('流数据完整性校验配置成功！');
            document.getElementById('addDataIntegrityModal').classList.remove('show');
            // 重置表单
            form.reset();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '检查失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }

      // 流数据完整性保障表单提交
      document.getElementById('addDataIntegrityForm').addEventListener('submit', function (e) {
        e.preventDefault();
        checkDataIntegrityAndSave();
      });

      // 流数据格式验证并保存
      async function checkDataValidationAndSave() {
        const form = document.getElementById('addDataValidationForm');
        if (!validateForm('addDataValidationForm')) {
          return;
        }

        // 获取表单数据
        const dataSource = document.getElementById('dvDataSource').value;
        const dataType = document.getElementById('dvDataType').value;
        const regex = document.getElementById('dvRegex').value;
        const features = document.getElementById('dvFeatures').value;
        const details = document.getElementById('dvDetails').value;

        // 构造请求数据
        const requestData = {
          dataSource: dataSource,
          dataType: dataType,
          regex: regex,
          features: features,
          details: details
        };

        const modal = document.getElementById('dataValidationCheckModal');
        const progressBar = document.getElementById('validationProgressBar');
        const statusText = document.getElementById('validationStatusText');

        // 显示进度条弹窗
        modal.classList.add('show');
        progressBar.style.width = '0%';
        statusText.textContent = '开始验证...';

        try {
          // 调用接口验证数据格式
          const result = await apiRequest(`${API_BASE_URL}/data-validation/validate`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '流数据格式验证通过！';

          // 延迟保存数据
          setTimeout(async () => {
            // 保存验证配置
            const saveResult = await apiRequest(`${API_BASE_URL}/data-validation/configs`, 'POST', requestData);
            modal.classList.remove('show');
            alert('流数据基础格式验证配置成功！');
            document.getElementById('addDataValidationModal').classList.remove('show');
            // 重置表单
            form.reset();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '验证失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }

      // 流数据基础格式验证表单提交
      document.getElementById('addDataValidationForm').addEventListener('submit', function (e) {
        e.preventDefault();
        checkDataValidationAndSave();
      });

      // 加密数据并保存
      async function checkEncryptionAndSave() {
        const form = document.getElementById('encryptDataForm');
        if (!validateForm('encryptDataForm')) {
          return;
        }

        // 获取表单数据
        const dataSource = document.getElementById('eDataSource').value;
        const algorithm = document.getElementById('eAlgorithm').value;
        const keyId = document.getElementById('eKeyId').value;
        const fields = document.getElementById('eFields').value.split(',').map(field => field.trim());
        const details = document.getElementById('eDetails').value;

        // 构造请求数据
        const requestData = {
          dataSource: dataSource,
          algorithm: algorithm,
          keyId: keyId,
          fields: fields,
          details: details
        };

        const modal = document.getElementById('dataEncryptionCheckModal');
        const progressBar = document.getElementById('encryptionProgressBar');
        const statusText = document.getElementById('encryptionStatusText');

        // 显示进度条弹窗
        modal.classList.add('show');
        progressBar.style.width = '0%';
        statusText.textContent = '开始加密...';

        try {
          // 调用接口加密数据
          const result = await apiRequest(`${API_BASE_URL}/data-encryption/encrypt`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '数据加密成功！';

          // 延迟保存数据
          setTimeout(async () => {
            // 保存加密配置
            const saveResult = await apiRequest(`${API_BASE_URL}/data-encryption/configs`, 'POST', requestData);
            modal.classList.remove('show');
            alert('加密实时采集数据配置成功！');
            document.getElementById('encryptDataModal').classList.remove('show');
            // 重置表单
            form.reset();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '加密失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }

      // 加密实时采集数据表单提交
      document.getElementById('encryptDataForm').addEventListener('submit', function (e) {
        e.preventDefault();
        checkEncryptionAndSave();
      });

      // 数据采集范围配置并保存
      async function checkDataScopeAndSave() {
        const form = document.getElementById('addDataScopeForm');
        if (!validateForm('addDataScopeForm')) {
          return;
        }

        // 获取表单数据
        const dataSource = document.getElementById('dsDataSource').value;
        const tables = document.getElementById('dsTables').value.split(',').map(table => table.trim());
        const fields = document.getElementById('dsFields').value ? document.getElementById('dsFields').value.split(',').map(field => field.trim()) : [];
        const filter = document.getElementById('dsFilter').value;
        const frequency = document.getElementById('dsFrequency').value;
        const details = document.getElementById('dsDetails').value;

        // 构造请求数据
        const requestData = {
          dataSource: dataSource,
          tables: tables,
          fields: fields,
          filter: filter,
          frequency: parseInt(frequency),
          details: details
        };

        const modal = document.getElementById('dataScopeCheckModal');
        const progressBar = document.getElementById('scopeProgressBar');
        const statusText = document.getElementById('scopeStatusText');

        // 显示进度条弹窗
        modal.classList.add('show');
        progressBar.style.width = '0%';
        statusText.textContent = '开始配置...';

        try {
          // 调用接口配置采集范围
          const result = await apiRequest(`${API_BASE_URL}/data-collection/scope/configure`, 'POST', requestData);
          progressBar.style.width = '100%';
          statusText.textContent = '数据采集范围配置成功！';

          // 延迟关闭弹窗
          setTimeout(() => {
            modal.classList.remove('show');
            alert('数据采集范围管理配置成功！');
            document.getElementById('addDataScopeModal').classList.remove('show');
            // 重置表单
            form.reset();
          }, 1000);
        } catch (error) {
          progressBar.style.width = '100%';
          progressBar.style.backgroundColor = '#f44336';
          statusText.textContent = '配置失败: ' + error.message;

          setTimeout(() => {
            modal.classList.remove('show');
          }, 2000);
        }
      }

      // 数据采集范围管理表单提交
      document.getElementById('addDataScopeForm').addEventListener('submit', function (e) {
        e.preventDefault();
        checkDataScopeAndSave();
      });



      // 删除数据源二次确认
      function confirmDeleteDataSource(rowIndex) {
        if (confirm('确定要删除这个数据源吗？此操作不可撤销！')) {
          deleteDataSource(rowIndex);
        }
      }

      // 删除数据源
      async function deleteDataSource(rowIndex) {
        const table = document.querySelector('.table');
        const row = table.rows[rowIndex + 1]; // +1 是因为第一行是表头
        if (!row) return;

        // 获取数据源ID（假设rowIndex就是ID）
        const dsId = rowIndex;

        try {
          // 调用接口删除数据源
          await apiRequest(`${API_BASE_URL}/data-sources/${dsId}`, 'DELETE');

          // 添加删除动画
          row.style.backgroundColor = '#ffebee';
          row.style.transition = 'all 0.3s ease';

          setTimeout(() => {
            row.style.height = row.offsetHeight + 'px';
            row.style.overflow = 'hidden';

            setTimeout(() => {
              row.style.height = '0';
              row.style.padding = '0';
              row.style.margin = '0';
              row.style.border = 'none';

              setTimeout(() => {
                row.remove();
                alert('数据源删除成功！');
              }, 300);
            }, 10);
          }, 300);
        } catch (error) {
          alert('删除数据源失败: ' + error.message);
        }
      }



      // 刷新表格数据
      function refreshTable() {
        const table = document.querySelector('.table tbody');
        if (table) {
          // 清空现有数据
          table.innerHTML = '';
          
          // 重新加载表格数据（保持原有数据）
          const rows = [
            {
              name: '用户数据库',
              type: 'MySQL',
              connection: 'localhost:3306',
              status: '已连接',
              createTime: '2023-07-01 10:30'
            },
            {
              name: '销售数据库',
              type: 'Oracle',
              connection: '*************:1521',
              status: '已连接',
              createTime: '2023-07-02 14:15'
            },
            {
              name: '日志文件',
              type: '文件',
              connection: '/data/logs/app.log',
              status: '已连接',
              createTime: '2023-07-03 09:45'
            },
            {
              name: '报表文件',
              type: 'FTP',
              connection: 'ftp.example.com:21',
              status: '未连接',
              createTime: '2023-07-05 16:20'
            },
            {
              name: '第三方API',
              type: 'API',
              connection: 'https://api.example.com',
              status: '已连接',
              createTime: '2023-07-08 11:05'
            },
            {
              name: '实时数据流',
              type: 'Kafka',
              connection: '192.168.1.101:9092',
              status: '已连接',
              createTime: '2023-07-10 15:30'
            }
          ];
          
          // 重新渲染表格行
          rows.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
              <td>${row.name}</td>
              <td><span class="tag tag-${getTagClass(row.type)}">${row.type}</span></td>
              <td>${row.connection}</td>
              <td><span class="tag tag-${getStatusClass(row.status)}">${row.status}</span></td>
              <td>${row.createTime}</td>
              <td>
                <button class="btn" style="color: var(--primary-color)" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color)"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
              </td>
            `;
            table.appendChild(tr);
          });
        }
      }

      // 获取标签样式类
      function getTagClass(type) {
        const typeMap = {
          'MySQL': 'info',
          'Oracle': 'warning',
          '文件': 'primary',
          'FTP': 'danger',
          'API': 'success',
          'Kafka': 'info'
        };
        return typeMap[type] || 'info';
      }

      // 获取状态样式类
      function getStatusClass(status) {
        return status === '已连接' ? 'success' : 'warning';
      }





      // 为所有模态框的取消按钮添加点击事件
      document.querySelectorAll('.modal .close, .modal .btn-secondary').forEach(button => {
        button.addEventListener('click', function() {
          const modal = this.closest('.modal');
          if (modal) {
            modal.classList.remove('show');
          }
        });
      });

      // 为所有连接按钮添加点击事件
      document.querySelectorAll('.btn-connect').forEach(button => {
        button.addEventListener('click', async function() {
          const rowIndex = this.getAttribute('data-index');
          console.log(`连接数据源, 索引: ${rowIndex}`);

          try {
            // 调用接口连接数据源
            const result = await apiRequest(`${API_BASE_URL}/data-sources/${rowIndex}/connect`, 'POST');
            console.log('数据源连接成功');
            alert('数据源连接成功！');
          } catch (error) {
            console.error('数据源连接失败:', error);
            alert('数据源连接失败: ' + error.message);
          }
        });
      });

      // 初始化编辑按钮点击事件
      function initEditButtons() {
        console.log('初始化编辑按钮事件...');
        
        // 为表格中的编辑按钮添加事件
        document.querySelectorAll('[data-modal-target="editDataSourceModal"]').forEach((button, index) => {
          console.log(`找到编辑按钮 ${index}:`, button);
          button.addEventListener('click', async function () {
            console.log(`编辑按钮 ${index} 被点击`);
            try {
              // 调用获取数据源详情接口
              await apiRequest(`${API_BASE_URL}/data-sources/${index}`, 'GET');
              console.log('获取数据源详情成功');
            } catch (error) {
              console.error('获取数据源详情失败:', error);
            }
            loadDataSourceData(index);
          });
        });
        
        // 为其他编辑按钮添加事件（如果有的话）
        document.querySelectorAll('.btn-edit').forEach(button => {
          button.addEventListener('click', async function() {
            const rowIndex = this.getAttribute('data-index');
            console.log(`编辑数据源, 索引: ${rowIndex}`);
            
            try {
              // 调用获取数据源详情接口
              await apiRequest(`${API_BASE_URL}/data-sources/${rowIndex}`, 'GET');
              console.log('获取数据源详情成功');
            } catch (error) {
              console.error('获取数据源详情失败:', error);
            }
            
            // 这里可以添加代码来加载数据源数据到编辑表单
            loadDataSourceToEditForm(rowIndex);
            document.getElementById('editDataSourceModal').classList.add('show');
          });
        });
        
        console.log('编辑按钮事件初始化完成');
      }

      // 初始化删除按钮点击事件
      function initDeleteButtons() {
        console.log('初始化删除按钮事件...');
        
        // 为表格中的删除按钮添加事件
        document.querySelectorAll('button .fa-trash').forEach((icon, index) => {
          console.log(`找到删除按钮 ${index}:`, icon.parentElement);
          icon.parentElement.addEventListener('click', async function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log(`删除按钮 ${index} 被点击`);
            try {
              // 调用删除数据源接口
              await apiRequest(`${API_BASE_URL}/data-sources/${index}`, 'DELETE');
              console.log('删除数据源成功');
            } catch (error) {
              console.error('删除数据源失败:', error);
            }
            confirmDeleteDataSource(index);
          });
        });
        
        // 为其他删除按钮添加事件（如果有的话）
        document.querySelectorAll('.btn-delete').forEach(button => {
          button.addEventListener('click', async function() {
            const rowIndex = this.getAttribute('data-index');
            if (confirm('确定要删除这个数据源吗？')) {
              try {
                // 调用删除数据源接口
                await apiRequest(`${API_BASE_URL}/data-sources/${rowIndex}`, 'DELETE');
                console.log('删除数据源成功');
              } catch (error) {
                console.error('删除数据源失败:', error);
              }
              deleteDataSource(rowIndex);
            }
          });
        });
        
        console.log('删除按钮事件初始化完成');
      }

      // 重写初始化数据源页面函数，添加按钮初始化
      async function initDataSourcePage() {
        try {
          console.log('初始化数据源页面...');
          // 调用接口获取数据源列表
          const dataSources = await apiRequest(`${API_BASE_URL}/data-sources`, 'GET');
          console.log('数据源列表:', dataSources);
          // 这里可以添加代码来更新表格数据

          // 初始化按钮事件
          setTimeout(() => {
            console.log('开始初始化按钮事件...');
            initEditButtons();
            initDeleteButtons();
            initSearchButtons();
            initTopButtons();
            initConnectButtons();
            initPaginationButtons();
            console.log('按钮事件初始化完成');
          }, 100);
        } catch (error) {
          console.error('初始化页面失败:', error);
        }
      }

      // 初始化顶部按钮事件
      function initTopButtons() {
        console.log('初始化顶部按钮事件...');
        
        // 新增数据源按钮
        const addDataSourceBtn = document.querySelector('[data-modal-target="addDataSourceModal"]');
        console.log('新增数据源按钮元素:', addDataSourceBtn);
        if (addDataSourceBtn) {
          addDataSourceBtn.addEventListener('click', async function() {
            console.log('新增数据源按钮被点击');
            try {
              // 调用获取数据源类型列表接口
              await apiRequest(`${API_BASE_URL}/data-sources/types`, 'GET');
              console.log('获取数据源类型列表成功');
            } catch (error) {
              console.error('获取数据源类型列表失败:', error);
            }
            document.getElementById('addDataSourceModal').classList.add('show');
          });
          console.log('新增数据源按钮事件监听器已添加');
        } else {
          console.error('未找到新增数据源按钮元素');
        }

        // 数据完整性校验按钮
        const dataIntegrityBtn = document.querySelector('[data-modal-target="addDataIntegrityModal"]');
        console.log('数据完整性校验按钮元素:', dataIntegrityBtn);
        if (dataIntegrityBtn) {
          dataIntegrityBtn.addEventListener('click', async function() {
            console.log('数据完整性校验按钮被点击');
            try {
              // 调用获取数据完整性配置接口
              await apiRequest(`${API_BASE_URL}/data-integrity/configs`, 'GET');
              console.log('获取数据完整性配置成功');
            } catch (error) {
              console.error('获取数据完整性配置失败:', error);
            }
            document.getElementById('addDataIntegrityModal').classList.add('show');
          });
          console.log('数据完整性校验按钮事件监听器已添加');
        } else {
          console.error('未找到数据完整性校验按钮元素');
        }

        // 数据格式验证按钮
        const dataValidationBtn = document.querySelector('[data-modal-target="addDataValidationModal"]');
        console.log('数据格式验证按钮元素:', dataValidationBtn);
        if (dataValidationBtn) {
          dataValidationBtn.addEventListener('click', async function() {
            console.log('数据格式验证按钮被点击');
            try {
              // 调用获取数据验证配置接口
              await apiRequest(`${API_BASE_URL}/data-validation/configs`, 'GET');
              console.log('获取数据验证配置成功');
            } catch (error) {
              console.error('获取数据验证配置失败:', error);
            }
            document.getElementById('addDataValidationModal').classList.add('show');
          });
          console.log('数据格式验证按钮事件监听器已添加');
        } else {
          console.error('未找到数据格式验证按钮元素');
        }

        // 加密采集数据按钮
        const encryptDataBtn = document.querySelector('[data-modal-target="encryptDataModal"]');
        console.log('加密采集数据按钮元素:', encryptDataBtn);
        if (encryptDataBtn) {
          encryptDataBtn.addEventListener('click', async function() {
            console.log('加密采集数据按钮被点击');
            try {
              // 调用获取加密配置接口
              await apiRequest(`${API_BASE_URL}/data-encryption/configs`, 'GET');
              console.log('获取加密配置成功');
            } catch (error) {
              console.error('获取加密配置失败:', error);
            }
            document.getElementById('encryptDataModal').classList.add('show');
          });
          console.log('加密采集数据按钮事件监听器已添加');
        } else {
          console.error('未找到加密采集数据按钮元素');
        }

        // 数据采集范围管理按钮
        const dataScopeBtn = document.querySelector('[data-modal-target="addDataScopeModal"]');
        console.log('数据采集范围管理按钮元素:', dataScopeBtn);
        if (dataScopeBtn) {
          dataScopeBtn.addEventListener('click', async function() {
            console.log('数据采集范围管理按钮被点击');
            try {
              // 调用获取数据采集范围配置接口
              await apiRequest(`${API_BASE_URL}/data-collection/scope/configs`, 'GET');
              console.log('获取数据采集范围配置成功');
            } catch (error) {
              console.error('获取数据采集范围配置失败:', error);
            }
            document.getElementById('addDataScopeModal').classList.add('show');
          });
          console.log('数据采集范围管理按钮事件监听器已添加');
        } else {
          console.error('未找到数据采集范围管理按钮元素');
        }
      }

      // 初始化连接按钮事件
      function initConnectButtons() {
        console.log('初始化连接按钮事件...');
        
        // 为表格中的连接按钮添加事件
        document.querySelectorAll('button .fa-link').forEach((icon, index) => {
          console.log(`找到连接按钮 ${index}:`, icon.parentElement);
          icon.parentElement.addEventListener('click', async function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log(`连接按钮 ${index} 被点击`);
            try {
              // 调用连接数据源接口
              await apiRequest(`${API_BASE_URL}/data-sources/${index}/connect`, 'POST');
              console.log('数据源连接成功');
            } catch (error) {
              console.error('数据源连接失败:', error);
            }
          });
        });
        
        console.log('连接按钮事件初始化完成');
      }

      // 初始化分页按钮事件
      function initPaginationButtons() {
        console.log('初始化分页按钮事件...');
        
        // 为分页按钮添加点击事件
        document.querySelectorAll('.pagination-item').forEach((item, index) => {
          console.log(`找到分页按钮 ${index}:`, item);
          item.addEventListener('click', async function() {
            if (this.classList.contains('active')) return;
            
            console.log(`分页按钮 ${index} 被点击`);
            try {
              // 调用获取分页数据接口
              const page = this.textContent;
              await apiRequest(`${API_BASE_URL}/data-sources?page=${page}&size=10`, 'GET');
              console.log(`获取第${page}页数据成功`);
            } catch (error) {
              console.error('获取分页数据失败:', error);
            }
            
            // 更新分页状态
            document.querySelectorAll('.pagination-item').forEach(item => item.classList.remove('active'));
            this.classList.add('active');
          });
        });
        
        console.log('分页按钮事件初始化完成');
      }

      // 初始化搜索相关按钮事件
      function initSearchButtons() {
        console.log('初始化搜索按钮事件...');
        
        // 为查询按钮添加点击事件
        const searchBtn = document.getElementById('btnSearchDataSource');
        console.log('查询按钮元素:', searchBtn);
        
        if (searchBtn) {
          searchBtn.addEventListener('click', async function() {
            console.log('查询按钮被点击');
            const searchInput = document.getElementById('searchDataSourceInput');
            const keyword = searchInput.value.trim();
            const typeSelect = document.querySelector('select[style*="padding: 6px 12px"]');
            const type = typeSelect.value;

            console.log('搜索关键词:', keyword);
            console.log('搜索类型:', type);

            try {
              // 调用搜索数据源接口 - 将参数作为URL查询参数
              const params = new URLSearchParams({
                keyword: keyword,
                type: type
              });
              await apiRequest(`${API_BASE_URL}/data-sources/search?${params.toString()}`, 'GET');

              console.log('搜索接口调用成功');
              // 不处理响应结果，直接刷新表格
              refreshTable();
                          } catch (error) {
                console.error('搜索失败:', error);
                // 即使接口调用失败，也刷新表格
                refreshTable();
              }
          });
          console.log('查询按钮事件监听器已添加');
        } else {
          console.error('未找到查询按钮元素');
        }

        // 为搜索框添加回车键事件
        const searchInput = document.getElementById('searchDataSourceInput');
        console.log('搜索框元素:', searchInput);
        
        if (searchInput) {
          searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
              console.log('搜索框回车键被按下');
              const keyword = searchInput.value.trim();
              const typeSelect = document.querySelector('select[style*="padding: 6px 12px"]');
              const type = typeSelect.value;

              // 调用搜索接口并刷新表格
              (async function() {
                try {
                  const params = new URLSearchParams({
                    keyword: keyword,
                    type: type
                  });
                  await apiRequest(`${API_BASE_URL}/data-sources/search?${params.toString()}`, 'GET');
                  console.log('搜索接口调用成功');
                  refreshTable();
                                  } catch (error) {
                    console.error('搜索失败:', error);
                    refreshTable();
                  }
              })();
            }
          });
          console.log('搜索框事件监听器已添加');
        } else {
          console.error('未找到搜索框元素');
        }
      }

      // 为删除按钮添加点击事件
      document.querySelectorAll('button .fa-trash').forEach((icon, index) => {
                  icon.parentElement.addEventListener('click', async function (e) {
            e.preventDefault();
            e.stopPropagation();
            try {
              // 调用删除数据源接口
              await apiRequest(`${API_BASE_URL}/data-sources/${index}`, 'DELETE');
              console.log('删除数据源成功');
            } catch (error) {
              console.error('删除数据源失败:', error);
            }
            confirmDeleteDataSource(index);
          });
      });




    </script>
  </body>
</html>
