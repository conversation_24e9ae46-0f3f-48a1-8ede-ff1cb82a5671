<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 首页</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item active" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-home page-title-icon"></i>
        数据概览
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item active">数据概览</div>
      </div>

      <!-- 时间选择器 -->
      <div class="date-picker">
        <label for="dateRange">时间范围:</label>
        <select id="dateRange" style="width: auto; margin-right: 12px">
          <option value="today">今日</option>
          <option value="yesterday">昨日</option>
          <option value="7days" selected>近7天</option>
          <option value="30days">近30天</option>
          <option value="custom">自定义</option>
        </select>
        <button class="btn btn-primary">
          <i class="fas fa-search"></i>
          查询
        </button>
      </div>

      <!-- 统计卡片 -->
      <div class="row">
        <div class="col col-3">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">128</div>
              <div class="stat-label">数据源数量</div>
            </div>
          </div>
        </div>
        <div class="col col-3">
          <div class="stat-card">
            <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: var(--success-color)">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">56</div>
              <div class="stat-label">运行中任务</div>
            </div>
          </div>
        </div>
        <div class="col col-3">
          <div class="stat-card">
            <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: var(--warning-color)">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">1.2T</div>
              <div class="stat-label">数据处理量</div>
            </div>
          </div>
        </div>
        <div class="col col-3">
          <div class="stat-card">
            <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: var(--danger-color)">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">3</div>
              <div class="stat-label">告警数量</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="row">
        <div class="col col-6">
          <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
              <h3 style="font-size: 18px; font-weight: 600">数据采集趋势</h3>
              <div class="dropdown">
                <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
                <div class="dropdown-menu">
                  <div class="dropdown-item">导出数据</div>
                  <div class="dropdown-item">刷新</div>
                  <div class="dropdown-item">设置</div>
                </div>
              </div>
            </div>
            <div class="chart-container">
              <canvas id="dataCollectionChart"></canvas>
            </div>
          </div>
        </div>
        <div class="col col-6">
          <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
              <h3 style="font-size: 18px; font-weight: 600">数据源分布</h3>
              <div class="dropdown">
                <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
                <div class="dropdown-menu">
                  <div class="dropdown-item">导出数据</div>
                  <div class="dropdown-item">刷新</div>
                  <div class="dropdown-item">设置</div>
                </div>
              </div>
            </div>
            <div class="chart-container">
              <canvas id="dataSourceChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近任务表格 -->
      <div class="card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
          <h3 style="font-size: 18px; font-weight: 600">最近任务</h3>
          <button class="btn btn-primary" data-modal-target="addTaskModal">
            <i class="fas fa-plus"></i>
            新建任务
          </button>
        </div>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>任务名称</th>
                <th>任务类型</th>
                <th>数据源</th>
                <th>状态</th>
                <th>开始时间</th>
                <th>结束时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>用户行为数据采集</td>
                <td>实时采集</td>
                <td>Kafka</td>
                <td><span class="tag tag-success">运行中</span></td>
                <td>2023-07-15 08:00</td>
                <td>-</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--warning-color)"><i class="fas fa-pause"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>销售数据同步</td>
                <td>离线采集</td>
                <td>MySQL</td>
                <td><span class="tag tag-success">已完成</span></td>
                <td>2023-07-14 20:00</td>
                <td>2023-07-14 20:30</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-redo"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>日志数据导入</td>
                <td>离线采集</td>
                <td>文件</td>
                <td><span class="tag tag-danger">失败</span></td>
                <td>2023-07-14 18:00</td>
                <td>2023-07-14 18:15</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-redo"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>产品库存监控</td>
                <td>实时采集</td>
                <td>API</td>
                <td><span class="tag tag-success">运行中</span></td>
                <td>2023-07-14 00:00</td>
                <td>-</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--warning-color)"><i class="fas fa-pause"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>客户信息同步</td>
                <td>离线采集</td>
                <td>Oracle</td>
                <td><span class="tag tag-warning">待执行</span></td>
                <td>2023-07-15 20:00</td>
                <td>-</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color)"><i class="fas fa-play"></i></button>
                  <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal" id="addTaskModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            新建任务
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addTaskForm">
            <div class="form-group">
              <label for="taskName">任务名称</label>
              <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称" />
            </div>
            <div class="form-group">
              <label for="taskType">任务类型</label>
              <select id="taskType" name="taskType" required>
                <option value="">请选择任务类型</option>
                <option value="realtime">实时采集</option>
                <option value="offline">离线采集</option>
              </select>
            </div>
            <div class="form-group">
              <label for="dataSource">数据源</label>
              <select id="dataSource" name="dataSource" required>
                <option value="">请选择数据源</option>
                <option value="mysql">MySQL</option>
                <option value="oracle">Oracle</option>
                <option value="file">文件</option>
                <option value="ftp">FTP</option>
                <option value="api">API</option>
                <option value="kafka">Kafka</option>
              </select>
            </div>
            <div class="form-group">
              <label for="taskTime">执行时间</label>
              <input type="datetime-local" id="taskTime" name="taskTime" required />
            </div>
            <div class="form-group">
              <label for="taskDesc">任务描述</label>
              <textarea id="taskDesc" name="taskDesc" rows="4" placeholder="请输入任务描述"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addTaskModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('addTaskForm').submit()">确认</button>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 初始化图表
      document.addEventListener('DOMContentLoaded', function () {
        // GET请求
        apiRequest('/get', 'GET', { id: 1 })
          .then(data => console.log(data))
          .catch(error => console.error(error));

        // POST请求
        apiRequest('/post', 'POST', { name: '测试', value: '123' })
          .then(data => console.log(data))
          .catch(error => console.error(error));
        // 数据采集趋势图表
        const collectionCtx = document.getElementById('dataCollectionChart').getContext('2d');
        const collectionChart = new Chart(collectionCtx, {
          type: 'line',
          data: {
            labels: ['7/10', '7/11', '7/12', '7/13', '7/14', '7/15', '7/16'],
            datasets: [
              {
                label: '实时采集',
                data: [120, 190, 150, 220, 280, 320, 350],
                borderColor: '#1890ff',
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                tension: 0.3,
                fill: true,
              },
              {
                label: '离线采集',
                data: [80, 100, 90, 130, 150, 170, 160],
                borderColor: '#52c41a',
                backgroundColor: 'rgba(82, 196, 26, 0.1)',
                tension: 0.3,
                fill: true,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top',
              },
              tooltip: {
                mode: 'index',
                intersect: false,
              },
            },
            scales: {
              y: {
                beginAtZero: true,
              },
            },
          },
        });

        // 数据源分布图表
        const sourceCtx = document.getElementById('dataSourceChart').getContext('2d');
        const sourceChart = new Chart(sourceCtx, {
          type: 'pie',
          data: {
            labels: ['MySQL', 'Oracle', '文件', 'FTP', 'API', 'Kafka'],
            datasets: [
              {
                data: [35, 20, 15, 10, 12, 8],
                backgroundColor: ['#1890ff', '#52c41a', '#faad14', '#ff4d4f', '#722ed1', '#13c2c2'],
                borderWidth: 0,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right',
              },
            },
          },
        });

        // 添加任务表单提交
        document.getElementById('addTaskForm').addEventListener('submit', function (e) {
          e.preventDefault();
          if (validateForm('addTaskForm')) {
            // 模拟提交成功
            alert('任务创建成功！');
            document.getElementById('addTaskModal').classList.remove('show');
            // 重置表单
            this.reset();
          }
        });
      });
    </script>
  </body>
</html>
