<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 指标血缘管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/vis-network/standalone/umd/vis-network.min.js"></script>
  <style>
    .bloodline-container {
      height: 600px;
      width: 100%;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      background-color: #f9f9f9;
    }
    .control-panel {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 16px;
      margin-bottom: 20px;
    }
    .detail-panel {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 16px;
      margin-top: 20px;
      max-height: 400px;
      overflow-y: auto;
    }
    .search-container {
      display: flex;
      margin-bottom: 16px;
    }
    .search-input {
      flex: 1;
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 4px 0 0 4px;
      font-size: 14px;
    }
    .search-btn {
      padding: 8px 16px;
      background-color: #4096ff;
      color: white;
      border: none;
      border-radius: 0 4px 4px 0;
      cursor: pointer;
    }
    .search-btn:hover {
      background-color: #3086e8;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #eee;
      margin-bottom: 16px;
    }
    .tab {
      padding: 8px 16px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    .tab.active {
      border-bottom-color: #4096ff;
      color: #4096ff;
      font-weight: 600;
    }
    .panel-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    }
    .info-item {
      margin-bottom: 16px;
    }
    .info-label {
      font-weight: 600;
      margin-bottom: 4px;
      color: #666;
    }
    .info-value {
      word-break: break-all;
    }
    .btn-group {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }
    .btn {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      border: none;
      font-size: 14px;
    }
    .btn-primary {
      background-color: #4096ff;
      color: white;
    }
    .btn-primary:hover {
      background-color: #3086e8;
    }
    .btn-secondary {
      background-color: #f0f0f0;
      color: #666;
    }
    .btn-secondary:hover {
      background-color: #e0e0e0;
    }
    .legend {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

    <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
           <div class="menu-item child " data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child  active" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" >
            <div class="menu-item child active" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>


     <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-project-diagram page-title-icon"></i>
      视图交互 - 指标血缘管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">视图交互</a></div>
      <div class="breadcrumb-item active">指标血缘管理</div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="search-container">
        <input type="text" class="search-input" id="indicatorSearch" placeholder="搜索指标...">
        <button class="search-btn" id="searchBtn"><i class="fas fa-search"></i> 搜索</button>
      </div>
      <div class="btn-group">
        <button class="btn btn-primary" id="expandAllBtn"><i class="fas fa-expand-alt"></i> 全部展开</button>
        <button class="btn btn-primary" id="collapseAllBtn"><i class="fas fa-compress-alt"></i> 全部收起</button>
        <button class="btn btn-primary" id="refreshBtn"><i class="fas fa-sync-alt"></i> 刷新</button>
        <button class="btn btn-primary" id="exportBtn"><i class="fas fa-download"></i> 导出</button>
        <button class="btn btn-secondary" id="layoutBtn"><i class="fas fa-th"></i> 布局切换</button>
        <button class="btn btn-secondary" id="filterBtn"><i class="fas fa-filter"></i> 筛选</button>
      </div>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color" style="background-color: #4096ff;"></div>
          <span>原始数据</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #73d13d;"></div>
          <span>计算指标</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #ff7a45;"></div>
          <span>聚合指标</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #faad14;"></div>
          <span>衍生指标</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #597ef7;"></div>
          <span>维度</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #f5222d;"></div>
          <span>异常指标</span>
        </div>
      </div>
    </div>

    <!-- 血缘图容器 -->
    <div id="bloodlineContainer" class="bloodline-container"></div>

    <!-- 详情面板 -->
    <div class="detail-panel">
      <div class="panel-title">指标详情</div>
      <div id="indicatorDetail">
        <div style="text-align: center; padding: 32px; color: #999;">
          <i class="fas fa-info-circle" style="font-size: 32px; margin-bottom: 16px;"></i>
          <p>请点击血缘图中的指标节点查看详情</p>
        </div>
      </div>
    </div>
  </div>
 <script src="js/common.js"></script>
  <script>
    // 切换二级菜单
    function toggleSubmenu(menuItem) {
      const submenuContainer = menuItem.querySelector('.submenu-container');
      const menuToggle = menuItem.querySelector('.menu-toggle');
      
      if (submenuContainer) {
        submenuContainer.classList.toggle('open');
        if (menuToggle) {
          menuToggle.classList.toggle('open');
        }
      }
    }
    
    // 切换三级菜单
    function toggleTertiaryMenu(submenuItem) {
      const tertiarymenuContainer = submenuItem.querySelector('.tertiarymenu-container');
      const menuToggle = submenuItem.querySelector('.menu-toggle');
      
      if (tertiarymenuContainer) {
        tertiarymenuContainer.classList.toggle('open');
        if (menuToggle) {
          menuToggle.classList.toggle('open');
        }
      }
    }

    // 初始化血缘图
    document.addEventListener('DOMContentLoaded', function() {
      // 获取容器
      const container = document.getElementById('bloodlineContainer');

      // 模拟数据
      const nodes = [
        { id: 1, label: '用户数', type: 'aggregate', color: '#ff7a45' },
        { id: 2, label: '新增用户', type: 'calculate', color: '#73d13d' },
        { id: 3, label: '活跃用户', type: 'calculate', color: '#73d13d' },
        { id: 4, label: '注册用户', type: 'raw', color: '#4096ff' },
        { id: 5, label: '登录用户', type: 'raw', color: '#4096ff' },
        { id: 6, label: '留存用户', type: 'derive', color: '#faad14' },
        { id: 7, label: '日活用户', type: 'calculate', color: '#73d13d' },
        { id: 8, label: '周活用户', type: 'calculate', color: '#73d13d' },
        { id: 9, label: '月活用户', type: 'calculate', color: '#73d13d' },
        { id: 10, label: '用户转化率', type: 'derive', color: '#faad14' },
        { id: 11, label: '渠道A用户', type: 'dimension', color: '#597ef7' },
        { id: 12, label: '渠道B用户', type: 'dimension', color: '#597ef7' },
        { id: 13, label: '渠道C用户', type: 'dimension', color: '#597ef7' },
        { id: 14, label: '异常活跃用户', type: 'abnormal', color: '#f5222d' }
      ];

      const edges = [
        { from: 4, to: 2 },
        { from: 5, to: 3 },
        { from: 2, to: 1 },
        { from: 3, to: 1 },
        { from: 4, to: 6 },
        { from: 5, to: 6 },
        { from: 5, to: 7 },
        { from: 7, to: 8 },
        { from: 8, to: 9 },
        { from: 2, to: 10 },
        { from: 1, to: 10 },
        { from: 4, to: 11 },
        { from: 4, to: 12 },
        { from: 4, to: 13 },
        { from: 7, to: 14 }
      ];

      // 创建数据对象
      const data = {
        nodes: nodes,
        edges: edges
      };

      // 配置选项
      const options = {
        autoResize: true,
        height: '100%',
        width: '100%',
        nodes: {
          shape: 'box',
          borderWidth: 2,
          size: 60,
          font: {
            size: 12
          },
          color: {
            border: '#999',
            background: '#fff'
          },
          shadow: {
            enabled: true,
            size: 10
          }
        },
        edges: {
          color: '#999',
          width: 2,
          arrows: {
            to: {
              enabled: true,
              scaleFactor: 1
            }
          }
        },
        layout: {
          hierarchical: {
            direction: 'LR',
            sortMethod: 'directed'
          }
        },
        interaction: {
          dragNodes: true,
          dragView: true,
          zoomView: true
        },
        physics: {
          enabled: true,
          solver: 'forceAtlas2Based',
          forceAtlas2Based: {
            gravitationalConstant: -200,
            centralGravity: 0.1,
            springLength: 100,
            springConstant: 0.08,
            damping: 0.4
          }
        }
      };

      // 创建网络实例
      const network = new vis.Network(container, data, options);

      // 节点点击事件
      network.on('click', function(params) {
        if (params.nodes.length > 0) {
          const nodeId = params.nodes[0];
          // 高亮选中的节点
          network.setSelection({ nodes: [nodeId] });
          // 显示节点详情
          showNodeDetail(nodeId);
          // 高亮血缘路径
          highlightBloodlinePath(nodeId);
        }
      });

      // 搜索按钮事件
      document.getElementById('searchBtn').addEventListener('click', function() {
        const searchText = document.getElementById('indicatorSearch').value.trim();
        if (searchText) {
          searchIndicator(searchText);
        }
        fetch('http://127.0.0.1:5000/test/searchButton/qury', {
          method: 'GET',
        });
      });

      // 搜索输入框回车事件
      document.getElementById('indicatorSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          const searchText = document.getElementById('indicatorSearch').value.trim();
          if (searchText) {
            searchIndicator(searchText);
          }
        }
      });

      // 全部展开按钮事件
      document.getElementById('expandAllBtn').addEventListener('click', function() {
        network.fit();
      });

      // 全部收起按钮事件
      document.getElementById('collapseAllBtn').addEventListener('click', function() {
        network.setOptions({
          layout: {
            hierarchical: {
              enabled: true
            }
          },
          physics: {
            enabled: false
          }
        });
      });

      // 刷新按钮事件
      document.getElementById('refreshBtn').addEventListener('click', function() {
        network.redraw();
        showNotification('血缘图已刷新');
        fetch('http://127.0.0.1:5000/test/searchButton/shuaxin', {
          method: 'GET',
        });
      });

      // 导出按钮事件
      document.getElementById('exportBtn').addEventListener('click', function() {
        // 这里实现导出功能
        showNotification('导出功能已触发');
        fetch('http://127.0.0.1:5000/test/searchButton/daochu', {
          method: 'GET',
        });
      });

      // 布局切换按钮事件
      document.getElementById('layoutBtn').addEventListener('click', function() {
        const currentLayout = network.getOptions().layout.hierarchical.enabled;
        network.setOptions({
          layout: {
            hierarchical: {
              enabled: !currentLayout
            }
          },
          physics: {
            enabled: currentLayout
          }
        });
        showNotification(currentLayout ? '已切换为力导向布局' : '已切换为层次布局');
      });

      // 显示节点详情
      function showNodeDetail(nodeId) {
        const node = nodes.find(n => n.id === nodeId);
        if (node) {
          let detailHTML = `
            <div class="info-item">
              <div class="info-label">指标名称</div>
              <div class="info-value">${node.label}</div>
            </div>
            <div class="info-item">
              <div class="info-label">指标类型</div>
              <div class="info-value">${getTypeName(node.type)}</div>
            </div>
            <div class="info-item">
              <div class="info-label">指标ID</div>
              <div class="info-value">IND-${node.id.toString().padStart(3, '0')}</div>
            </div>
            <div class="info-item">
              <div class="info-label">责任人</div>
              <div class="info-value">数据团队</div>
            </div>
            <div class="info-item">
              <div class="info-label">更新时间</div>
              <div class="info-value">${new Date().toLocaleString()}</div>
            </div>
            <div class="info-item">
              <div class="info-label">计算逻辑</div>
              <div class="info-value">${getCalculationLogic(node.type)}</div>
            </div>
            <div class="info-item">
              <div class="info-label">上下游依赖</div>
              <div class="info-value">
                <div style="margin-bottom: 8px;"><strong>上游依赖:</strong> ${getUpstreamDependencies(nodeId).join(', ') || '无'}</div>
                <div><strong>下游依赖:</strong> ${getDownstreamDependencies(nodeId).join(', ') || '无'}</div>
              </div>
            </div>
          `;
          document.getElementById('indicatorDetail').innerHTML = detailHTML;
        }
      }

      // 高亮血缘路径
      function highlightBloodlinePath(nodeId) {
        // 重置所有边的样式
        const allEdges = network.body.data.edges.map(edge => edge.id);
        network.setOptions({
          edges: {
            color: '#999',
            width: 2
          }
        });

        // 获取上下游边
        const upstreamEdges = edges.filter(edge => edge.to === nodeId).map(edge => edge.id);
        const downstreamEdges = edges.filter(edge => edge.from === nodeId).map(edge => edge.id);
        const highlightEdges = [...upstreamEdges, ...downstreamEdges];

        // 高亮边
        network.setEdgesOptions(highlightEdges, {
          color: '#4096ff',
          width: 3
        });
      }

      // 搜索指标
      function searchIndicator(text) {
        const foundNode = nodes.find(n => n.label.includes(text));
        if (foundNode) {
          // 居中显示节点
          network.selectNodes([foundNode.id]);
          network.focus(foundNode.id, { scale: 2 });
          // 显示详情
          showNodeDetail(foundNode.id);
          // 高亮血缘路径
          highlightBloodlinePath(foundNode.id);
          showNotification(`找到指标: ${foundNode.label}`);
        } else {
          showNotification(`未找到指标: ${text}`);
        }
      }

      // 获取类型名称
      function getTypeName(type) {
        const typeMap = {
          'raw': '原始数据',
          'calculate': '计算指标',
          'aggregate': '聚合指标',
          'derive': '衍生指标',
          'dimension': '维度',
          'abnormal': '异常指标'
        };
        return typeMap[type] || type;
      }

      // 获取计算逻辑
      function getCalculationLogic(type) {
        const logicMap = {
          'raw': '直接从数据源获取，无计算逻辑',
          'calculate': '基于原始数据通过特定公式计算得出',
          'aggregate': '对多个指标进行聚合计算',
          'derive': '基于其他指标衍生计算',
          'dimension': '用于数据分组和筛选的维度',
          'abnormal': '偏离正常范围的异常指标'
        };
        return logicMap[type] || '未知计算逻辑';
      }

      // 获取上游依赖
      function getUpstreamDependencies(nodeId) {
        return edges
          .filter(edge => edge.to === nodeId)
          .map(edge => {
            const node = nodes.find(n => n.id === edge.from);
            return node ? node.label : '';
          })
          .filter(Boolean);
      }

      // 获取下游依赖
      function getDownstreamDependencies(nodeId) {
        return edges
          .filter(edge => edge.from === nodeId)
          .map(edge => {
            const node = nodes.find(n => n.id === edge.to);
            return node ? node.label : '';
          })
          .filter(Boolean);
      }

      // 显示通知
      function showNotification(message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.backgroundColor = 'rgba(64, 150, 255, 0.9)';
        notification.style.color = 'white';
        notification.style.padding = '10px 20px';
        notification.style.borderRadius = '4px';
        notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        notification.style.zIndex = '1000';
        notification.style.transition = 'all 0.3s ease';
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 3秒后移除
        setTimeout(function() {
          notification.style.opacity = '0';
          setTimeout(function() {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }

    });







  </script>
</body>
</html>