/**
 * API客户端工具 - 用于DevOps平台的API交互
 * 基础URL: http://127.0.0.1/api/v1
 */

class ApiClient {
  constructor() {
    this.baseURL = 'http://127.0.0.1/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json;charset=gbk',
      'Authorization': 'Bearer demo-token-12345'
    };
  }

  /**
   * 显示loading状态
   */
  showLoading(element) {
    if (element) {
      element.disabled = true;
      const originalText = element.innerHTML;
      element.setAttribute('data-original-text', originalText);
      element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    }
  }

  /**
   * 隐藏loading状态
   */
  hideLoading(element) {
    if (element) {
      element.disabled = false;
      const originalText = element.getAttribute('data-original-text');
      if (originalText) {
        element.innerHTML = originalText;
        element.removeAttribute('data-original-text');
      }
    }
  }

  /**
   * 显示成功消息
   */
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  /**
   * 显示错误消息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示通知消息
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `api-notification api-notification-${type}`;
    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
      </div>
    `;
    
    // 添加样式
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      padding: 12px 16px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      background-color: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
    `;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * 通用请求方法
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: { ...this.defaultHeaders, ...options.headers },
      ...options
    };

    console.log(`🚀 API Request: ${config.method || 'GET'} ${url}`, config.body ? JSON.parse(config.body) : null);

    try {
      const response = await fetch(url, config);
      const data = await response.json().catch(() => ({}));
      
      console.log(`✅ API Response: ${response.status}`, data);

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error(`❌ API Error: ${config.method || 'GET'} ${url}`, error);
      
      // 如果是网络错误，返回模拟数据
      if (error.name === 'TypeError' || error.message.includes('fetch')) {
        console.log('🔄 Network error detected, returning mock data');
        return this.getMockResponse(endpoint, config.method || 'GET');
      }
      
      throw error;
    }
  }

  /**
   * GET请求
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  /**
   * POST请求
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT请求
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE请求
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  /**
   * 获取模拟响应数据 - 使用新的JSON格式
   */
  getMockResponse(endpoint, method) {
    const mockData = {
      // 通知相关
      '/notifications': {
        code: 200,
        message: '成功',
        data: [
          {
            notificationId: 'notif_001',
            title: '新任务通知',
            message: '您有3个新任务需要处理',
            type: 'task',
            priority: 'medium',
            isRead: false,
            createdAt: '2024-01-15T10:25:00Z',
            relativeTime: '2分钟前'
          },
          {
            notificationId: 'notif_002',
            title: '数据采集完成',
            message: '昨日数据采集已完成',
            type: 'system',
            priority: 'low',
            isRead: false,
            createdAt: '2024-01-15T09:30:00Z',
            relativeTime: '1小时前'
          }
        ],
        unreadCount: 2
      },

      // 用户相关
      '/user_profile': {
        code: 200,
        message: '成功',
        data: {
          userId: 'admin_001',
          username: '管理员',
          email: '<EMAIL>',
          role: 'administrator',
          department: 'IT运维部',
          avatar: 'https://picsum.photos/id/1005/40/40',
          lastLoginTime: '2024-01-15T09:00:00Z'
        }
      },

      // Dashboard相关
      '/dashboard/stats': {
        success: true,
        data: {
          runningApps: 24,
          todayDeployments: 18,
          activeAlerts: 3,
          systemHealth: 98.5
        }
      },

      '/dashboard/deployment-trend': {
        success: true,
        data: [
          { time: '00:00', deployments: 2, success: 2 },
          { time: '04:00', deployments: 1, success: 1 },
          { time: '08:00', deployments: 5, success: 4 },
          { time: '12:00', deployments: 8, success: 7 },
          { time: '16:00', deployments: 12, success: 11 },
          { time: '20:00', deployments: 6, success: 6 }
        ]
      },

      // 部署相关
      '/deployments/environments': {
        success: true,
        data: [
          { id: 'prod', name: '生产环境' },
          { id: 'staging', name: '预发布环境' },
          { id: 'dev', name: '开发环境' }
        ]
      },

      // 监控相关
      '/monitoring/alerts': {
        success: true,
        data: [
          {
            id: '1',
            level: 'critical',
            title: 'CPU 使用率过高',
            message: 'user-service 节点 CPU 使用率达到 95%',
            time: '2分钟前',
            service: 'user-service',
            status: 'active'
          },
          {
            id: '2',
            level: 'warning',
            title: '内存使用率警告',
            message: 'order-service 内存使用率达到 85%',
            time: '5分钟前',
            service: 'order-service',
            status: 'active'
          }
        ]
      },

      // 流水线相关
      '/pipelines': {
        success: true,
        data: [
          {
            id: '1',
            name: 'user-service-pipeline',
            status: 'success',
            branch: 'main',
            author: '张三',
            duration: '6m 0s'
          }
        ]
      },

      // 服务拓扑相关
      '/topology/services': {
        success: true,
        data: [
          {
            id: 'user-service',
            name: 'User Service',
            type: 'service',
            status: 'healthy',
            instances: 3
          }
        ]
      }
    };

    // 根据endpoint返回对应的模拟数据
    for (const [path, data] of Object.entries(mockData)) {
      if (endpoint.includes(path)) {
        return data;
      }
    }

    // 默认成功响应
    return {
      success: true,
      message: `${method} ${endpoint} 操作成功`,
      data: {},
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 带loading状态的API调用
   */
  async callWithLoading(buttonElement, apiCall) {
    this.showLoading(buttonElement);
    try {
      const result = await apiCall();
      this.showSuccess(result.message || '操作成功');
      return result;
    } catch (error) {
      this.showError(error.message || '操作失败');
      throw error;
    } finally {
      this.hideLoading(buttonElement);
    }
  }
}

// 创建全局API客户端实例
window.apiClient = new ApiClient();

// 导出API客户端（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiClient;
}
