// 画布弹窗管理脚本

// 初始化弹窗功能
function initCanvasModal() {
  // 获取DOM元素
  const addButton = document.querySelector('.btn-outline.text-sm i.fa-plus').parentElement;
  const canvasModal = document.getElementById('createCanvasModal');
  const closeModal = canvasModal.querySelector('.modal-close');
  const cancelButton = canvasModal.querySelector('.btn-outline.text-sm:first-of-type');
  const saveButton = canvasModal.querySelector('.btn-outline.text-sm:last-of-type');
  const notification = document.getElementById('notification');

  // 点击新增按钮显示弹窗
  addButton.addEventListener('click', function() {
    canvasModal.style.display = 'block';
    // 添加淡入动画
    const modalContent = canvasModal.querySelector('div');
    setTimeout(() => {
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1) translate(-50%, -50%)';
    }, 10);
  });

  // 关闭弹窗的函数
  function closeCanvasModal() {
    const modalContent = canvasModal.querySelector('div');
    modalContent.style.opacity = '0';
    modalContent.style.transform = 'scale(0.95) translate(-50%, -50%)';
    setTimeout(() => {
      canvasModal.style.display = 'none';
    }, 300);
  }

  // 点击关闭按钮关闭弹窗
  closeModal.addEventListener('click', closeCanvasModal);

  // 点击取消按钮关闭弹窗并显示提示
  cancelButton.addEventListener('click', function() {
    closeCanvasModal();
    showNotification('已取消保存画布', 'info');
  });

  // 点击保存按钮关闭弹窗并显示提示
  saveButton.addEventListener('click', function() {
    const canvasName = document.getElementById('canvasName').value;
    const dataSourceType = document.getElementById('dataSourceType').value;
    if (!canvasName.trim()) {
      showNotification('请输入画布名称', 'error');
      return;
    }
    closeCanvasModal();
    showNotification('画布保存成功', 'success');
    // 这里可以添加实际保存画布的逻辑
    console.log('画布名称:', canvasName);
    console.log('画布类型:', document.getElementById('canvasType').value);
    console.log('数据源类型:', dataSourceType);
    console.log('画布描述:', document.getElementById('canvasDescription').value);
  });

  // 点击弹窗外部关闭弹窗
  canvasModal.addEventListener('click', function(event) {
    if (event.target === canvasModal) {
      closeCanvasModal();
    }
  });

  // 显示通知提示
  function showNotification(message, type = 'info') {
    // 设置通知内容和类型
    notification.textContent = message;
    notification.className = 'notification';
    notification.classList.add(type);

    // 显示通知
    notification.style.display = 'block';
    notification.style.opacity = '1';

    // 3秒后隐藏通知
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        notification.style.display = 'none';
      }, 300);
    }, 3000);
  }

  // 画布类型选择功能
  const canvasTypeOptions = document.querySelectorAll('.canvas-type-option');
  canvasTypeOptions.forEach(option => {
    option.addEventListener('click', function() {
      // 移除所有选项的选中状态
      canvasTypeOptions.forEach(opt => {
        opt.style.border = '2px solid transparent';
      });
      // 设置当前选项为选中状态
      this.style.border = '2px solid var(--primary-color)';
    });
  });
}

// 当文档加载完成后初始化弹窗功能
document.addEventListener('DOMContentLoaded', initCanvasModal);