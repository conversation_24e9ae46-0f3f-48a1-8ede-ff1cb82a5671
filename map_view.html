<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 地图视图</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .map-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 80px);
      }
      .map-controls { 
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 16px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .level-controls {
        display: flex;
        align-items: center;
      }
      .level-btn {
        margin-right: 12px;
        padding: 6px 12px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
      }
      .level-btn:disabled {
        background-color: var(--border-color);
        cursor: not-allowed;
      }
      .level-btn i {
        margin-right: 6px;
      }
      .level-selector {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
      }
      .map-view {
        flex: 1;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
        display: flex;
        flex-direction: column;
        overflow: auto;
      }
      .map-display {
        flex: 1;
        background-color: #f9f9f9;
        border-radius: 8px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }
      .map-display img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: transform 0.5s ease;
      }
      .map-zoom-controls {
        position: absolute;
        right: 20px;
        bottom: 20px;
        display: flex;
        flex-direction: column;
      }
      .zoom-btn {
        width: 36px;
        height: 36px;
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .region-info {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 16px;
        margin-bottom: 20px;
      }
      .region-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
      }
      .region-title i {
        margin-right: 8px;
        color: var(--primary-color);
      }
      .data-stats {
        display: flex;
        margin-bottom: 16px;
      }
      .data-stat-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        background-color: rgba(24, 144, 255, 0.05);
        border-radius: 8px;
        margin-right: 12px;
      }
      .data-stat-item:last-child {
        margin-right: 0;
      }
      .data-stat-value {
        font-size: 20px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 4px;
      }
      .data-stat-label {
        font-size: 14px;
        color: var(--text-secondary);
      }
      .region-list {
        max-height: 200px;
        overflow-y: auto;
      }
      .region-item {
        padding: 12px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: background-color 0.3s;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .region-item:hover {
        background-color: var(--hover-color);
      }
      .region-item.active {
        background-color: rgba(24, 144, 255, 0.1);
        color: var(--primary-color);
      }
      .region-item-actions {
        display: flex;
      }
      .region-item-action {
        margin-left: 8px;
        color: var(--text-tertiary);
        cursor: pointer;
      }
      .region-item-action:hover {
        color: var(--primary-color);
      }
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
      }
      .loading-content {
        text-align: center;
      }
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(24, 144, 255, 0.3);
        border-radius: 50%;
        border-top-color: var(--primary-color);
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 16px;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>
   <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

         <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
           <div class="menu-item child " data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" >
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child  active" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

    
      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-map-marked-alt page-title-icon"></i>
        运营视图 - 视频交互 - 地图层级
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">视频交互</a></div>
        <div class="breadcrumb-item active">地图层级</div>
      </div>

      <!-- 地图容器 -->
      <div class="map-container">
        <!-- 地图控件区 -->
        <div class="map-controls">
          <div class="level-controls">
            <button id="upLevelBtn" class="level-btn" disabled>
              <i class="fas fa-arrow-up"></i>
              上钻到父级
            </button>
            <select id="levelSelector" class="level-selector">
              <option value="country">全国</option>
              <option value="province">省份</option>
              <option value="city">城市</option>
              <option value="district">区县</option>
            </select>
            <div style="margin-left: 12px">
              <input type="text" id="dateRangePicker" placeholder="选择日期范围" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 200px" />
            </div>
          </div>
          <div style="display: flex">
            <button class="btn" id="daochu" style="border: 1px solid var(--border-color); margin-right: 12px">
              <i class="fas fa-download"></i>
              导出
            </button>
            <button class="btn" id="fenxiang"style="border: 1px solid var(--border-color); margin-right: 12px">
              <i class="fas fa-share-alt"></i>
              分享
            </button>
            <button class="btn btn-primary">
              <i class="fas fa-expand"></i>
              全屏
            </button>
          </div>
        </div>

        <!-- 地图显示区 -->
        <div class="map-view">
          <div class="map-display" id="mapDisplay" style="min-height:300px">
            <div class="loading-overlay" id="loadingOverlay">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <div>加载中，请稍候...</div>
              </div>
            </div>
            <img id="mapImage" src="./images/全国.png" alt="全国地图" />
            <div class="map-zoom-controls">
              <button class="zoom-btn" id="zoomInBtn"><i class="fas fa-plus"></i></button>
              <button class="zoom-btn" id="zoomOutBtn"><i class="fas fa-minus"></i></button>
            </div>
          </div>

          <!-- 当前区域信息 -->
          <div class="region-info">
            <div class="region-title">
              <i class="fas fa-info-circle"></i>
              当前区域信息
            </div>
            <div id="currentRegionName" style="font-size: 16px; font-weight: 500; margin-bottom: 16px">全国</div>

            <!-- 数据统计 -->
            <div class="data-stats">
              <div class="data-stat-item">
                <div class="data-stat-value">¥12.6M</div>
                <div class="data-stat-label">总销售额</div>
              </div>
              <div class="data-stat-item">
                <div class="data-stat-value">12.5%</div>
                <div class="data-stat-label">同比增长</div>
              </div>
              <div class="data-stat-item">
                <div class="data-stat-value">324</div>
                <div class="data-stat-label">销售单量</div>
              </div>
              <div class="data-stat-item">
                <div class="data-stat-value">¥39,426</div>
                <div class="data-stat-label">客单价</div>
              </div>
            </div>

            <!-- 子区域列表 -->
            <div id="subRegionList" class="region-list">
              <div class="region-item" data-region="华东地区">
                <div>华东地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
              <div class="region-item" data-region="华南地区">
                <div>华南地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
              <div class="region-item" data-region="华北地区">
                <div>华北地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
              <div class="region-item" data-region="西南地区">
                <div>西南地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
              <div class="region-item" data-region="西北地区">
                <div>西北地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
              <div class="region-item" data-region="东北地区">
                <div>东北地区</div>
                <div class="region-item-actions">
                  <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script src="js/common.js"></script>
    <script src="js/common.js"></script>
    <script>
      // 地图页面特定的交互逻辑
      document.addEventListener('DOMContentLoaded', function () {
        // 初始化日期选择器
        flatpickr('#dateRangePicker', {
          mode: 'range',
          dateFormat: 'Y-m-d',
          defaultDate: ['2021-01-27', '2021-04-27'],
          locale: 'zh',
          monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
          weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
          weekNumbers: false,
          firstDayOfWeek: 1,
          rangeSeparator: ' 至 ',
          todayBtn: true,
          todayDateFormat: 'Y-m-d',
          clearBtn: true,
          closeOnSelect: false,
        });
        // 获取DOM元素
        const upLevelBtn = document.getElementById('upLevelBtn');
        const levelSelector = document.getElementById('levelSelector');
        const mapDisplay = document.getElementById('mapDisplay');
        const mapImage = document.getElementById('mapImage');
        const currentRegionName = document.getElementById('currentRegionName');
        const subRegionList = document.getElementById('subRegionList');
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const loadingOverlay = document.getElementById('loadingOverlay');

        const daochu = document.getElementById('daochu');
        const fenxiang = document.getElementById('fenxiang');

        // 地图状态
        let currentLevel = 'province'; // country, province, city, district
        let currentRegion = '华东地区';
        let currentScale = 1;
        let regionPath = ['全国', '华东地区']; // 记录层级路径

        // 模拟数据 - 实际应用中应从API获取
        const regionData = {
          全国: {
            image: './images/全国.png',
            subRegions: ['华东地区', '华南地区', '华北地区', '西南地区', '西北地区', '东北地区'],
            stats: {
              sales: '¥12.6M',
              growth: '12.5%',
              orders: '324',
              avgPrice: '¥39,426',
            },
          },
          华东地区: {
            image: './images/省份.png',
            subRegions: ['江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省'],
            stats: {
              sales: '¥4.2M',
              growth: '15.2%',
              orders: '108',
              avgPrice: '¥39,426',
            },
          },
          江苏省: {
            image: './images/城市.png',
            subRegions: ['南京市', '苏州市', '无锡市', '常州市', '徐州市'],
            stats: {
              sales: '¥1.8M',
              growth: '18.3%',
              orders: '46',
              avgPrice: '¥39,130',
            },
          },
          南京市: {
            image: './images/区县.png',
            subRegions: ['玄武区', '秦淮区', '建邺区', '鼓楼区', '雨花台区'],
            stats: {
              sales: '¥620K',
              growth: '22.1%',
              orders: '16',
              avgPrice: '¥38,750',
            },
          },
        };

        // 显示加载状态
        function showLoading() {
          loadingOverlay.style.display = 'flex';
        }

        // 隐藏加载状态
        function hideLoading() {
          loadingOverlay.style.display = 'none';
        }

        // 更新地图显示
        function updateMap() {
          showLoading();

          // 模拟数据加载延迟
          setTimeout(() => {
            // 更新地图图片
            if (regionData[currentRegion]) {
              mapImage.src = regionData[currentRegion].image;
            } else {
              mapImage.src = 'images/全国.png';
            }

            // 更新当前区域名称
            currentRegionName.textContent = currentRegion;

            // 更新数据统计
            const stats = regionData[currentRegion]?.stats || regionData['全国'].stats;
            const statItems = document.querySelectorAll('.data-stat-value');
            statItems[0].textContent = stats.sales;
            statItems[1].textContent = stats.growth;
            statItems[2].textContent = stats.orders;
            statItems[3].textContent = stats.avgPrice;

            // 更新子区域列表
            updateSubRegionList();

            // 更新层级选择器
            updateLevelControls();

            // 重置缩放
            currentScale = 1;
            mapImage.style.transform = `scale(${currentScale})`;

            hideLoading();
          }, 800);
        }

        // 更新子区域列表
        function updateSubRegionList() {
          const subRegions = regionData[currentRegion]?.subRegions || [];
          subRegionList.innerHTML = '';

          if (subRegions.length === 0) {
            subRegionList.innerHTML = '<div style="padding: 12px; text-align: center; color: var(--text-tertiary);">暂无下级区域数据</div>';
            return;
          }

          subRegions.forEach(region => {
            const regionItem = document.createElement('div');
            regionItem.className = 'region-item';
            regionItem.setAttribute('data-region', region);

            regionItem.innerHTML = `
            <div>${region}</div>
            <div class="region-item-actions">
              <span class="region-item-action"><i class="fas fa-chevron-down"></i></span>
            </div>
          `;

            // 点击子区域下钻
            regionItem.addEventListener('click', () => {
              // 更新层级路径
              regionPath.push(region);
              currentRegion = region;

              // 更新层级
              if (currentLevel === 'country') currentLevel = 'province';
              else if (currentLevel === 'province') currentLevel = 'city';
              else if (currentLevel === 'city') currentLevel = 'district';

              updateMap();
            });

            subRegionList.appendChild(regionItem);
          });
        }

        // 更新层级控件状态
        function updateLevelControls() {
          // 更新上钻按钮状态
          upLevelBtn.disabled = regionPath.length <= 1;

          // 更新层级选择器
          levelSelector.value = currentLevel;
        }

        // 上钻到父级
        upLevelBtn.addEventListener('click', () => {
          if (regionPath.length > 1) {
            // 移除当前区域
            regionPath.pop();
            // 设置为父级区域
            currentRegion = regionPath[regionPath.length - 1];

            // 更新层级
            if (currentLevel === 'province') currentLevel = 'country';
            else if (currentLevel === 'city') currentLevel = 'province';
            else if (currentLevel === 'district') currentLevel = 'city';
            fetch('http://127.0.0.1:5000/test/searchButton/up', {
          method: 'GET',
        });
            updateMap();
          }
        });

        // 层级选择器变更
        levelSelector.addEventListener('change', () => {
          const selectedLevel = levelSelector.value;

          // 简化处理：直接跳转到对应层级的全国/省份/城市/区县视图
          if (selectedLevel !== currentLevel) {
            currentLevel = selectedLevel;

            // 重置路径
            regionPath = ['全国'];
            currentRegion = '全国';

            // 根据选择的层级调整
            if (currentLevel === 'province') {
              regionPath = ['全国', '华东地区'];
              currentRegion = '华东地区';
            } else if (currentLevel === 'city') {
              regionPath = ['全国', '华东地区', '江苏省'];
              currentRegion = '江苏省';
            } else if (currentLevel === 'district') {
              regionPath = ['全国', '华东地区', '江苏省', '南京市'];
              currentRegion = '南京市';
            }
        
        fetch('http://127.0.0.1:5000/test/searchButton/xuanze', {
          method: 'GET',
        });
      
            updateMap();
          }
        });

        // 地图点击事件 - 模拟区域选择
        mapImage.addEventListener('click', () => {
          // 实际应用中，这里应该根据点击的坐标识别区域
          // 这里简化处理，选择第一个子区域进行演示
          const subRegions = regionData[currentRegion]?.subRegions || [];
          if (subRegions.length > 0) {
            const selectedRegion = subRegions[0]; // 选择第一个子区域代替随机选择

            // 更新层级路径
            regionPath.push(selectedRegion);
            currentRegion = selectedRegion;

            // 更新层级
            if (currentLevel === 'country') currentLevel = 'province';
            else if (currentLevel === 'province') currentLevel = 'city';
            else if (currentLevel === 'city') currentLevel = 'district';

            updateMap(); // 确保调用updateMap函数更新视图
          }
        });

        // 地图缩放
        zoomInBtn.addEventListener('click', () => {
          if (currentScale < 2) {
            currentScale += 0.1;
            mapImage.style.transform = `scale(${currentScale})`;
          }
        });

        zoomOutBtn.addEventListener('click', () => {
          if (currentScale > 0.5) {
            currentScale -= 0.1;
            mapImage.style.transform = `scale(${currentScale})`;
          }
        });

        // 初始化地图
        updateMap();

        document.getElementById('daochu').addEventListener('click', function() {

        fetch('http://127.0.0.1:5000/test/searchButton/daochu', {
          method: 'GET',
        });
      });

      document.getElementById('fenxiang').addEventListener('click', function() {
        
        fetch('http://127.0.0.1:5000/test/searchButton/fenxiang', {
          method: 'GET',
        });
      });











      });
    </script>
  </body>
</html>
