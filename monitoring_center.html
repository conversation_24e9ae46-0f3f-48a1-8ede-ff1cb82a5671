<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>监控中心 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child active" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration"  data-href="five_level_penetration.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-chart-line page-title-icon"></i>
      监控中心
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">DevOps 平台</a></div>
      <div class="breadcrumb-item active">监控中心</div>
    </div>

    <!-- 页面标题和操作 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <div>
        <h1 style="font-size: 24px; font-weight: bold; margin: 0; color: var(--text-primary);">监控中心</h1>
        <p style="color: var(--text-secondary); margin: 4px 0 0 0;">系统性能监控与日志管理</p>
      </div>
      <div style="display: flex; align-items: center; gap: 8px;">
        <select id="timeRange" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 8px 12px; font-size: 14px;" onchange="changeTimeRange(this)">
          <option value="5m">5分钟</option>
          <option value="15m">15分钟</option>
          <option value="1h" selected>1小时</option>
          <option value="6h">6小时</option>
          <option value="24h">24小时</option>
        </select>
        <button class="btn btn-primary" onclick="refreshAllCharts(this)"  style="width: 137px;height: 38px;">
          <i class="fas fa-sync-alt"></i> 刷新
        </button>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="row">
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(59, 130, 246, 0.1); color: #3B82F6;">
            <i class="fas fa-microchip"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">58.2%</div>
            <div class="stat-label">平均CPU使用率</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-up"></i> +2.4%
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(16, 185, 129, 0.1); color: #10B981;">
            <i class="fas fa-hdd"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">1.5GB</div>
            <div class="stat-label">平均内存使用</div>
            <div class="stat-change" style="color: var(--danger-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-down"></i> -120MB
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(139, 69, 19, 0.1); color: #8B4513;">
            <i class="fas fa-network-wired"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">21.3MB/s</div>
            <div class="stat-label">网络吞吐量</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-up"></i> +3.2MB/s
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(245, 158, 11, 0.1); color: #F59E0B;">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value" id="activeAlertsCount">3</div>
            <div class="stat-label">活跃告警</div>
            <div class="stat-change" style="color: var(--warning-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-exclamation-triangle"></i> 需关注
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能图表 -->
    <div class="row">
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600;">CPU 使用率</h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="cpuChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600;">内存使用率</h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="memoryChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 网络和磁盘图表 -->
    <div class="row">
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600;">网络流量</h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="networkChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600;">磁盘使用率</h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="diskChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警列表 -->
    <div class="card">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
        <h3 style="font-size: 18px; font-weight: 600;">活跃告警</h3>
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="refreshAlerts(this)">
          <i class="fas fa-sync-alt"></i> 刷新告警
        </button>
      </div>
      <div id="alertsList">
        <!-- 告警列表将通过JavaScript动态生成 -->
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script src="js/api-client.js"></script>
  <script>
    // API交互函数

    // 改变时间范围
    async function changeTimeRange(selectElement) {
      const timeRange = selectElement.value;
      apiClient.showLoading(selectElement);

      try {
        const response = await apiClient.get('/monitoring/metrics', { timeRange });

        if (response.success && response.data) {
          // 更新监控数据
          monitoringData.cpu = response.data.cpu || generateTimeSeriesData(60, 30, 80);
          monitoringData.memory = response.data.memory || generateTimeSeriesData(60, 40, 90);
          monitoringData.network = response.data.network || generateTimeSeriesData(60, 10, 50);
          monitoringData.disk = response.data.disk || generateTimeSeriesData(60, 20, 60);

          // 重新初始化图表
          initCharts();
          apiClient.showSuccess(`已切换到${timeRange}时间范围`);
        }
      } catch (error) {
        apiClient.showError('获取监控数据失败');
      } finally {
        apiClient.hideLoading(selectElement);
      }
    }

    // 刷新所有图表
    async function refreshAllCharts(button) {
      await apiClient.callWithLoading(button, async () => {
        const timeRange = document.getElementById('timeRange').value;
        const response = await apiClient.get('/monitoring/metrics', { timeRange });

        if (response.success) {
          // 重新生成数据
          monitoringData.cpu = response.data?.cpu || generateTimeSeriesData(60, 30, 80);
          monitoringData.memory = response.data?.memory || generateTimeSeriesData(60, 40, 90);
          monitoringData.network = response.data?.network || generateTimeSeriesData(60, 10, 50);
          monitoringData.disk = response.data?.disk || generateTimeSeriesData(60, 20, 60);

          // 更新图表
          initCharts();
          return { message: '监控数据已刷新' };
        }
      });
    }

    // 刷新告警
    async function refreshAlerts(button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.get('/monitoring/alerts');

        if (response.success && response.data) {
          // 更新告警数据
          alerts.length = 0;
          alerts.push(...response.data);

          renderAlertsList();

          // 更新告警计数
          const activeCount = alerts.filter(a => a.status === 'active').length;
          document.getElementById('activeAlertsCount').textContent = activeCount;

          return { message: '告警数据已刷新' };
        }
      });
    }

    // 解决告警
    async function resolveAlert(alertId, button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post(`/monitoring/alerts/${alertId}/resolve`);

        if (response.success) {
          const alert = alerts.find(a => a.id === alertId);
          if (alert) {
            alert.status = 'resolved';
            renderAlertsList();

            // 更新告警计数
            const activeCount = alerts.filter(a => a.status === 'active').length;
            document.getElementById('activeAlertsCount').textContent = activeCount;
          }

          return { message: '告警已解决' };
        }
      });
    }

    // 静音告警
    async function muteAlert(alertId, button) {
      await apiClient.callWithLoading(button, async () => {
        const response = await apiClient.post(`/monitoring/alerts/${alertId}/mute`);

        if (response.success) {
          const alert = alerts.find(a => a.id === alertId);
          if (alert) {
            alert.muted = true;
          }

          return { message: '告警已静音' };
        }
      });
    }

    // 模拟监控数据
    const monitoringData = {
      cpu: generateTimeSeriesData(60, 30, 80),
      memory: generateTimeSeriesData(60, 40, 90),
      network: generateTimeSeriesData(60, 10, 50),
      disk: generateTimeSeriesData(60, 20, 60)
    };

    const alerts = [
      {
        id: '1',
        level: 'critical',
        title: 'CPU 使用率过高',
        message: 'user-service 节点 CPU 使用率达到 95%',
        time: '2分钟前',
        service: 'user-service',
        status: 'active'
      },
      {
        id: '2',
        level: 'warning',
        title: '内存使用率警告',
        message: 'order-service 内存使用率达到 85%',
        time: '5分钟前',
        service: 'order-service',
        status: 'active'
      },
      {
        id: '3',
        level: 'info',
        title: '服务重启',
        message: 'payment-service 已自动重启',
        time: '10分钟前',
        service: 'payment-service',
        status: 'resolved'
      }
    ];

    // 生成时间序列数据
    function generateTimeSeriesData(points, min, max) {
      const data = [];
      const labels = [];
      const now = new Date();

      for (let i = points - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000); // 每分钟一个点
        labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
        data.push(Math.floor(Math.random() * (max - min) + min));
      }

      return { labels, data };
    }

    // 获取告警级别颜色
    function getAlertLevelColor(level) {
      switch (level) {
        case 'critical': return 'var(--danger-color)';
        case 'warning': return 'var(--warning-color)';
        case 'info': return 'var(--info-color)';
        default: return 'var(--text-tertiary)';
      }
    }

    function getAlertLevelIcon(level) {
      switch (level) {
        case 'critical': return '<i class="fas fa-exclamation-circle"></i>';
        case 'warning': return '<i class="fas fa-exclamation-triangle"></i>';
        case 'info': return '<i class="fas fa-info-circle"></i>';
        default: return '<i class="fas fa-bell"></i>';
      }
    }

    function getAlertLevelText(level) {
      switch (level) {
        case 'critical': return '严重';
        case 'warning': return '警告';
        case 'info': return '信息';
        default: return '未知';
      }
    }

    // 渲染告警列表
    function renderAlertsList() {
      const container = document.getElementById('alertsList');
      const activeAlerts = alerts.filter(alert => alert.status === 'active');

      if (activeAlerts.length === 0) {
        container.innerHTML = `
          <div style="text-align: center; padding: 40px; color: var(--text-tertiary);">
            <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 16px; color: var(--success-color);"></i>
            <p style="font-size: 16px; margin: 0;">暂无活跃告警</p>
          </div>
        `;
        return;
      }

      container.innerHTML = activeAlerts.map(alert => `
        <div style="display: flex; align-items: flex-start; padding: 16px; border-bottom: 1px solid var(--border-color); transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='var(--bg-color)'" onmouseout="this.style.backgroundColor='transparent'">
          <div style="margin-right: 12px; color: ${getAlertLevelColor(alert.level)}; font-size: 18px;">
            ${getAlertLevelIcon(alert.level)}
          </div>
          <div style="flex: 1;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
              <h4 style="margin: 0; font-size: 16px; font-weight: 500; color: var(--text-primary);">
                ${alert.title}
              </h4>
              <span style="background-color: ${getAlertLevelColor(alert.level)}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 500;">
                ${getAlertLevelText(alert.level)}
              </span>
            </div>
            <p style="margin: 0 0 8px 0; color: var(--text-secondary); font-size: 14px;">
              ${alert.message}
            </p>
            <div style="display: flex; align-items: center; gap: 16px; font-size: 12px; color: var(--text-tertiary);">
              <span><i class="fas fa-clock"></i> ${alert.time}</span>
              <span><i class="fas fa-server"></i> ${alert.service}</span>
            </div>
          </div>
          <div style="display: flex; gap: 8px;">
            <button class="btn" style="padding: 4px 8px; font-size: 12px; border: 1px solid var(--success-color); color: var(--success-color);" onclick="resolveAlert('${alert.id}', this)">
              <i class="fas fa-check"></i> 解决
            </button>
            <button class="btn" style="padding: 4px 8px; font-size: 12px; border: 1px solid var(--text-tertiary); color: var(--text-tertiary);" onclick="muteAlert('${alert.id}', this)">
              <i class="fas fa-bell-slash"></i> 静音
            </button>
          </div>
        </div>
      `).join('');
    }

    // 创建图表
    function createChart(canvasId, label, data, color) {
      const ctx = document.getElementById(canvasId).getContext('2d');
      return new Chart(ctx, {
        type: 'line',
        data: {
          labels: data.labels,
          datasets: [{
            label: label,
            data: data.data,
            borderColor: color,
            backgroundColor: color + '20',
            tension: 0.3,
            fill: true,
            pointRadius: 2,
            pointHoverRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            },
            x: {
              display: true
            }
          },
          elements: {
            point: {
              radius: 0
            }
          }
        }
      });
    }

    // 原有的函数已被API版本替换

    // 初始化图表
    function initCharts() {
      createChart('cpuChart', 'CPU 使用率 (%)', monitoringData.cpu, '#3B82F6');
      createChart('memoryChart', '内存使用率 (%)', monitoringData.memory, '#10B981');
      createChart('networkChart', '网络流量 (MB/s)', monitoringData.network, '#8B4513');
      createChart('diskChart', '磁盘使用率 (%)', monitoringData.disk, '#F59E0B');
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      initCharts();
      renderAlertsList();

      // 定时刷新数据（每30秒）
      setInterval(() => {
        monitoringData.cpu = generateTimeSeriesData(60, 30, 80);
        monitoringData.memory = generateTimeSeriesData(60, 40, 90);
        monitoringData.network = generateTimeSeriesData(60, 10, 50);
        monitoringData.disk = generateTimeSeriesData(60, 20, 60);
        initCharts();
      }, 30000);
    });
  </script>
</body>
</html>
