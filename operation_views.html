<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 运营视图</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item " data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

     <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child " data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child active" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <script>
    // 切换二级菜单
    function toggleSubmenu(menuItem) {
      const submenuContainer = menuItem.querySelector('.submenu-container');
      const menuToggle = menuItem.querySelector('.menu-toggle');
      
      if (submenuContainer) {
        submenuContainer.classList.toggle('open');
        if (menuToggle) {
          menuToggle.classList.toggle('open');
        }
      }
    }
    
    // 切换三级菜单
    function toggleTertiaryMenu(submenuItem) {
      const tertiarymenuContainer = submenuItem.querySelector('.tertiarymenu-container');
      const menuToggle = submenuItem.querySelector('.menu-toggle');
      
      if (tertiarymenuContainer) {
        tertiarymenuContainer.classList.toggle('open');
        if (menuToggle) {
          menuToggle.classList.toggle('open');
        }
      }
    }
    
    // 菜单项点击事件
    document.querySelectorAll('.menu-item, .submenu-item, .tertiarymenu-item').forEach(item => {
      item.addEventListener('click', function(e) {
        // 阻止事件冒泡，避免触发父级菜单的切换
        if (this.classList.contains('submenu-item') || this.classList.contains('tertiarymenu-item')) {
          e.stopPropagation();
        }
        
        // 移除同级别所有项的active类
        const parent = this.parentElement;
        if (this.classList.contains('menu-item')) {
          document.querySelectorAll('.menu-item').forEach(menuItem => {
            menuItem.classList.remove('active');
          });
        } else if (this.classList.contains('submenu-item')) {
          parent.querySelectorAll('.submenu-item').forEach(subItem => {
            subItem.classList.remove('active');
          });
        } else if (this.classList.contains('tertiarymenu-item')) {
          parent.querySelectorAll('.tertiarymenu-item').forEach(tertiaryItem => {
            tertiaryItem.classList.remove('active');
          });
        }
        
        // 为当前项添加active类
        this.classList.add('active');
      });
    });
  </script>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-chart-area page-title-icon"></i>
      运营视图 - 大屏模板
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item active">运营视图</div>
    </div>

    <!-- 筛选和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" id="templateLevelFilter">
            <option value="all">全部模板</option>
            <option value="national">全国模板</option>
            <option value="provincial">分省模板</option>
            <option value="city">市级模板</option>
            <option value="district">区县级模板</option>
          </select>
        </div>
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);" id="industryFilter">
            <option value="all">全部行业</option>
            <option value="retail">零售行业</option>
            <option value="finance">金融行业</option>
            <option value="manufacturing">制造业</option>
            <option value="service">服务业</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" id="filterBtn" style="border: 1px solid var(--border-color); margin-right: 12px;" onclick="filterTemplates()"><i class="fas fa-filter"></i> 筛选</button>
        <button class="btn btn-primary" id="createTemplateBtn" data-modal-target="createTemplateModal"><i class="fas fa-plus"></i> 创建模板</button>
      </div>
    </div>

    <!-- 模板列表 -->
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
      <!-- 全国运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/资源调度大屏（总）.png" alt="全国运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">全国运营监控大屏</div>
            <div style="font-size: 12px;">全国级数据总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 128</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn edit-template-btn" style="color: var(--primary-color); margin-right: 8px;" data-template-name="全国运营监控大屏"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 省份运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/资源调度大屏（省）.png" alt="省份运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">省份运营分析大屏</div>
            <div style="font-size: 12px;">省级区域数据分析模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 分省</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 96</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn edit-template-btn" style="color: var(--primary-color); margin-right: 8px;" data-template-name="省份运营分析大屏"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 城市运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/资源调度大屏（市）.png" alt="城市运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">城市运营监控大屏</div>
            <div style="font-size: 12px;">市级区域数据监控模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 零售</span>
            <span><i class="fas fa-eye"></i> 72</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn edit-template-btn" style="color: var(--primary-color); margin-right: 8px;" data-template-name="城市运营监控大屏"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 区县运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/资源调度大屏（区县）.png" alt="区县运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">区县运营分析大屏</div>
            <div style="font-size: 12px;">区县级区域数据分析模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 区县</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 制造业</span>
            <span><i class="fas fa-eye"></i> 56</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 金融行业大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/分类动态监控大屏（总）.png" alt="分类动态监控大屏（总）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">分类动态监控大屏（总）</div>
            <div style="font-size: 12px;">全国级分类动态总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 金融</span>
            <span><i class="fas fa-eye"></i> 48</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 销售数据大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/价值分析大屏（总）.png" alt="价值分析大屏（总）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">价值分析大屏（总）</div>
            <div style="font-size: 12px;">全国级政企价值总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 零售</span>
            <span><i class="fas fa-eye"></i> 64</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 分类动态监控大屏（市） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/分类动态监控大屏（市）.png" alt="分类动态监控大屏（市）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">分类动态监控大屏（市）</div>
            <div style="font-size: 12px;">市级政企客户全景监测模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 42</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 分类动态监控大屏（区/县） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/分类动态监控大屏（区县）.png" alt="分类动态监控大屏（区县）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">分类动态监控大屏（区/县）</div>
            <div style="font-size: 12px;">区县级基层政企全景监测模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 区县</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 38</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 价值分析大屏（省） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/价值分析大屏（省）.png" alt="价值分析大屏（省）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">价值分析大屏（省）</div>
            <div style="font-size: 12px;">省级政企价值透视模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 分省</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 45</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 价值分析大屏（市） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/价值分析大屏（市）.png" alt="价值分析大屏（市）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">价值分析大屏（市）</div>
            <div style="font-size: 12px;">市级政企价值深耕模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 39</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 价值分析大屏（区县） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/价值分析大屏（区县）.png" alt="价值分析大屏（区县）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">价值分析大屏（区县）</div>
            <div style="font-size: 12px;">区县级基层价值细胞分析模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 区县</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 36</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 工单调度大屏（总） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/工单调度大屏（总）.png" alt="工单调度大屏（总）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">工单调度大屏（总）</div>
            <div style="font-size: 12px;">全国工单态势总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 52</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 工单调度大屏（省） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/工单调度大屏（省）.png" alt="工单调度大屏（省）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">工单调度大屏（省）</div>
            <div style="font-size: 12px;">省级工单作战视图模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 分省</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 47</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 工单调度大屏（市） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/工单调度大屏（市）.png" alt="工单调度大屏（市）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">工单调度大屏（市）</div>
            <div style="font-size: 12px;">市级全城工单实时监控模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 43</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 工单调度大屏（区县） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="images/工单调度大屏（区县）.png" alt="工单调度大屏（区县）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">工单调度大屏（区县）</div>
            <div style="font-size: 12px;">区县级街道工单分钟监控模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 区县</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 34</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 资源调度大屏（总） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/190/800/400" alt="资源调度大屏（总）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">资源调度大屏（总）</div>
            <div style="font-size: 12px;">全国云网资源总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 49</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 资源调度大屏（省） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/191/800/400" alt="资源调度大屏（省）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">资源调度大屏（省）</div>
            <div style="font-size: 12px;">省级战区资源作战视图模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 分省</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 44</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 资源调度大屏（市） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/192/800/400" alt="资源调度大屏（市）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">资源调度大屏（市）</div>
            <div style="font-size: 12px;">市级全域云网资源作战视图模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 41</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 资源调度大屏（区县） -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/193/800/400" alt="资源调度大屏（区县）" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">资源调度大屏（区县）</div>
            <div style="font-size: 12px;">区县级末梢资源实时感知网模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 区县</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 37</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 创建模板模态框 -->
  <div class="modal" id="createTemplateModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 创建大屏模板</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="createTemplateForm">
          <div class="form-group">
            <label for="templateName">模板名称</label>
            <input type="text" id="templateName" name="templateName" required placeholder="请输入模板名称">
          </div>
          <div class="form-group">
            <label for="templateLevel">模板级别</label>
            <select id="templateLevel" name="templateLevel" required>
              <option value="">请选择模板级别</option>
              <option value="national">全国级</option>
              <option value="provincial">省级</option>
              <option value="city">市级</option>
              <option value="district">区县级</option>
            </select>
          </div>
          <div class="form-group">
            <label for="templateIndustry">适用行业</label>
            <select id="templateIndustry" name="templateIndustry" required>
              <option value="">请选择适用行业</option>
              <option value="general">通用</option>
              <option value="retail">零售行业</option>
              <option value="finance">金融行业</option>
              <option value="manufacturing">制造业</option>
              <option value="service">服务业</option>
            </select>
          </div>
          <div class="form-group">
            <label for="templateDescription">模板描述</label>
            <textarea id="templateDescription" name="templateDescription" rows="3" placeholder="请输入模板描述"></textarea>
          </div>
          <div class="form-group">
            <label>模板布局</label>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-top: 8px;">
              <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; text-align: center; cursor: pointer;">
                <img src="https://picsum.photos/id/36/200/100" alt="布局1" style="width: 100%; height: 80px; object-fit: cover; margin-bottom: 8px;">
                <div style="font-size: 14px;">布局1</div>
              </div>
              <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; text-align: center; cursor: pointer;">
                <img src="https://picsum.photos/id/37/200/100" alt="布局2" style="width: 100%; height: 80px; object-fit: cover; margin-bottom: 8px;">
                <div style="font-size: 14px;">布局2</div>
              </div>
              <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; text-align: center; cursor: pointer;">
                <img src="https://picsum.photos/id/38/200/100" alt="布局3" style="width: 100%; height: 80px; object-fit: cover; margin-bottom: 8px;">
                <div style="font-size: 14px;">布局3</div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('createTemplateModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('createTemplateForm').submit()">创建</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // API基础URL
    const API_BASE_URL = 'http://localhost:8000/api';

    // 页面加载时调用接口记录访问
    window.addEventListener('load', async function() {
      try {
        await apiRequest(`${API_BASE_URL}/operation-views/page-visit`, 'POST', {
          action: 'page_load',
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          pageUrl: window.location.href,
          referrer: document.referrer
        });
        console.log('页面访问记录接口调用成功');
      } catch (error) {
        console.error('页面访问记录接口调用失败:', error);
        // 即使接口调用失败，也不影响页面正常加载
      }
    });

    // 封装fetch请求函数
    async function apiRequest(url, method, data = null) {
      try {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
          }
        };

        if (data) {
          options.body = JSON.stringify(data);
        }

        console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
        const response = await fetch(url, options);
        const result = await response.json();
        console.log(`接口响应:`, result);

        if (!response.ok) {
          throw new Error(result.message || `请求失败: ${response.status}`);
        }

        return result;
      } catch (error) {
        console.error('API请求错误:', error);
        // 不处理响应结果，静默处理错误
        throw error;
      }
    }



    // 创建模板按钮点击事件
    document.getElementById('createTemplateBtn').addEventListener('click', async function() {
      try {
        // 调用获取模板级别列表接口
        await apiRequest(`${API_BASE_URL}/operation-views/template-levels`, 'GET');
        console.log('获取模板级别列表接口调用成功');
        
        // 调用获取行业列表接口
        await apiRequest(`${API_BASE_URL}/operation-views/industries`, 'GET');
        console.log('获取行业列表接口调用成功');
        
        // 调用获取布局模板列表接口
        await apiRequest(`${API_BASE_URL}/operation-views/layout-templates`, 'GET');
        console.log('获取布局模板列表接口调用成功');
        
        // 调用获取模板配置选项接口
        await apiRequest(`${API_BASE_URL}/operation-views/template-options`, 'GET');
        console.log('获取模板配置选项接口调用成功');
        
      } catch (error) {
        console.error('获取创建模板下拉选择框数据失败:', error);
        // 即使接口调用失败，也不影响模态框显示
      }
      
      // 保持原有逻辑，显示模态框
      document.getElementById('createTemplateModal').classList.add('show');
    });

    // 创建模板表单提交
    document.getElementById('createTemplateForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('createTemplateForm')) {
        try {
          // 调用创建模板接口
          await apiRequest(`${API_BASE_URL}/operation-views/create-template`, 'POST', {
            templateName: document.getElementById('templateName').value,
            templateLevel: document.getElementById('templateLevel').value,
            templateIndustry: document.getElementById('templateIndustry').value,
            templateDescription: document.getElementById('templateDescription').value,
            action: 'create_template',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('创建模板接口调用成功');
        } catch (error) {
          console.error('创建模板接口调用失败:', error);
          // 即使接口调用失败，也继续执行原有的创建逻辑
        }
        
        // 保持原有逻辑
        alert('大屏模板创建成功！');
        document.getElementById('createTemplateModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });

    // 筛选模板函数
    async function filterTemplates() {
      const templateLevelSelect = document.getElementById('templateLevelFilter');
      const industryFilter = document.getElementById('industryFilter');
      const filterBtn = document.getElementById('filterBtn');
      const selectedLevel = templateLevelSelect ? templateLevelSelect.value : 'all';
      const selectedIndustry = industryFilter ? industryFilter.value : 'all';
      

      
      try {
        // 调用筛选模板接口
        await apiRequest(`${API_BASE_URL}/operation-views/filter-templates`, 'POST', {
          level: selectedLevel,
          industry: selectedIndustry,
          action: 'manual_filter',
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        });
        console.log('筛选模板接口调用成功');
      } catch (error) {
        console.error('筛选模板接口调用失败:', error);
        // 即使接口调用失败，也继续执行原有的筛选逻辑
      }
      
      // 保持原有逻辑
      const allTemplates = document.querySelectorAll('.card');
      allTemplates.forEach(template => {
        const templateLevel = template.querySelector('.fa-map-marker-alt').nextSibling.textContent.trim();
        const templateIndustry = template.querySelector('.fa-industry').nextSibling.textContent.trim();
        
        let showByLevel = false;
        let showByIndustry = false;
        
        // 检查模板级别筛选
        if (selectedLevel === 'all') {
          showByLevel = true;
        } else if (selectedLevel === 'national' && templateLevel === '全国') {
          showByLevel = true;
        } else if (selectedLevel === 'provincial' && templateLevel === '分省') {
          showByLevel = true;
        } else if (selectedLevel === 'city' && templateLevel === '市级') {
          showByLevel = true;
        } else if (selectedLevel === 'district' && templateLevel === '区县') {
          showByLevel = true;
        }
        
        // 检查行业筛选
        if (selectedIndustry === 'all') {
          showByIndustry = true;
        } else if (selectedIndustry === 'retail' && templateIndustry === '零售') {
          showByIndustry = true;
        } else if (selectedIndustry === 'finance' && templateIndustry === '金融') {
          showByIndustry = true;
        } else if (selectedIndustry === 'manufacturing' && templateIndustry === '制造业') {
          showByIndustry = true;
        } else if (selectedIndustry === 'service' && templateIndustry === '服务业') {
          showByIndustry = true;
        } else if (selectedIndustry === 'all' && templateIndustry === '通用') {
          showByIndustry = true;
        }
        
        // 同时满足两个条件才显示
        if (showByLevel && showByIndustry) {
          template.style.display = 'flex';
        } else {
          template.style.display = 'none';
        }
      });
    }

    // 模板级别筛选功能
    const templateLevelSelect = document.getElementById('templateLevelFilter');
    const industryFilter = document.getElementById('industryFilter');
    const allTemplates = document.querySelectorAll('.card');

    // 确保元素存在再绑定事件
    if (templateLevelSelect) {
      templateLevelSelect.addEventListener('change', async function() {
        const selectedLevel = this.value;
        
        try {
          // 调用模板筛选接口
          await apiRequest(`${API_BASE_URL}/operation-views/filter-templates`, 'POST', {
            level: selectedLevel,
            action: 'level_filter',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('模板级别筛选接口调用成功');
        } catch (error) {
          console.error('模板级别筛选接口调用失败:', error);
          // 即使接口调用失败，也继续执行原有的筛选逻辑
        }
        
        // 保持原有逻辑
        allTemplates.forEach(template => {
          const templateLevel = template.querySelector('.fa-map-marker-alt').nextSibling.textContent.trim();
          
          if (selectedLevel === 'all') {
            template.style.display = 'flex';
          } else if (selectedLevel === 'national' && templateLevel === '全国') {
            template.style.display = 'flex';
          } else if (selectedLevel === 'provincial' && templateLevel === '分省') {
            template.style.display = 'flex';
          } else if (selectedLevel === 'city' && templateLevel === '市级') {
            template.style.display = 'flex';
          } else if (selectedLevel === 'district' && templateLevel === '区县') {
            template.style.display = 'flex';
          } else {
            template.style.display = 'none';
          }
        });
      });
    } else {
      console.error('未找到模板级别筛选元素');
    }

    // 行业筛选功能
    if (industryFilter) {
      industryFilter.addEventListener('change', async function() {
        const selectedIndustry = this.value;
        
        try {
          // 调用行业筛选接口
          await apiRequest(`${API_BASE_URL}/operation-views/filter-by-industry`, 'POST', {
            industry: selectedIndustry,
            action: 'industry_filter',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          });
          console.log('行业筛选接口调用成功');
        } catch (error) {
          console.error('行业筛选接口调用失败:', error);
          // 即使接口调用失败，也继续执行原有的筛选逻辑
        }
        
        // 保持原有逻辑
        allTemplates.forEach(template => {
          const templateIndustry = template.querySelector('.fa-industry').nextSibling.textContent.trim();
          
          if (selectedIndustry === 'all') {
            template.style.display = 'flex';
          } else if (selectedIndustry === 'retail' && templateIndustry === '零售') {
            template.style.display = 'flex';
          } else if (selectedIndustry === 'finance' && templateIndustry === '金融') {
            template.style.display = 'flex';
          } else if (selectedIndustry === 'manufacturing' && templateIndustry === '制造业') {
            template.style.display = 'flex';
          } else if (selectedIndustry === 'service' && templateIndustry === '服务业') {
            template.style.display = 'flex';
          } else {
            template.style.display = 'none';
          }
        });
      });
    } else {
      console.error('未找到行业筛选元素');
    }

    // 预览功能实现
    const dashboardImages = {
      "全国运营监控大屏": "images/资源调度大屏（总）.png",
      "省份运营分析大屏": "images/资源调度大屏（省）.png",
      "城市运营监控大屏": "images/资源调度大屏（市）.png",
      "区县运营分析大屏": "images/资源调度大屏（区县）.png",
      "分类动态监控大屏（总）": "images/分类动态监控大屏（总）.png",
      "价值分析大屏（总）": "images/价值分析大屏（总）.png",
      "分类动态监控大屏（市）": "images/分类动态监控大屏（市）.png",
      "分类动态监控大屏（区/县）": "images/分类动态监控大屏（区县）.png",
      "价值分析大屏（省）": "images/价值分析大屏（省）.png",
      "价值分析大屏（市）": "images/价值分析大屏（市）.png",
      "价值分析大屏（区县）": "images/价值分析大屏（区县）.png",
      "工单调度大屏（总）": "images/工单调度大屏（总）.png",
      "工单调度大屏（省）": "images/工单调度大屏（省）.png",
      "工单调度大屏（市）": "images/工单调度大屏（市）.png",
      "工单调度大屏（区县）": "images/工单调度大屏（区县）.png"
    };

    // 创建预览模态框元素
    const previewModal = document.createElement('div');
    previewModal.id = 'previewModal';
    previewModal.style.cssText = `
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.7);
      z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
      margin: 5% auto;
      padding: 20px;
      width: 80%;
      max-width: 1200px;
      background-color: #000;
      border-radius: 8px;
      position: relative;
    `;

    const closeBtn = document.createElement('span');
    closeBtn.style.cssText = `
      color: #fff;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    `;
    closeBtn.textContent = '×';

    const previewImg = document.createElement('img');
    previewImg.style.cssText = `
      width: 100%;
      height: auto;
      border-radius: 4px;
    `;

    modalContent.appendChild(closeBtn);
    modalContent.appendChild(previewImg);
    previewModal.appendChild(modalContent);
    document.body.appendChild(previewModal);

    // 关闭模态框
    closeBtn.addEventListener('click', () => {
      previewModal.style.display = 'none';
    });

    // 点击模态框外部关闭
    previewModal.addEventListener('click', (e) => {
      if (e.target === previewModal) {
        previewModal.style.display = 'none';
      }
    });

    // 为所有预览按钮添加点击事件
    document.querySelectorAll('.btn-primary').forEach(button => {
      if (button.textContent.includes('预览')) {
        button.addEventListener('click', async function() {
          const cardTitle = this.closest('.card').querySelector('div[style*="font-size: 16px; font-weight: 500;"]').textContent.trim();
          
          try {
            // 调用预览模板接口
            await apiRequest(`${API_BASE_URL}/operation-views/preview-template`, 'POST', {
              templateName: cardTitle,
              action: 'preview_template',
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('预览模板接口调用成功');
          } catch (error) {
            console.error('预览模板接口调用失败:', error);
            // 即使接口调用失败，也继续执行原有的预览逻辑
          }
          
          // 保持原有逻辑
          const imgSrc = dashboardImages[cardTitle];
          
          if (imgSrc) {
            previewImg.src = imgSrc;
            previewImg.alt = cardTitle;
            previewModal.style.display = 'block';
          } else {
            console.error(`未找到 ${cardTitle} 的预览图片`);
            alert('预览图片不存在');
          }
        });
      }
    });

    // 为所有编辑按钮添加点击事件
    document.querySelectorAll('.btn').forEach(button => {
      if (button.textContent.includes('编辑') && !button.classList.contains('edit-template-btn')) {
        button.addEventListener('click', async function() {
          const cardTitle = this.closest('.card').querySelector('div[style*="font-size: 16px; font-weight: 500;"]').textContent.trim();
          
          try {
            // 调用编辑模板接口
            await apiRequest(`${API_BASE_URL}/operation-views/edit-template`, 'POST', {
              templateName: cardTitle,
              action: 'edit_template',
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent
            });
            console.log('编辑模板接口调用成功');
          } catch (error) {
            console.error('编辑模板接口调用失败:', error);
            // 即使接口调用失败，也继续执行原有的编辑逻辑
          }
          
          // 保持原有逻辑
          alert(`正在编辑模板: ${cardTitle}`);
        });
      }
    });
  </script>
</body>
</html>