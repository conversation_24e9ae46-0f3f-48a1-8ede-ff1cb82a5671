<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 穿透管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .tab-content {
        display: none;
        padding: 20px 0;
      }
      .tab-content.active {
        display: block;
      }
      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 20px;
      }
      .tab {
        padding: 10px 20px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
      }
      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">任务完成通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">任务#10086已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">任务告警</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">任务#10087执行失败</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="data_permission_control.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child active" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-file-alt page-title-icon"></i>
        穿透管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">五级穿透调度</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">五级穿透</a></div>
        <div class="breadcrumb-item active">穿透管理</div>
      </div>

      <!-- 标签页 -->
      <div class="tabs">
        <div class="tab active" data-tab-target="content-management">穿透内容管理</div>
        <div class="tab" data-tab-target="key-info-config">穿透关键信息配置</div>
        <div class="tab" data-tab-target="template-management">穿透模板管理</div>
        <div class="tab" data-tab-target="performance-monitoring">穿透性能监控</div>
        <div class="tab" data-tab-target="record-analysis">穿透记录分析</div>
        <div class="tab" data-tab-target="security-protection">穿透安全防护</div>
        <div class="tab" data-tab-target="trace-management">历史操作记录</div>
      </div>

      <div class="tab-content active" id="content-management">
        <div id="contentManagementModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透内容管理</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('contentManagementModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addContentForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="contentId">
                      内容ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="contentId" placeholder="请输入内容ID" required />
                  </div>
                  <div>
                    <label for="contentTitle">
                      内容标题
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="contentTitle" placeholder="请输入内容标题" required />
                  </div>
                  <div>
                    <label for="contentType">
                      内容类型
                      <span style="color: red">*</span>
                    </label>
                    <select id="contentType" required>
                      <option value="">请选择内容类型</option>
                      <option value="page">页面</option>
                      <option value="form">表单</option>
                      <option value="report">报表</option>
                      <option value="chart">图表</option>
                    </select>
                  </div>
                  <div>
                    <label for="editor">
                      编辑人
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="editor" placeholder="请输入编辑人" required />
                  </div>
                  <div>
                    <label for="editTime">
                      编辑时间
                      <span style="color: red">*</span>
                    </label>
                    <input type="datetime-local" id="editTime" required />
                  </div>
                  <div>
                    <label for="contentStatus">
                      状态
                      <span style="color: red">*</span>
                    </label>
                    <select id="contentStatus" required>
                      <option value="">请选择状态</option>
                      <option value="draft">草稿</option>
                      <option value="published">已发布</option>
                      <option value="archived">已归档</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2">
                    <label for="pageContent">页面内容</label>
                    <textarea id="pageContent" rows="6" placeholder="请输入页面内容"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addContentForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存内容</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透内容管理列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addContentManagement" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透内容
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>内容ID</th>
                    <th>内容标题</th>
                    <th>内容类型</th>
                    <th>编辑人</th>
                    <th>编辑时间</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>CM001</td>
                    <td>客户信息查询页面</td>
                    <td>页面</td>
                    <td>张三</td>
                    <td>2023-05-20 10:00:00</td>
                    <td><span class="tag tag-success">已发布</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>CM002</td>
                    <td>业务办理表单</td>
                    <td>表单</td>
                    <td>李四</td>
                    <td>2023-05-21 11:30:00</td>
                    <td><span class="tag tag-success">已发布</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>CM003</td>
                    <td>销售报表</td>
                    <td>报表</td>
                    <td>王五</td>
                    <td>2023-05-22 14:15:00</td>
                    <td><span class="tag tag-warning">草稿</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content" id="trace-management">
        <div id="traceManagementModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增历史操作记录</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('traceManagementModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addTraceForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="traceCode">
                      留痕编号
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="traceCode" placeholder="请输入留痕编号" required />
                  </div>
                  <div>
                    <label for="traceName">
                      留痕名称
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="traceName" placeholder="请输入留痕名称" required />
                  </div>
                  <div>
                    <label for="impactObject">
                      影响对象
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="impactObject" placeholder="请输入影响对象" required />
                  </div>
                  <div>
                    <label for="operationType">
                      操作类型
                      <span style="color: red">*</span>
                    </label>
                    <select id="operationType" required>
                      <option value="">请选择操作类型</option>
                      <option value="add">新增</option>
                      <option value="edit">修改</option>
                      <option value="delete">删除</option>
                      <option value="query">查询</option>
                    </select>
                  </div>
                  <div>
                    <label for="operator">
                      操作员
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="operator" placeholder="请输入操作员" required />
                  </div>
                  <div>
                    <label for="operationTime">
                      操作时间
                      <span style="color: red">*</span>
                    </label>
                    <input type="datetime-local" id="operationTime" required />
                  </div>
                  <div style="grid-column: span 2">
                    <label for="remark">备注</label>
                    <textarea id="remark" rows="4" placeholder="请输入备注信息"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addTraceForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存留痕</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">历史操作记录列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <!-- <button id="addTraceManagement" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透关键信息配置
                </button> -->
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>留痕编号</th>
                    <th>留痕名称</th>
                    <th>影响对象</th>
                    <th>操作类型</th>
                    <th>操作员</th>
                    <th>操作时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>TR001</td>
                    <td>添加穿透权限</td>
                    <td>权限配置表</td>
                    <td>新增</td>
                    <td>管理员</td>
                    <td>2023-05-20 10:00:00</td>
                    <td>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>TR002</td>
                    <td>修改穿透模板</td>
                    <td>模板配置表</td>
                    <td>修改</td>
                    <td>张三</td>
                    <td>2023-05-21 11:30:00</td>
                    <td>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>TR003</td>
                    <td>删除过期权限</td>
                    <td>权限配置表</td>
                    <td>删除</td>
                    <td>李四</td>
                    <td>2023-05-22 14:15:00</td>
                    <td>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="key-info-config">
        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透关键信息配置列表</div>
              <div style="display: flex; align-items: center">
                <div class="search-box" style="width: 250px; margin-right: 12px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索配置..." />
                </div>
                <button id="addKeyInfoBtn" class="btn btn-primary" style="height: 40px; margin-top: -21px">
                  <i class="fas fa-plus"></i>
                  新增穿透关键信息配置
                </button>
              </div>
            </div>
          </div>

          <!-- 模态框 -->
          <div id="keyInfoModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
            <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
              <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
                <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透关键信息配置</h3>
                <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('keyInfoModal')">&times;</span>
              </div>
              <div class="modal-body" style="padding: 20px">
                <form id="addKeyInfoForm">
                  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                    <div>
                      <label for="configCode">
                        配置编号
                        <span style="color: red">*</span>
                      </label>
                      <input type="text" id="configCode" placeholder="请输入配置编号" required />
                    </div>
                    <div>
                      <label for="configName">
                        配置名称
                        <span style="color: red">*</span>
                      </label>
                      <input type="text" id="configName" placeholder="请输入配置名称" required />
                    </div>
                    <div>
                      <label for="province">
                        省公司
                        <span style="color: red">*</span>
                      </label>
                      <input type="text" id="province" placeholder="请输入省公司名称" required />
                    </div>
                    <div>
                      <label for="systemName">
                        系统名称
                        <span style="color: red">*</span>
                      </label>
                      <input type="text" id="systemName" placeholder="请输入系统名称" required />
                    </div>
                    <div>
                      <label for="url">
                        URL链接
                        <span style="color: red">*</span>
                      </label>
                      <input type="text" id="url" placeholder="请输入URL链接" required />
                    </div>
                    <div>
                      <label for="verificationMethod">
                        验证方式
                        <span style="color: red">*</span>
                      </label>
                      <select id="verificationMethod" required>
                        <option value="">请选择验证方式</option>
                        <option value="token">Token验证</option>
                        <option value="certificate">证书验证</option>
                        <option value="sso">单点登录</option>
                      </select>
                    </div>
                    <div style="grid-column: span 2">
                      <label for="configDescription">配置描述</label>
                      <textarea id="configDescription" rows="4" placeholder="请输入配置描述信息"></textarea>
                    </div>
                  </div>
                  <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                    <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addKeyInfoForm')">重置</button>
                    <button type="submit" class="btn btn-primary">保存配置</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>配置编号</th>
                    <th>配置名称</th>
                    <th>省公司</th>
                    <th>系统名称</th>
                    <th>URL链接</th>
                    <th>验证方式</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>KC001</td>
                    <td>江苏CRM系统配置</td>
                    <td>江苏</td>
                    <td>CRM系统</td>
                    <td>https://js.crm.example.com</td>
                    <td>单点登录</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>KC002</td>
                    <td>浙江BOSS系统配置</td>
                    <td>浙江</td>
                    <td>BOSS系统</td>
                    <td>https://zj.boss.example.com</td>
                    <td>Token验证</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>KC003</td>
                    <td>上海数据分析系统配置</td>
                    <td>上海</td>
                    <td>数据分析系统</td>
                    <td>https://sh.analysis.example.com</td>
                    <td>证书验证</td>
                    <td><span class="tag tag-warning">禁用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="template-management">
        <div id="templateManagementModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透模板管理</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('templateManagementModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addTemplateForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="templateId">
                      模板ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="templateId" placeholder="请输入模板ID" required />
                  </div>
                  <div>
                    <label for="templateName">
                      模板名称
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="templateName" placeholder="请输入模板名称" required />
                  </div>
                  <div>
                    <label for="templateType">
                      模板类型
                      <span style="color: red">*</span>
                    </label>
                    <select id="templateType" required>
                      <option value="">请选择模板类型</option>
                      <option value="page">页面模板</option>
                      <option value="form">表单模板</option>
                      <option value="report">报表模板</option>
                    </select>
                  </div>
                  <div>
                    <label for="scope">
                      使用范围
                      <span style="color: red">*</span>
                    </label>
                    <select id="scope" required>
                      <option value="">请选择使用范围</option>
                      <option value="all">全部省公司</option>
                      <option value="province">指定省公司</option>
                      <option value="city">指定市公司</option>
                    </select>
                  </div>
                  <div id="provinceField" style="display: none">
                    <label for="templateProvince">适用省公司</label>
                    <input type="text" id="templateProvince" placeholder="请输入适用省公司" />
                  </div>
                  <div>
                    <label for="templateStatus">
                      状态
                      <span style="color: red">*</span>
                    </label>
                    <select id="templateStatus" required>
                      <option value="">请选择状态</option>
                      <option value="active">启用</option>
                      <option value="inactive">禁用</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2">
                    <label for="templateContent">模板内容</label>
                    <textarea id="templateContent" rows="6" placeholder="请输入模板内容"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addTemplateForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存模板</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透模板管理列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addTemplateManagement" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透模板
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>模板ID</th>
                    <th>模板名称</th>
                    <th>模板类型</th>
                    <th>使用范围</th>
                    <th>适用省公司</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>TM001</td>
                    <td>标准客户信息页面模板</td>
                    <td>页面模板</td>
                    <td>全部省公司</td>
                    <td>全部</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>TM002</td>
                    <td>江苏业务表单模板</td>
                    <td>表单模板</td>
                    <td>指定省公司</td>
                    <td>江苏</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>TM003</td>
                    <td>销售报表模板</td>
                    <td>报表模板</td>
                    <td>指定市公司</td>
                    <td>上海</td>
                    <td><span class="tag tag-warning">禁用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="performance-monitoring">
        <div id="performanceManagementModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透性能监控</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('performanceManagementModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addPerformanceForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="monitoringId">
                      监控ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="monitoringId" placeholder="请输入监控ID" required />
                  </div>
                  <div>
                    <label for="monitoringName">
                      监控名称
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="monitoringName" placeholder="请输入监控名称" required />
                  </div>
                  <div>
                    <label for="pageId">
                      关联页面ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="pageId" placeholder="请输入关联页面ID" required />
                  </div>
                  <div>
                    <label for="alarmThreshold">
                      报警阈值
                      <span style="color: red">*</span>
                    </label>
                    <input type="number" id="alarmThreshold" placeholder="请输入报警阈值" required />
                  </div>
                  <div>
                    <label for="errorRate">
                      错误率
                      <span style="color: red">*</span>
                    </label>
                    <input type="number" step="0.01" id="errorRate" placeholder="请输入错误率" required />
                  </div>
                  <div>
                    <label for="monitoringStatus">
                      监控状态
                      <span style="color: red">*</span>
                    </label>
                    <select id="monitoringStatus" required>
                      <option value="">请选择监控状态</option>
                      <option value="active">启用</option>
                      <option value="inactive">禁用</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2">
                    <label for="monitoringDescription">监控描述</label>
                    <textarea id="monitoringDescription" rows="4" placeholder="请输入监控描述信息"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addPerformanceForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存监控</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透性能监控列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addPerformanceManagement" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透性能监控
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <!-- <div style="height: 300px; background-color: #f5f7fa; margin-bottom: 20px; display: flex; align-items: center; justify-content: center">
              <div style="text-align: center">
                <i class="fas fa-chart-line" style="font-size: 48px; color: var(--primary-color); margin-bottom: 16px"></i>
                <div>性能监控图表区域</div>
              </div>
            </div> -->
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>监控ID</th>
                    <th>监控名称</th>
                    <th>关联页面ID</th>
                    <th>报警阈值</th>
                    <th>错误率</th>
                    <th>当前访问量</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>PM001</td>
                    <td>客户信息页面监控</td>
                    <td>CM001</td>
                    <td>1000次/分钟</td>
                    <td>1%</td>
                    <td>850次/分钟</td>
                    <td><span class="tag tag-success">正常</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>PM002</td>
                    <td>业务办理表单监控</td>
                    <td>CM002</td>
                    <td>500次/分钟</td>
                    <td>0.5%</td>
                    <td>320次/分钟</td>
                    <td><span class="tag tag-success">正常</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>PM003</td>
                    <td>销售报表监控</td>
                    <td>CM003</td>
                    <td>200次/分钟</td>
                    <td>2%</td>
                    <td>180次/分钟</td>
                    <td><span class="tag tag-warning">警告</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="record-analysis">
        <div id="recordAnalysisModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透记录分析</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('recordAnalysisModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addRecordForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="recordId">
                      记录ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="recordId" placeholder="请输入记录ID" required />
                  </div>
                  <div>
                    <label for="recordName">
                      记录名称
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="recordName" placeholder="请输入记录名称" required />
                  </div>
                  <div>
                    <label for="logType">
                      日志类型
                      <span style="color: red">*</span>
                    </label>
                    <select id="logType" required>
                      <option value="">请选择日志类型</option>
                      <option value="access">访问日志</option>
                      <option value="operation">操作日志</option>
                      <option value="error">错误日志</option>
                    </select>
                  </div>
                  <div>
                    <label for="content">
                      内容
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="content" placeholder="请输入内容" required />
                  </div>
                  <div>
                    <label for="analysisPerson">
                      分析人
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="analysisPerson" placeholder="请输入分析人" required />
                  </div>
                  <div>
                    <label for="analysisTime">
                      分析时间
                      <span style="color: red">*</span>
                    </label>
                    <input type="datetime-local" id="analysisTime" required />
                  </div>
                  <div style="grid-column: span 2">
                    <label for="analysisResult">分析结果</label>
                    <textarea id="analysisResult" rows="4" placeholder="请输入分析结果"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addRecordForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存分析</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透记录分析列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addRecordAnalysis" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透记录分析
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>记录ID</th>
                    <th>记录名称</th>
                    <th>日志类型</th>
                    <th>分析人</th>
                    <th>分析时间</th>
                    <th>分析结果</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>RA001</td>
                    <td>访问量异常分析</td>
                    <td>访问日志</td>
                    <td>张三</td>
                    <td>2023-05-20 10:00:00</td>
                    <td>正常波动</td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>RA002</td>
                    <td>操作失败分析</td>
                    <td>操作日志</td>
                    <td>李四</td>
                    <td>2023-05-21 11:30:00</td>
                    <td>权限不足</td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>RA003</td>
                    <td>系统错误分析</td>
                    <td>错误日志</td>
                    <td>王五</td>
                    <td>2023-05-22 14:15:00</td>
                    <td>数据库连接失败</td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                      <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="security-protection">
        <div id="securityProtectionModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增穿透安全防护</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('securityProtectionModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="addSecurityForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px">
                  <div>
                    <label for="securityId">
                      防护ID
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="securityId" placeholder="请输入防护ID" required />
                  </div>
                  <div>
                    <label for="securityName">
                      防护名称
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="securityName" placeholder="请输入防护名称" required />
                  </div>
                  <div>
                    <label for="protectionType">
                      防护类型
                      <span style="color: red">*</span>
                    </label>
                    <select id="protectionType" required>
                      <option value="">请选择防护类型</option>
                      <option value="accessControl">访问控制</option>
                      <option value="dataEncryption">数据加密</option>
                      <option value="threatDetection">威胁检测</option>
                    </select>
                  </div>
                  <div>
                    <label for="recoveryTime">
                      恢复时间
                      <span style="color: red">*</span>
                    </label>
                    <input type="datetime-local" id="recoveryTime" required />
                  </div>
                  <div>
                    <label for="handler">
                      处理人
                      <span style="color: red">*</span>
                    </label>
                    <input type="text" id="handler" placeholder="请输入处理人" required />
                  </div>
                  <div>
                    <label for="securityStatus">
                      状态
                      <span style="color: red">*</span>
                    </label>
                    <select id="securityStatus" required>
                      <option value="">请选择状态</option>
                      <option value="active">启用</option>
                      <option value="inactive">禁用</option>
                      <option value="warning">警告</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2">
                    <label for="securityDescription">防护描述</label>
                    <textarea id="securityDescription" rows="4" placeholder="请输入防护描述信息"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end">
                  <button type="button" class="btn" style="margin-right: 12px" onclick="resetForm('addSecurityForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存防护</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">穿透安全防护列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addSecurityProtection" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增穿透安全防护
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>防护ID</th>
                    <th>防护名称</th>
                    <th>防护类型</th>
                    <th>恢复时间</th>
                    <th>处理人</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>SP001</td>
                    <td>IP访问控制</td>
                    <td>访问控制</td>
                    <td>2023-05-20 10:00:00</td>
                    <td>张三</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>SP002</td>
                    <td>敏感数据加密</td>
                    <td>数据加密</td>
                    <td>2023-05-21 11:30:00</td>
                    <td>李四</td>
                    <td><span class="tag tag-success">启用</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                  <tr>
                    <td>SP003</td>
                    <td>异常访问检测</td>
                    <td>威胁检测</td>
                    <td>2023-05-22 14:15:00</td>
                    <td>王五</td>
                    <td><span class="tag tag-warning">警告</span></td>
                    <td>
                      <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 16px">
              <div class="pagination">
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 标签页切换功能
      document.addEventListener('DOMContentLoaded', function () {
        const tabBtns = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
          btn.addEventListener('click', () => {
            const target = btn.getAttribute('data-tab-target');

            // 切换按钮状态
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            // 切换内容显示
            tabContents.forEach(content => {
              content.classList.remove('active');
              if (content.id === target) {
                content.classList.add('active');
              }
            });
          });
        });

        // 使用范围选择事件
        const scopeSelect = document.getElementById('scope');
        const provinceField = document.getElementById('provinceField');

        if (scopeSelect && provinceField) {
          scopeSelect.addEventListener('change', function () {
            if (this.value === 'province' || this.value === 'city') {
              provinceField.style.display = 'block';
            } else {
              provinceField.style.display = 'none';
            }
          });
        }

        // 表单提交处理
        document.getElementById('addTraceForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('历史操作记录已保存!');
        
        });

        const contentManagementModal = document.getElementById('contentManagementModal');
        const addContentManagement = document.getElementById('addContentManagement');
        addContentManagement.addEventListener('click', function () {
          contentManagementModal.style.display = 'flex';
        });

        // 弹窗控制逻辑
        const keyInfoModal = document.getElementById('keyInfoModal');
        const addKeyInfoBtn = document.getElementById('addKeyInfoBtn');

        // 打开弹窗
        addKeyInfoBtn.addEventListener('click', function () {
          keyInfoModal.style.display = 'flex';
        });

        const templateManagementModal = document.getElementById('templateManagementModal');
        const addTemplateManagement = document.getElementById('addTemplateManagement');
        addTemplateManagement.addEventListener('click', function () {
          templateManagementModal.style.display = 'flex';
        });

        const performanceManagementModal = document.getElementById('performanceManagementModal');
        const addPerformanceManagement = document.getElementById('addPerformanceManagement');
        addPerformanceManagement.addEventListener('click', function () {
          performanceManagementModal.style.display = 'flex';
        });

        const recordAnalysisModal = document.getElementById('recordAnalysisModal');
        const addRecordAnalysis = document.getElementById('addRecordAnalysis');
        addRecordAnalysis.addEventListener('click', function () {
          recordAnalysisModal.style.display = 'flex';
        });

        const securityProtectionModal = document.getElementById('securityProtectionModal');
        const addSecurityProtection = document.getElementById('addSecurityProtection');
        addSecurityProtection.addEventListener('click', function () {
          securityProtectionModal.style.display = 'flex';
        });

        // const traceManagementModal = document.getElementById('traceManagementModal');
        // const addTraceManagement = document.getElementById('addTraceManagement');
        // addTraceManagement.addEventListener('click', function () {
        //   traceManagementModal.style.display = 'flex';
        // });

        // 关闭弹窗 - 点击外部区域
        window.addEventListener('click', function (event) {
          if (event.target === keyInfoModal) {
            keyInfoModal.style.display = 'none';
          }
        });

        // 表单提交处理
        document.getElementById('addKeyInfoForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透关键信息配置已保存!');
          document.getElementById('key-info-config').classList.add('active');
        
          // 关闭弹窗
          keyInfoModal.style.display = 'none';
        });

        document.getElementById('addContentForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透内容管理已保存!');
          document.getElementById('content-management').classList.add('active');
          contentManagementModal.style.display = 'none';
        });

        document.getElementById('addTemplateForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透模板管理已保存!');
          document.getElementById('template-management').classList.add('active');
          templateManagementModal.style.display = 'none';
        });

        document.getElementById('addPerformanceForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透性能监控已保存!');
          document.getElementById('performance-monitoring').classList.add('active');
          performanceManagementModal.style.display = 'none';
        });

        document.getElementById('addRecordForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透记录分析已保存!');
          document.getElementById('record-analysis').classList.add('active');
           recordAnalysisModal.style.display = 'none';
        });

        document.getElementById('addSecurityForm').addEventListener('submit', function (e) {
          e.preventDefault();
          // 模拟表单提交和保存
          alert('穿透安全防护已保存!');
          document.getElementById('security-protection').classList.add('active');
         securityProtectionModal.style.display = 'none';
        });
      });

      function closeModal(id) {
        const modal = document.getElementById(id);
        modal.style.display = 'none';
      }

      // 重置表单函数
      function resetForm(formId) {
        document.getElementById(formId).reset();
        // 重置使用范围相关字段
        const provinceField = document.getElementById('provinceField');
        if (provinceField) {
          provinceField.style.display = 'none';
        }
      }
    </script>
  </body>
</html>
