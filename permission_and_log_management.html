<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 统一运营门户</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child active" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-globe-asia page-title-icon"></i>
        权限日志管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb" style="font-size: 12px; border-bottom: 1px solid #e0e0e0; padding-bottom: 8px; margin-bottom: 16px">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">统一运营门户</a></div>
        <div class="breadcrumb-item active" style="font-weight: bold">权限日志管理</div>
      </div>

      <!-- 视图权限管理 -->
      <div class="tab-content" id="viewPermission">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; margin-bottom: 20px; gap: 12px; font-size: 14px">
          <div style="display: flex; flex-wrap: wrap; gap: 12px">
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作角色</label>
              <div class="search-box" style="width: 150px; margin-bottom: 0">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="清输入" style="color: var(--text-primary)" />
                <style>
                  ::placeholder {
                    color: var(--text-tertiary);
                  }
                </style>
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作工号</label>
              <div class="search-box" style="width: 220px; margin-bottom: 0">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="请输入操作工号..." style="color: var(--text-primary)" />
                <style>
                  ::placeholder {
                    color: var(--text-tertiary);
                  }
                </style>
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作时间</label>
              <div style="display: flex; gap: 8px; align-items: center">
                <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
                <span>至</span>
                <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作类型</label>
              <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 150px; color: var(--text-tertiary)">
                <option value="" selected style="color: var(--text-tertiary)"></option>
                <option value="all" style="color: var(--text-primary)">全部操作类型</option>
                <option value="create" style="color: var(--text-primary)">新增</option>
                <option value="edit" style="color: var(--text-primary)">编辑</option>
                <option value="delete" style="color: var(--text-primary)">删除</option>
                <option value="authorize" style="color: var(--text-primary)">授权</option>
                <option value="empower" style="color: var(--text-primary)">赋权</option>
              </select>
            </div>
            <button class="btn btn-primary" style="align-self: flex-end; margin-top: 24px">
              <i class="fas fa-search"></i>
              搜索
            </button>
          </div>
          <div style="display: flex">
            <button class="btn btn-primary" data-modal-target="addPermissionModal">
              <i class="fas fa-plus"></i>
              新增权限
            </button>
          </div>
        </div>

        <!-- 权限列表 -->
        <div class="card">
          <div class="table-container">
            <table class="table">
              <thead style="font-size: 14px; font-weight: bold">
                <tr>
                  <th>操作角色</th>
                  <th>操作工号</th>
                  <th>操作时间</th>
                  <th>操作类型</th>
                  <th>操作内容</th>
                  <th>操作详情</th>
                </tr>
              </thead>
              <tbody style="font-size: 14px; font-weight: normal">
                <tr>
                  <td>全国运营大屏查看权限</td>
                  <td>允许查看全国运营监控大屏</td>
                  <td>全国运营监控大屏</td>
                  <td>管理员</td>
                  <td>2023-06-01 14:30</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--primary-color)">
                      <i class="fas fa-link"></i>
                      关联工号
                    </button>
                  </td>
                </tr>
                <tr>
                  <td>数据分析报告查看权限</td>
                  <td>允许查看各类数据分析报告</td>
                  <td>所有分析报告</td>
                  <td>管理员</td>
                  <td>2023-06-10 10:15</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--primary-color)">
                      <i class="fas fa-link"></i>
                      关联工号
                    </button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>数据编辑权限</td>
                  <td>允许编辑和修改平台数据</td>
                  <td>所有数据模块</td>
                  <td>管理员</td>
                  <td>2023-06-15 09:45</td>
                  <td><span class="tag tag-warning">禁用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--primary-color)">
                      <i class="fas fa-link"></i>
                      关联工号
                    </button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item disabled"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item disabled"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>

      <!-- 权限日志管理 -->
      <div class="tab-content active" id="permissionLog">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; font-size: 14px">
          <div style="display: flex; width: 100%; flex-wrap: wrap; gap: 12px">
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作角色</label>
              <div class="search-box" style="width: 150px; margin-bottom: 0">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="清输入" style="color: var(--text-primary)" />
                <style>
                  ::placeholder {
                    color: var(--text-tertiary);
                  }
                </style>
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作工号</label>
              <div class="search-box" style="width: 220px; margin-bottom: 0">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="请输入操作工号..." />
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作时间</label>
              <div style="display: flex; gap: 8px; align-items: center">
                <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
                <span>至</span>
                <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
              </div>
            </div>
            <div style="display: flex; flex-direction: column">
              <label style="font-size: 14px; font-weight: bold; margin-bottom: 4px">操作类型</label>
              <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 150px; color: var(--text-tertiary)">
                <option value="" selected style="color: var(--text-tertiary)"></option>
                <option value="all" style="color: var(--text-primary)">全部操作类型</option>
                <option value="create" style="color: var(--text-primary)">新增</option>
                <option value="edit" style="color: var(--text-primary)">编辑</option>
                <option value="delete" style="color: var(--text-primary)">删除</option>
              </select>
            </div>
            <button id="searchBtn" class="btn btn-primary" style="height: 32px; padding: 6px 12px; font-size: 12px; font-weight: bold; white-space: nowrap; margin-top: 23px; display: flex; align-items: center; justify-content: center" onclick="callApi('http://120.48.171.191:5000/test/searchBtn')">
              <i class="fas fa-search"></i>
              搜索
            </button>
          </div>
          <div style="display: flex; justify-content: flex-end; width: auto; gap: 12px">
            <button id="exportBtn" class="btn" style="margin-right: 12px; background-color: var(--primary-color); color: white; font-size: 12px; font-weight: bold; white-space: nowrap" onclick="callApi('http://120.48.171.191:5000/test/exportBtn')">
              <i class="fas fa-download"></i>
              导出
            </button>
          </div>
        </div>

        <!-- 日志列表 -->
        <div class="card">
          <div class="table-container">
            <table class="table">
              <thead style="font-size: 14px; font-weight: bold">
                <tr>
                  <th>操作角色</th>
                  <th>操作工号</th>
                  <th>操作时间</th>
                  <th>操作类型</th>
                  <th>操作内容</th>
                  <th>操作详情</th>
                </tr>
              </thead>
              <tbody style="font-size: 14px; font-weight: normal">
                <tr>
                  <td>地市视图角色</td>
                  <td>admin</td>
                  <td>2025/07/01 13:13:13</td>
                  <td><span class="tag tag-info">编辑</span></td>
                  <td>变更角色</td>
                  <td><button id="detailBtn_1" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px" data-modal-target="operationDetailModal" onclick="callApi('http://120.48.171.191:5000/test/detailBtn_1')">查看</button></td>
                </tr>
                <tr>
                  <td>全国视图角色</td>
                  <td>admin</td>
                  <td>2025/06/23 11:11:11</td>
                  <td><span class="tag tag-danger">删除</span></td>
                  <td>删除角色</td>
                  <td><button id="detailBtn_2" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px" data-modal-target="operationDetailModal" onclick="callApi('http://120.48.171.191:5000/test/detailBtn_2')">查看</button></td>
                </tr>
                <tr>
                  <td>地市视图角色</td>
                  <td>admin</td>
                  <td>2025/06/21 09:10:10</td>
                  <td><span class="tag tag-warning">赋权</span></td>
                  <td>为hbsj-wzs赋权</td>
                  <td><button id="detailBtn_3" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px" data-modal-target="operationDetailModal" onclick="callApi('http://120.48.171.191:5000/test/detailBtn_3')">查看</button></td>
                </tr>
                <tr>
                  <td>地市视图角色</td>
                  <td>admin</td>
                  <td>2025/06/20 10:10:10</td>
                  <td><span class="tag tag-primary">新增</span></td>
                  <td>创建角色</td>
                  <td><button id="detailBtn_4" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px" data-modal-target="operationDetailModal" onclick="callApi('http://120.48.171.191:5000/test/detailBtn_4')">查看</button></td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item" style="color: #999"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item" style="color: #999"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增权限模态框 -->
    <div class="modal" id="addPermissionModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            新增视图权限
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addPermissionForm">
            <div class="form-group">
              <label for="permissionName">权限名称</label>
              <input type="text" id="permissionName" name="permissionName" required placeholder="请输入权限名称" />
            </div>
            <div class="form-group">
              <label for="permissionDesc">权限描述</label>
              <textarea id="permissionDesc" name="permissionDesc" rows="3" required placeholder="请输入权限描述"></textarea>
            </div>
            <div class="form-group">
              <label for="relatedView">关联视图</label>
              <select id="relatedView" name="relatedView" required>
                <option value="">请选择关联视图</option>
                <option value="national_screen">全国运营监控大屏</option>
                <option value="provincial_screen">省份运营分析大屏</option>
                <option value="city_screen">城市运营监控大屏</option>
                <option value="district_screen">区县运营分析大屏</option>
                <option value="all_reports">所有分析报告</option>
              </select>
            </div>
            <div class="form-group">
              <label for="permissionStatus">权限状态</label>
              <div style="display: flex; gap: 16px">
                <label style="display: flex; align-items: center">
                  <input type="radio" name="permissionStatus" value="active" checked />
                  启用
                </label>
                <label style="display: flex; align-items: center">
                  <input type="radio" name="permissionStatus" value="inactive" />
                  禁用
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addPermissionModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('addPermissionForm').submit()">保存</button>
        </div>
      </div>
    </div>

    <!-- 编辑角色模态框 -->
    <!-- 注意：模态框ID保持不变，因为按钮的data-modal-target属性使用的是editPermissionModal -->
    <div class="modal" id="editPermissionModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-edit"></i>
            编辑角色
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="editRoleForm">
            <div class="form-group">
              <label for="roleName">角色名称</label>
              <input type="text" id="roleName" name="roleName" required placeholder="请输入角色名称" />
            </div>
            <div class="form-group">
              <label for="roleDesc">角色描述</label>
              <textarea id="roleDesc" name="roleDesc" rows="3" required placeholder="请输入角色描述"></textarea>
            </div>
            <div class="form-group">
              <label>关联视图</label>
              <div style="margin-top: 8px">
                <div style="margin-bottom: 12px">
                  <div style="font-weight: 500; margin-bottom: 8px">分类动态监控大屏</div>
                  <div style="display: flex; flex-wrap: wrap; gap: 24px">
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="dynamic_national" />
                      全国
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="dynamic_provincial" />
                      省分
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="dynamic_city" />
                      地市
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="dynamic_district" />
                      区县
                    </label>
                  </div>
                </div>
                <div style="margin-bottom: 12px">
                  <div style="font-weight: 500; margin-bottom: 8px">价值分析大屏</div>
                  <div style="display: flex; flex-wrap: wrap; gap: 24px">
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="value_national" />
                      全国
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="value_provincial" />
                      省分
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="value_city" />
                      地市
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="value_district" />
                      区县
                    </label>
                  </div>
                </div>
                <div style="margin-bottom: 12px">
                  <div style="font-weight: 500; margin-bottom: 8px">工单调度大屏</div>
                  <div style="display: flex; flex-wrap: wrap; gap: 24px">
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="ticket_national" />
                      全国
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="ticket_provincial" />
                      省分
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="ticket_city" />
                      地市
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="ticket_district" />
                      区县
                    </label>
                  </div>
                </div>
                <div>
                  <div style="font-weight: 500; margin-bottom: 8px">资源调度大屏</div>
                  <div style="display: flex; flex-wrap: wrap; gap: 24px">
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="resource_national" />
                      全国
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="resource_provincial" />
                      省分
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="resource_city" />
                      地市
                    </label>
                    <label style="display: flex; align-items: center">
                      <input type="checkbox" name="relatedViews" value="resource_district" />
                      区县
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="roleStatus">角色状态</label>
              <div style="display: flex; gap: 16px; margin-top: 8px">
                <label style="display: flex; align-items: center">
                  <input type="radio" name="roleStatus" value="active" checked />
                  有效
                </label>
                <label style="display: flex; align-items: center">
                  <input type="radio" name="roleStatus" value="inactive" />
                  禁用
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editPermissionModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('editRoleForm').submit()">保存修改</button>
        </div>
      </div>
    </div>

    <!-- 操作详情模态框 -->
    <div class="modal" id="operationDetailModal">
      <div class="modal-content" style="width: 800px; max-width: 90%">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-info-circle"></i>
            操作详情
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body" style="display: flex; gap: 20px; flex-wrap: wrap">
          <!-- 变更前卡片 -->
          <div class="card" style="flex: 1; min-width: 300px">
            <div class="card-header" style="background-color: #f5f5f5; padding: 12px; font-weight: bold">变更前</div>
            <div class="card-body" style="padding: 16px">
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色名称：</span>
                <span>地市视图角色</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色状态：</span>
                <span class="tag tag-success">启用</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">关联视图：</span>
                <span>省分运营大屏、地市运营大屏</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">关联工号：</span>
                <span>ZY-SJ-张三、SF-DS-张三</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色描述：</span>
                <span></span>
              </div>
            </div>
          </div>
          <!-- 变更后卡片 -->
          <div class="card" style="flex: 1; min-width: 300px">
            <div class="card-header" style="background-color: #f5f5f5; padding: 12px; font-weight: bold">变更后</div>
            <div class="card-body" style="padding: 16px">
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色名称：</span>
                <span>地市视图角色</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色状态：</span>
                <span class="tag tag-success">启用</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">关联视图：</span>
                <span>地市运营大屏</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">关联工号：</span>
                <span>SF-DS-张三</span>
              </div>
              <div style="margin-bottom: 12px">
                <span style="font-weight: 500; display: inline-block; width: 100px">角色描述：</span>
                <span>专用于查看地市运营大屏的角色</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('operationDetailModal').classList.remove('show')">关闭</button>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 导出模态框 - 包含3秒倒计时功能
      document.addEventListener('DOMContentLoaded', function () {
        // 创建导出模态框
        const exportModalHTML = `
        <div class="modal" id="exportModal">
          <div class="modal-content" style="width: 400px; text-align: center;">
            <div class="modal-header">
              <div class="modal-title"><i class="fas fa-download"></i> 数据导出</div>
              <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" style="padding: 30px 20px;">
              <div style="font-size: 16px; margin-bottom: 20px;">数据导出中，请稍候...</div>
              <div style="font-size: 24px; font-weight: bold; margin-bottom: 20px;">倒计时: <span id="countdown">3</span> 秒</div>
              <div style="width: 100%; height: 10px; background-color: #f0f0f0; border-radius: 5px;">
                <div id="progressBar" style="width: 100%; height: 100%; background-color: var(--primary-color); border-radius: 5px; transition: width 1s linear;"></div>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-primary" id="exportCancelBtn">取消</button>
            </div>
          </div>
        </div>
      `;

        // 添加模态框到body
        document.body.insertAdjacentHTML('beforeend', exportModalHTML);

        // 获取模态框元素
        const exportModal = document.getElementById('exportModal');
        const countdownEl = document.getElementById('countdown');
        const progressBar = document.getElementById('progressBar');
        const exportCancelBtn = document.getElementById('exportCancelBtn');

        // 导出按钮点击事件
        document.getElementById('exportBtn').addEventListener('click', function () {
          // 显示模态框
          exportModal.classList.add('show');

          // 初始化倒计时
          let countdown = 3;
          countdownEl.textContent = countdown;
          progressBar.style.width = '100%';

          // 倒计时定时器
          const timer = setInterval(function () {
            countdown--;
            countdownEl.textContent = countdown;
            progressBar.style.width = (countdown / 3) * 100 + '%';

            if (countdown <= 0) {
              clearInterval(timer);
              // 添加淡出动画效果
              exportModal.style.transition = 'opacity 0.5s ease-out';
              exportModal.style.opacity = '0';
              // 等待动画结束后完全隐藏模态框
              setTimeout(function () {
                exportModal.classList.remove('show');
                exportModal.style.opacity = '1'; // 重置透明度
                exportModal.style.transition = ''; // 重置过渡效果
                alert('温馨提示：数据已导出，请前往C:\Users\<USER>\Downloads查看');
              }, 500);
            }
          }, 1000);

          // 取消按钮点击事件
          exportCancelBtn.onclick = function () {
            clearInterval(timer);
            exportModal.classList.remove('show');
          };

          // 关闭按钮点击事件
          exportModal.querySelector('.modal-close').onclick = function () {
            clearInterval(timer);
            exportModal.classList.remove('show');
          };
        });
      });
    </script>
    <script>
      // 标签页切换
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', () => {
          // 移除所有标签页的active类
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
          document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

          // 给当前点击的标签页添加active类
          tab.classList.add('active');

          // 显示对应的内容
          const target = tab.getAttribute('data-tab-target');
          document.getElementById(target).classList.add('active');
        });
      });

      // 新增权限表单提交
      document.getElementById('addPermissionForm').addEventListener('submit', function (e) {
        e.preventDefault();
        if (validateForm('addPermissionForm')) {
          // 模拟提交成功
          alert('视图权限创建成功！');
          document.getElementById('addPermissionModal').classList.remove('show');
          // 重置表单
          this.reset();
        }
      });

      // 打开编辑角色模态框
      document.querySelectorAll('[data-modal-target="editPermissionModal"]').forEach(button => {
        button.addEventListener('click', () => {
          // 获取当前行的数据（实际应用中可能来自API或数据源）
          const row = button.closest('tr');
          const roleName = row.cells[0].textContent;
          const roleDesc = row.cells[1].textContent;
          const roleStatus = row.cells[2].textContent === '有效';
          // 示例数据，实际应用中可能来自API
          const relatedViews = ['dynamic_national', 'value_provincial'];

          // 填充表单数据
          document.getElementById('roleName').value = roleName;
          document.getElementById('roleDesc').value = roleDesc;

          // 设置角色状态单选按钮
          if (roleStatus) {
            document.querySelector('input[name="roleStatus"][value="active"]').checked = true;
          } else {
            document.querySelector('input[name="roleStatus"][value="inactive"]').checked = true;
          }

          // 选中关联视图
          document.querySelectorAll('input[name="relatedViews"]').forEach(checkbox => {
            checkbox.checked = relatedViews.includes(checkbox.value);
          });

          // 添加表格行状态判断，隐藏启用状态的删除按钮
          document.querySelectorAll('table tbody tr').forEach(row => {
            const statusCell = row.cells[5];
            const deleteButton = row.querySelector('button .fa-trash').parentElement;
            if (statusCell.textContent.trim() === '启用') {
              deleteButton.style.display = 'none';
            } else {
              deleteButton.style.display = 'inline-block';
            }
          });

          // 显示模态框
          document.getElementById('editPermissionModal').classList.add('show');
        });
      });

      // 编辑角色表单提交
      document.getElementById('editRoleForm').addEventListener('submit', function (e) {
        e.preventDefault();
        if (validateForm('editRoleForm')) {
          // 模拟提交成功
          alert('角色编辑成功！');
          document.getElementById('editPermissionModal').classList.remove('show');
        }
      });

      // 关闭模态框
      document.querySelectorAll('.modal-close').forEach(button => {
        button.addEventListener('click', () => {
          button.closest('.modal').classList.remove('show');
        });
      });

      // 点击模态框外部关闭
      window.addEventListener('click', e => {
        if (e.target.classList.contains('modal')) {
          e.target.classList.remove('show');
        }
      });

      // 表单验证函数
      function validateForm(formId) {
        const form = document.getElementById(formId);
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
          if (!input.value.trim()) {
            alert(`${input.previousElementSibling.textContent}不能为空`);
            input.focus();
            isValid = false;
            return;
          }
        });

        return isValid;
      }
    </script>
    <script>
      function callApi(url) {
        console.log('调用API:', url);
        // 这里是API调用的实现
        fetch(url)
          .then(response => {
            if (!response.ok) {
              throw new Error('网络响应错误');
            }
            return response.json();
          })
          .then(data => {
            console.log('API响应:', data);
            // 处理响应数据
          })
          .catch(error => {
            console.error('API错误:', error);
            // 处理错误
          });
      }
    </script>
  </body>
</html>
