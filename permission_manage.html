<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 统一运营门户</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>
  <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child active" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

       <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-globe-asia page-title-icon"></i>
     视图权限管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb" style="font-size: 12px; border-bottom: 1px solid #e0e0e0; padding-bottom: 8px; margin-bottom: 16px;">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">统一运营门户</a></div>
      <div class="breadcrumb-item active" style="font-weight: bold;">视图权限管理</div>
    </div>

    <!-- 标签页已移除 -->

    <!-- 视图权限管理 -->
    <div class="tab-content active" id="viewPermission">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; margin-bottom: 20px; gap: 12px;">
        <div style="display: flex; flex-wrap: wrap; gap: 12px;">
          <div style="display: flex; flex-direction: column;">
            <label style="margin-bottom: 4px; font-size: 14px; font-weight: bold;">角色名称</label>
            <div class="search-box" style="width: 200px; margin-bottom: 0;">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="请输入角色名称">
            </div>
          </div>
          <div style="display: flex; flex-direction: column;">
            <label style="margin-bottom: 4px; font-size: 14px; font-weight: bold;">角色状态</label>
            <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 200px;">
              <option value="all">全部状态</option>
              <option value="active">启用</option>
              <option value="inactive">禁用</option>
            </select>
          </div>
          <div style="display: flex; flex-direction: column;">
            <label style="margin-bottom: 4px; font-size: 14px; font-weight: bold;">生效时间</label>
            <div style="display: flex; gap: 8px; align-items: center;">
              <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 140px;">
              <span style="color: var(--text-tertiary);">至</span>
              <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 140px;">
            </div>
          </div>
          <button id="searchBtn" class="btn btn-primary" style="align-self: center; margin-left: 12px; padding: 6px 12px; font-size: 12px; font-weight: bold; white-space: nowrap;    margin-top: 17px;
    height: 32px;" onclick="callApi('http://120.48.171.191:5000/test/searchBtn')"><i class="fas fa-search"></i> 搜索</button>
        </div>
        <div style="display: flex;">
          <button id="addRoleBtn" class="btn btn-primary" data-modal-target="addRoleModal" onclick="callApi('http://120.48.171.191:5000/test/addRoleBtn')"><i class="fas fa-plus"></i> 新增角色</button>
        </div>
      </div>

      <!-- 权限列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead style="font-size: 14px; font-weight: bold;">
              <tr>
                <th>角色名称</th>
                <th>角色描述</th>
                <th>关联视图</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody style="font-size: 14px; font-weight: normal;">
              <tr data-role-name="全国运营大屏查看权限" data-role-desc="允许查看全国运营监控大屏" data-role-status="active" data-related-views="dynamic_national">
                <td>全国运营大屏查看权限</td>
                <td>允许查看全国运营监控大屏</td>
                <td>全国运营监控大屏</td>
                <td>管理员</td>
                  <td>2023-06-01 14:30</td>
                  <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button id="editBtn_1" class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal" onclick="callApi('http://120.48.171.191:5000/test/editBtn_1')"><i class="fas fa-edit"></i></button>
                  <button id="linkEmpBtn_1" class="btn" style="color: var(--primary-color);" data-modal-target="linkEmployeeModal" onclick="callApi('http://120.48.171.191:5000/test/linkEmpBtn_1')"><i class="fas fa-link"></i> 关联工号</button>
                </td>
              </tr>
              <tr data-role-name="省分运营大屏查看权限" data-role-desc="允许查看省分大屏" data-role-status="active" data-related-views="dynamic_provincial">
                <td>省分运营大屏查看权限</td>
                <td>允许查看省分大屏</td>
                <td>省分运营大屏</td>
                <td>管理员</td>
                <td>2023-06-10 10:15</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button id="editBtn_2" class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal" onclick="callApi('http://120.48.171.191:5000/test/editBtn_2')"><i class="fas fa-edit"></i></button>
                  <button id="linkEmpBtn_2" class="btn" style="color: var(--primary-color);" data-modal-target="linkEmployeeModal" onclick="callApi('http://120.48.171.191:5000/test/linkEmpBtn_2')"><i class="fas fa-link"></i> 关联工号</button>
                </td>
              </tr>
              <tr data-role-name="地市运营大屏查看权限" data-role-desc="允许查看地市大屏" data-role-status="inactive" data-related-views="dynamic_city">
                <td>地市运营大屏查看权限</td>
                <td>允许查看地市大屏</td>
                <td>地市运营大屏</td>
                <td>管理员</td>
                  <td>2023-06-15 09:45</td>
                  <td><span class="tag tag-danger">禁用</span></td>
                <td>
                  <button id="editBtn_3" class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal" onclick="callApi('http://120.48.171.191:5000/test/editBtn_3')"><i class="fas fa-edit"></i></button>
                  <button id="linkEmpBtn_3" class="btn" style="color: var(--primary-color);" data-modal-target="linkEmployeeModal" onclick="callApi('http://120.48.171.191:5000/test/linkEmpBtn_3')"><i class="fas fa-link"></i> 关联工号</button>
                  <button id="deleteBtn_3" class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfirmModal" onclick="callApi('http://120.48.171.191:5000/test/deleteBtn_3')"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item" style="color: #999;"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item" style="color: #999;"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 权限日志管理 -->
    <div class="tab-content" id="permissionLog">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="display: flex; width: 70%;">
          <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" placeholder="搜索日志...">
          </div>
          <div style="margin-right: 12px;">
            <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="all">所有操作类型</option>
              <option value="create">创建</option>
              <option value="edit">编辑</option>
              <option value="delete">删除</option>
              <option value="grant">授权</option>
              <option value="revoke">撤销</option>
            </select>
          </div>
          <div style="display: flex; gap: 8px;">
            <input type="date" placeholder="开始日期">
            <input type="date" placeholder="结束日期">
          </div>
        </div>
        <div style="display: flex;">
          <button class="btn" style="background-color: var(--primary-color); color: white; font-size: 12px; font-weight: bold; white-space: nowrap; margin-right: 12px;"><i class="fas fa-download"></i> 导出</button>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>操作人</th>
                <th>操作时间</th>
                <th>操作类型</th>
                <th>操作描述</th>
                <th>IP地址</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>管理员</td>
                <td>2023-07-16 14:30</td>
                <td><span class="tag tag-primary">创建</span></td>
                <td>创建了新的视图权限: 省级运营大屏查看权限</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
              <tr>
                <td>张三</td>
                <td>2023-07-16 13:15</td>
                <td><span class="tag tag-warning">授权</span></td>
                <td>为用户李四授予数据分析报告查看权限</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
              <tr>
                <td>李四</td>
                <td>2023-07-16 10:45</td>
                <td><span class="tag tag-danger">删除</span></td>
                <td>尝试删除数据编辑权限</td>
                <td>192.168.1.102</td>
                <td><span class="tag tag-danger">失败</span></td>
              </tr>
              <tr>
                <td>管理员</td>
                <td>2023-07-15 16:20</td>
                <td><span class="tag tag-info">编辑</span></td>
                <td>修改了全国运营大屏查看权限的描述信息</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item" style="color: #999;"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item" style="color: #999;"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增角色模态框 -->
  <div class="modal" id="addRoleModal">
    <div class="modal-content" style="width: 800px; max-width: 90%; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);">
      <div class="modal-header" style="background-color: #007bff; color: white; padding: 16px 24px; display: flex; justify-content: space-between; align-items: center;">
        <div class="modal-title" style="font-size: 18px; font-weight: 600;"><i class="fas fa-plus-circle"></i> 新增角色</div>
        <button class="modal-close" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; transition: transform 0.2s;">&times;</button>
      </div>
      <div class="modal-body" style="padding: 24px;">
        <form id="addRoleForm">
          <div class="form-group" style="margin-bottom: 24px;">
            <label for="roleName" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色名称 <span style="color: #ff4d4f;">*</span></label>
            <input type="text" id="roleName" name="roleName" required placeholder="请输入角色名称" style="width: 100%; padding: 10px 16px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; transition: border-color 0.3s, box-shadow 0.3s;">
          </div>
          <div class="form-group" style="margin-bottom: 24px;">
            <label for="roleDesc" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色描述 <span style="color: #ff4d4f;">*</span></label>
            <textarea id="roleDesc" name="roleDesc" rows="3" required placeholder="请输入角色描述，150字以内~" style="width: 100%; padding: 10px 16px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; resize: vertical; transition: border-color 0.3s, box-shadow 0.3s;"></textarea>
            <p id="charCount" style="margin-top: 8px; font-size: 12px; color: #999; text-align: right;">0/150</p>
          </div>
          <div class="form-group" style="margin-bottom: 24px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色状态</label>
            <div style="display: flex; gap: 24px; margin-top: 8px;">
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="permissionStatus" value="active" checked style="width: 16px; height: 16px; margin-right: 8px;">
                <span style="color: #333;">有效</span>
              </label>
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="permissionStatus" value="inactive" style="width: 16px; height: 16px; margin-right: 8px;">
                <span style="color: #333;">禁用</span>
              </label>
            </div>
          </div>
          <div class="form-group">
            <label style="display: block; margin-bottom: 12px; font-weight: 500; color: #333;">关联视图</label>
            <div style="margin-top: 8px; display: flex; flex-wrap: wrap; gap: 16px;">
              <!-- 分类动态监控大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">分类动态监控大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="dynamic" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_national" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_provincial" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_city" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_district" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 价值分析大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">价值分析大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="value" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_national" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_provincial" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_city" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_district" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 工单调度大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">工单调度大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="ticket" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_national" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_provincial" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_city" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_district" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 资源调度大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">资源调度大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="resource" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_national" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_provincial" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_city" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_district" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer" style="background-color: #f5f5f5; padding: 16px 24px; display: flex; justify-content: flex-end; gap: 12px;">
        <button class="btn" style="border: 1px solid #d9d9d9; background-color: white; color: #333; padding: 8px 20px; border-radius: 6px; cursor: pointer; transition: all 0.3s;" onclick="document.getElementById('addRoleModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" style="background-color: #007bff; color: white; border: none; padding: 8px 24px; border-radius: 6px; cursor: pointer; transition: all 0.3s;" onclick="document.getElementById('addRoleForm').submit()">保存</button>
      </div>
    </div>
  </div>

  <!-- 编辑角色模态框 -->
  <!-- 注意：模态框ID保持不变，因为按钮的data-modal-target属性使用的是editPermissionModal -->
  <div class="modal" id="editPermissionModal">
    <div class="modal-content" style="width: 800px; max-width: 90%; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);">
      <div class="modal-header" style="background-color: #007bff; color: white; padding: 16px 24px; display: flex; justify-content: space-between; align-items: center;">
        <div class="modal-title" style="font-size: 18px; font-weight: 600;"><i class="fas fa-edit"></i> 编辑角色</div>
        <button class="modal-close" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; transition: transform 0.2s;">&times;</button>
      </div>
      <div class="modal-body" style="padding: 24px;">
        <form id="editRoleForm">
          <div class="form-group" style="margin-bottom: 24px;">
            <label for="roleName" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色名称 <span style="color: #ff4d4f;">*</span></label>
            <input type="text" id="roleName" name="roleName" required placeholder="全国运营大屏查看权限" style="width: 100%; padding: 10px 16px; border: 1px solid #333; border-radius: 6px; font-size: 14px; transition: border-color 0.3s, box-shadow 0.3s; color: #333;">
          </div>
          <div class="form-group" style="margin-bottom: 24px;">
            <label for="roleDesc" style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色描述 <span style="color: #ff4d4f;">*</span></label>
            <textarea id="roleDesc" name="roleDesc" rows="3" required placeholder="允许查看全国运营监控大屏" style="width: 100%; padding: 10px 16px; border: 1px solid #333; border-radius: 6px; font-size: 14px; resize: vertical; transition: border-color 0.3s, box-shadow 0.3s; color: #333;"></textarea>
            <p id="editCharCount" style="margin-top: 8px; font-size: 12px; color: #333; text-align: right;">0/150</p>
          </div>
          <div class="form-group" style="margin-bottom: 24px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">角色状态</label>
            <div style="display: flex; gap: 24px; margin-top: 8px;">
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="roleStatus" value="active" checked style="width: 16px; height: 16px; margin-right: 8px;">
                <span style="color: #333;">有效</span>
              </label>
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="roleStatus" value="inactive" style="width: 16px; height: 16px; margin-right: 8px;">
                <span style="color: #333;">禁用</span>
              </label>
            </div>
          </div>
          <div class="form-group">
            <label style="display: block; margin-bottom: 12px; font-weight: 500; color: #333;">关联视图</label>
            <div style="margin-top: 8px; display: flex; flex-wrap: wrap; gap: 16px;">
              <!-- 分类动态监控大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">分类动态监控大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="dynamic" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_national" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_provincial" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_city" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="dynamic_district" class="dynamic-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 价值分析大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">价值分析大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="value" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_national" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_provincial" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_city" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="value_district" class="value-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 工单调度大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">工单调度大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="ticket" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_national" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_provincial" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_city" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="ticket_district" class="ticket-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
              <!-- 资源调度大屏 -->
              <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; width: calc(50% - 8px); min-width: 300px; transition: all 0.3s; background-color: white;" class="card-hover">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                  <div style="font-weight: 500; color: #333;">资源调度大屏</div>
                  <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" class="select-all" data-target="resource" style="width: 16px; height: 16px; margin-right: 6px;">
                    <span style="font-size: 14px; color: #666;">全选</span>
                  </label>
                </div>
                <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px 0;">
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_national" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">全国</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_provincial" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">省分</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_city" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">地市</span>
                  </label>
                  <label style="display: flex; align-items: center; cursor: pointer; width: calc(50% - 8px);">
                    <input type="checkbox" name="relatedViews" value="resource_district" class="resource-checkbox" style="width: 16px; height: 16px; margin-right: 8px;">
                    <span style="color: #333;">区县</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer" style="background-color: #f5f5f5; padding: 16px 24px; display: flex; justify-content: flex-end; gap: 12px;">
        <button class="btn" style="border: 1px solid #d9d9d9; background-color: white; color: #333; padding: 8px 20px; border-radius: 6px; cursor: pointer; transition: all 0.3s;" onclick="document.getElementById('editPermissionModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" style="background-color: #007bff; color: white; border: none; padding: 8px 24px; border-radius: 6px; cursor: pointer; transition: all 0.3s;" onclick="document.getElementById('editRoleForm').submit()">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 确保新增角色模态框为空
    document.querySelectorAll('[data-modal-target="addRoleModal"]').forEach(button => {
      button.addEventListener('click', () => {
        // 重置表单
        document.getElementById('addRoleForm').reset();
        // 重置关联视图选择
        document.querySelectorAll('input[name="relatedViews"]').forEach(checkbox => {
          checkbox.checked = false;
        });
        // 显示模态框
        document.getElementById('addRoleModal').classList.add('show');
      });
    });
  </script>
  <style>
    /* 卡片悬停效果 */
    .card-hover:hover {
      border-color: #007bff;
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
      transform: translateY(-2px);
    }
  </style>
  <script>
    /* 标签页切换代码已被注释，不再执行 */
  </script>
  <script>
    // 关联视图全选功能
    document.querySelectorAll('.select-all').forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const targetClass = this.getAttribute('data-target') + '-checkbox';
        document.querySelectorAll('.' + targetClass).forEach(cb => {
          cb.checked = this.checked;
        });
      });
    });

    // 当子复选框改变时，更新全选框状态
    document.querySelectorAll('[name="relatedViews"]').forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const targetClass = this.className.split('-')[0] + '-checkbox';
        const allCheckboxes = document.querySelectorAll('.' + targetClass);
        const checkedCheckboxes = document.querySelectorAll('.' + targetClass + ':checked');
        const selectAllCheckbox = document.querySelector('.select-all[data-target="' + this.className.split('-')[0] + '"]');
        selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
      });
    });
    // 打开新增角色模态框
    document.querySelectorAll('[data-modal-target="addRoleModal"]').forEach(button => {
      button.addEventListener('click', () => {
        // 重置表单
        document.getElementById('addRoleForm').reset();
        // 重置角色描述字数统计
        document.getElementById('charCount').textContent = '0/150';
        document.getElementById('charCount').style.color = '#999';
        // 显示模态框
        document.getElementById('addRoleModal').classList.add('show');
      });
    });

    // 新增角色表单提交
    document.getElementById('addRoleForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const descInput = document.getElementById('roleDesc');
      if (descInput.value.length > 150) {
        alert('角色描述不能超过150字');
        descInput.focus();
        return;
      }
      if (validateForm('addRoleForm')) {
        // 模拟提交成功
        alert('角色创建成功！');
        document.getElementById('addRoleModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });

    // 打开编辑角色模态框
    // 先移除旧的事件监听器，避免重复绑定
    document.querySelectorAll('[data-modal-target="editPermissionModal"]').forEach(button => {
      // 移除所有现有的点击事件监听器
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);
      // 绑定新的点击事件监听器
      newButton.addEventListener('click', () => {
        // 获取当前行的数据
        const row = newButton.closest('tr');
        const roleName = row.getAttribute('data-role-name');
        const roleDesc = row.getAttribute('data-role-desc');
        const roleStatus = row.getAttribute('data-role-status') === 'active';
        // 获取当前行的关联视图数据
        const relatedViews = row.getAttribute('data-related-views') ? row.getAttribute('data-related-views').split(',') : [];

        console.log('角色名称:', roleName);
        console.log('角色描述:', roleDesc);
        console.log('角色状态:', roleStatus);
        console.log('关联视图:', relatedViews);

        // 填充表单数据
        const roleNameInput = document.getElementById('roleName');
        const roleDescInput = document.getElementById('roleDesc');
        if (roleNameInput && roleDescInput) {
          // 直接设置固定值
          console.log('设置固定角色名称: 全国运营大屏查看权限');
          console.log('设置固定角色描述: 允许查看全国运营监控大屏');
          roleNameInput.value = '全国运营大屏查看权限';
          roleDescInput.value = '允许查看全国运营监控大屏';
          // 触发输入事件，确保表单验证和字数统计能正常工作
          roleNameInput.dispatchEvent(new Event('input'));
          roleDescInput.dispatchEvent(new Event('input'));
        
       
        }
        
        // 设置角色状态单选按钮
        const activeRadio = document.querySelector('input[name="roleStatus"][value="active"]');
        const inactiveRadio = document.querySelector('input[name="roleStatus"][value="inactive"]');
        if (activeRadio && inactiveRadio) {
          if (roleStatus) {
            activeRadio.checked = true;
            inactiveRadio.checked = false;
          } else {
            activeRadio.checked = false;
            inactiveRadio.checked = true;
          }
        }

        // 选中关联视图
        document.querySelectorAll('input[name="relatedViews"]').forEach(checkbox => {
          // 先取消所有选中状态
          checkbox.checked = false;
          // 再根据关联视图数据选中对应的复选框
          if (relatedViews.includes(checkbox.value)) {
            checkbox.checked = true;
            console.log('选中复选框:', checkbox.value);
          }
        });

        // 添加表格行状态判断，隐藏启用状态的删除按钮
        document.querySelectorAll('table tbody tr').forEach(row => {
          const statusCell = row.cells[5];
          const deleteButton = row.querySelector('button .fa-trash').parentElement;
          if (statusCell.textContent.trim() === '启用') {
            deleteButton.style.display = 'none';
          } else {
            deleteButton.style.display = 'inline-block';
          }
        });

        // 强制更新UI，确保表单数据正确显示
        setTimeout(() => {
          document.getElementById('editPermissionModal').classList.add('show');
        }, 10);
      });
    });

    // 编辑角色表单提交
    document.getElementById('editRoleForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const descInput = document.getElementById('roleDesc');
      if (descInput.value.length > 150) {
        alert('角色描述不能超过150字');
        descInput.focus();
        return;
      }
      if (validateForm('editRoleForm')) {
        // 模拟提交成功
        alert('角色编辑成功！');
        document.getElementById('editPermissionModal').classList.remove('show');
      }
    });

    // 编辑角色描述字数统计
    document.getElementById('roleDesc').addEventListener('input', function() {
      const descInput = this;
      const charCount = document.getElementById('editCharCount');
      charCount.textContent = `${descInput.value.length}/150`;
      if (descInput.value.length > 150) {
        charCount.style.color = '#ff4d4f';
      } else {
        charCount.style.color = '#999';
      }
    });

    // 关闭模态框
    document.querySelectorAll('.modal-close').forEach(button => {
      button.addEventListener('click', () => {
        button.closest('.modal').classList.remove('show');
      });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal')) {
        e.target.classList.remove('show');
      }
    });

    // 表单验证函数
    function validateForm(formId) {
      const form = document.getElementById(formId);
      const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
      let isValid = true;

      inputs.forEach(input => {
        if (!input.value.trim()) {
          alert(`${input.previousElementSibling.textContent}不能为空`);
          input.focus();
          isValid = false;
          return;
        }
      });

      return isValid;
    }
  </script>
<!-- 关联工号弹出框 -->
<div class="modal" id="linkEmployeeModal">
  <div class="modal-overlay"></div>
  <div class="modal-container" style="width: 900px; max-width: 90%;">
    <div class="modal-header" style="position: relative;">
      <div class="modal-title">关联工号</div>
      <button class="modal-close" style="position: absolute; right: 16px; top: 16px;"><i class="fas fa-times"></i></button>
    </div>
    <div class="modal-body">
      <!-- 查询区 -->
      <div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
        <div style="display: flex; flex-direction: column; width: 220px;">
          <label style="margin-bottom: 4px; font-size: 14px; font-weight: bold;">系统名称</label>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); width: 100%;">
            <option value="all">全部</option>
            <option value="central">集中系统</option>
            <option value="professional">专业系统</option>
            <option value="provincial">省分系统</option>
          </select>
        </div>
        <div style="display: flex; flex-direction: column; width: 220px;">
          <label style="margin-bottom: 4px; font-size: 14px; font-weight: bold;">工号</label>
          <div class="search-box" style="width: 100%; margin-bottom: 0;">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" placeholder="请输入工号" style="color: var(--text-primary);">
            <style>
              ::placeholder {
                color: var(--text-tertiary);
              }
            </style>
          </div>
        </div>
        <button class="btn btn-primary" style="align-self: flex-end; padding: 6px 12px; font-size: 12px; font-weight: bold; white-space: nowrap;"><i class="fas fa-search"></i> 搜索</button>
      </div>

      <!-- 工号卡片区域 -->
      <div style="display: flex; flex-wrap: wrap; gap: 16px;">
        <!-- 集中系统工号卡片 -->
        <div class="card" style="flex: 1; min-width: 250px;">
          <div class="card-header" style="display: flex; align-items: center; cursor: pointer;">
            <i class="fas fa-chevron-down" style="margin-right: 8px;"></i>
            <span style="font-weight: bold;">集中系统工号</span>
          </div>
          <div class="card-body">
            <ul class="tree-list">
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>全国工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QG-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QG-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QG-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QG-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QG-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>省级工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-SJ-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-SJ-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-SJ-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-SJ-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-SJ-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>地市工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-DS-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-DS-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-DS-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-DS-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-DS-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>区县工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QX-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QX-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QX-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QX-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> JT-QX-SQ</li>
                </ul>
              </li>
            </ul>
          </div>
        </div>

        <!-- 专业系统工号卡片 -->
        <div class="card" style="flex: 1; min-width: 250px;">
          <div class="card-header" style="display: flex; align-items: center; cursor: pointer;">
            <i class="fas fa-chevron-down" style="margin-right: 8px;"></i>
            <span style="font-weight: bold;">专业系统工号</span>
          </div>
          <div class="card-body">
            <ul class="tree-list">
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>全国工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QG-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QG-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QG-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QG-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QG-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>省级工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-SJ-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-SJ-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-SJ-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-SJ-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-SJ-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>地市工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-DS-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-DS-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-DS-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-DS-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-DS-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>区县工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QX-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QX-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QX-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QX-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> ZY-QX-SQ</li>
                </ul>
              </li>
            </ul>
          </div>
        </div>

        <!-- 省分系统工号卡片 -->
        <div class="card" style="flex: 1; min-width: 250px;">
          <div class="card-header" style="display: flex; align-items: center; cursor: pointer;">
            <i class="fas fa-chevron-down" style="margin-right: 8px;"></i>
            <span style="font-weight: bold;">省分系统工号</span>
          </div>
          <div class="card-body">
            <ul class="tree-list">
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>全国工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QG-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QG-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QG-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QG-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QG-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>省级工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-SJ-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-SJ-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-SJ-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-SJ-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-SJ-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>地市工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-DS-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-DS-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-DS-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-DS-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-DS-SQ</li>
                </ul>
              </li>
              <li>
                <div style="display: flex; align-items: center; cursor: pointer; padding: 4px 0;">
                    <i class="fas fa-chevron-right" style="margin-right: 8px; font-size: 10px;"></i>
                    <span>区县工号</span>
                  </div>
                  <ul style="padding-left: 24px; display: none;">
                  <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QX-ZS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QX-LS</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QX-WW</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QX-ZL</li>
              <li style="padding: 2px 0; display: flex; align-items: center;"><input type="checkbox" style="margin-right: 8px; vertical-align: middle; opacity: 0.5;" class="employee-checkbox"> SF-QX-SQ</li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" id="confirmLinkBtn">确认关联</button>
      <button class="btn modal-close">取消</button>
    </div>
  </div>
</div>

<!-- 操作成功提示 -->
<div id="successToast" class="toast" style="position: fixed; top: 20px; right: 20px; background-color: #4CAF50; color: white; padding: 12px 24px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); opacity: 0; transition: opacity 0.3s; z-index: 1001;">
  操作成功
</div>

<!-- 删除确认模态框 -->
<div class="modal" id="deleteConfirmModal">
  <div class="modal-overlay"></div>
  <div class="modal-container" style="width: 400px; max-width: 90%;">
    <div class="modal-header" style="position: relative;">
      <div class="modal-title"><i class="fas fa-exclamation-triangle"></i> 确认删除</div>
      <button class="modal-close" style="position: absolute; right: 16px; top: 16px;"><i class="fas fa-times"></i></button>
    </div>
    <div class="modal-body" style="text-align: center; padding: 30px 16px;">
      <p style="margin-bottom: 20px; font-size: 16px;">删除后将无法恢复，确认删除？</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" id="confirmDeleteBtn">确认删除</button>
      <button class="btn modal-close">取消</button>
    </div>
  </div>
</div>

<!-- 添加弹出框样式 -->
<style>
  .modal {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: none; align-items: center; justify-content: center; z-index: 1000;
  }
  .modal-overlay {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5);
  }
  .modal-container {
    position: relative; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  .modal-header {
    display: flex; justify-content: space-between; align-items: center; padding: 16px; border-bottom: 1px solid var(--border-color);
  }
  .modal-title {
    font-size: 16px; font-weight: bold;
  }
  .modal-close {
    background: none; border: none; cursor: pointer; font-size: 16px; color: var(--text-secondary);
  }
  .modal-body {
    padding: 16px;
  }
  .modal-footer {
    display: flex; justify-content: flex-end; gap: 8px; padding: 16px; border-top: 1px solid var(--border-color);
  }
  .tree-list {
    list-style: none; padding: 0;
  }
  .tree-list li {
    position: relative;
    padding-left: 5px;
  }
  .tree-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 5px;
    height: 1px;
    background-color: #ccc;
  }
  .employee-checkbox {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border: 1px solid #ccc;
    border-radius: 2px;
    outline: none;
    transition: all 0.2s;
  }
  .employee-checkbox:checked {
    background-color: #f0f0f0;
  }
  .employee-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    font-size: 12px;
    color: #666;
  }
  .card {
    border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden;
  }
  .card-header {
    padding: 12px 16px; background-color: var(--bg-secondary); border-bottom: 1px solid var(--border-color);
  }
  .card-body {
    padding: 16px;
  }
  
  /* 提示框样式 */
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1001;
  }
  .toast.show {
    opacity: 1;
  }
</style>

<!-- 确认删除按钮事件处理 -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 为确认删除按钮添加点击事件
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
      // 这里添加删除逻辑
      alert('删除成功！');
      // 关闭模态框
      document.getElementById('deleteConfirmModal').style.display = 'none';
      // 实际应用中，这里应该有AJAX请求或其他删除操作
    });
  });
</script>

<!-- 添加弹出框交互脚本 -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 获取所有关联工号按钮
    const linkButtons = document.querySelectorAll('[data-modal-target="linkEmployeeModal"]');
    // 获取弹出框
    const modal = document.getElementById('linkEmployeeModal');
    // 获取关闭按钮
    const closeButtons = modal.querySelectorAll('.modal-close, .modal-overlay');

    // 打开弹出框
    linkButtons.forEach(button => {
      button.addEventListener('click', function() {
        modal.style.display = 'flex';
      });
    });

    // 关闭弹出框
    closeButtons.forEach(button => {
      button.addEventListener('click', function() {
        modal.style.display = 'none';
      });
    });

    // 点击树状结构展开/折叠
    const treeNodes = modal.querySelectorAll('.tree-list li > div');
    treeNodes.forEach(node => {
      node.addEventListener('click', function() {
        const icon = this.querySelector('i');
        const subList = this.nextElementSibling;

        if (icon.classList.contains('fa-chevron-down')) {
          icon.classList.replace('fa-chevron-down', 'fa-chevron-right');
          subList.style.display = 'none';
        } else {
          icon.classList.replace('fa-chevron-right', 'fa-chevron-down');
          subList.style.display = 'block';
        }
      });
    });

    // 确认关联按钮点击事件
    document.getElementById('confirmLinkBtn').addEventListener('click', function() {
      // 显示成功提示
      const toast = document.getElementById('successToast');
      toast.classList.add('show');
      
      // 3秒后隐藏提示
      setTimeout(function() {
        toast.classList.remove('show');
      }, 3000);
      
      // 关闭模态框
      modal.style.display = 'none';
    });
  });
</script>

<style>
  /* 自定义样式增强 */
  .modal-content:hover .modal-close {
    transform: scale(1.1);
  }
  .form-group input:focus,
  .form-group textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
    outline: none;
  }
  .form-group input::placeholder,
  .form-group textarea::placeholder {
    color: #999;
  }
  /* 修复模态框滚动问题 */
  .modal-body {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 24px;
  }
  .modal-footer .btn {
    background-color: #f0f0f0;
  }
  .modal-footer .btn:hover {
    background-color: #e0e0e0;
  }
  .modal-footer .btn.btn-primary {
    background-color: #0069d9;
  }
  .modal-footer .btn.btn-primary:hover {
    background-color: #0056b3;
  }
  .card-hover {
    transition: all 0.3s ease;
  }
  .card-hover:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
  }
</style>

<script>
  // 角色管理相关功能增强
  document.addEventListener('DOMContentLoaded', function() {
    // 添加角色描述字数统计
    const roleDesc = document.getElementById('roleDesc');
    const charCount = document.getElementById('charCount');
    if (roleDesc && charCount) {
      roleDesc.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = `${count}/150`;
        if (count > 150) {
          charCount.style.color = '#ff4d4f';
        } else {
          charCount.style.color = '#999';
        }
      });
    }

    // 实现全选/反选功能
    const selectAllCheckboxes = document.querySelectorAll('.select-all');
    selectAllCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const targetClass = this.getAttribute('data-target');
        const targetCheckboxes = document.querySelectorAll(`.${targetClass}-checkbox`);
        targetCheckboxes.forEach(cb => {
          cb.checked = this.checked;
        });
      });
    });

    // 监听子复选框变化，更新全选状态
    const allChildCheckboxes = document.querySelectorAll('[name="relatedViews"]');
    allChildCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const targetClass = this.className.split('-')[0];
        const targetCheckboxes = document.querySelectorAll(`.${targetClass}-checkbox`);
        const selectAllCheckbox = document.querySelector(`.select-all[data-target="${targetClass}"]`);
        let allChecked = true;
        let allUnchecked = true;

        targetCheckboxes.forEach(cb => {
          if (!cb.checked) allChecked = false;
          if (cb.checked) allUnchecked = false;
        });

        if (allChecked) {
          selectAllCheckbox.checked = true;
          selectAllCheckbox.indeterminate = false;
        } else if (allUnchecked) {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
        } else {
          selectAllCheckbox.indeterminate = true;
        }
      });
    });

    // 添加表单提交处理
    const addRoleForm = document.getElementById('addRoleForm');
    if (addRoleForm) {
      addRoleForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // 这里可以添加表单验证逻辑
        // 提交成功后关闭模态框
        document.getElementById('addRoleModal').classList.remove('show');
        // 可以添加成功提示
        alert('角色创建成功！');
        // 重置表单
        this.reset();
        // 重置字数统计
        if (charCount) charCount.textContent = '0/150';
      });
    }

    // 修复模态框关闭按钮事件
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    modalCloseButtons.forEach(button => {
      button.addEventListener('click', function() {
        const modal = this.closest('.modal');
        if (modal) {
          modal.classList.remove('show');
        }
      });
    });
  });
</script>
<script>
function callApi(url) {
  console.log('调用API:', url);
  // 这里是API调用的实现
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error('网络响应错误');
      }
      return response.json();
    })
    .then(data => {
      console.log('API响应:', data);
      // 处理响应数据
    })
    .catch(error => {
      console.error('API错误:', error);
      // 处理错误
    });
}
</script>
</body>
</html>