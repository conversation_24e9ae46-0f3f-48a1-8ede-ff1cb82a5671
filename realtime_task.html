<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 实时采集任务管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
 <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #165DFF;
            --primary-light: #E8F3FF;
            --border-color: #DCDFE6;
            --text-primary: #303133;
            --text-secondary: #606266;
            --bg-primary: #FFFFFF;
            --bg-secondary: #F5F7FA;
            --success-color: #52C41A;
            --danger-color: #FF4D4F;
            --warning-color: #FAAD14;
             --primary-color: #1890ff;
  --secondary-color: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #ff4d4f;
  --info-color: #1890ff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e8e8e8;
  --hover-color: #f0f0f0;
        }

        /* 操作列悬浮提示样式 */
        td button {
            position: relative;
            margin-right: 5px;
        }

        td button:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 3px 8px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 12px;
            border-radius: 4px;
            white-space: nowrap;
            margin-bottom: 5px;
            z-index: 100;
        }

        td button:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
            margin-bottom: 0;
            z-index: 101;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0E4CAD;
        }
        
        .search-box {
            position: relative;
            width: 300px;
            margin-bottom: 16px;
        }
        
        .search-box input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .search-box-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background-color: var(--bg-primary);
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-secondary);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        
        .card {
            background-color: var(--bg-primary);
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }
        
        .tab-btn.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .tag-success {
            background-color: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
        }
        
        .tag-danger {
            background-color: rgba(255, 77, 79, 0.1);
            color: var(--danger-color);
        }
        
        .tag-warning {
            background-color: rgba(250, 173, 20, 0.1);
            color: var(--warning-color);
        }
        .tag-online {
    background-color: rgba(22, 93, 255, 0.1);
    color: var(--primary-color);
}

.tag-offline {
    background-color: rgba(96, 98, 102, 0.1);
    color: var(--text-secondary);

        .progress-container {
            height: 6px;
            background-color: #E5E6EB;
            border-radius: 3px;
            margin: 8px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0;
        }
        
        .report-section {
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px dashed var(--border-color);
        }
        
        .report-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .report-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--primary-color);
        }
        
        .chart-placeholder {
            height: 200px;
            background-color: var(--bg-secondary);
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--text-secondary);
            margin: 12px 0;
        }
        
        .data-result-container {
            margin-top: 12px;
        }
        
        .result-stats {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px 4px 0 0;
            background: var(--bg-secondary);
            font-size: 14px;
        }
        
        .data-table-container {
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
            overflow: hidden;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">数据源管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">离线采集任务管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">实时采集任务管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child  active" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

     <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="data_permission_control.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-bolt page-title-icon"></i>
      实时采集任务管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">数据融通</a></div>
      <div class="breadcrumb-item active">实时采集任务管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: flex-start; align-items: center; margin-bottom: 20px;gap:10px">
         <div>
          <select style="height: 46px; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="stopped">已停止</option>
            <option value="error">异常</option>
          </select>
        </div>
      <div class="search-box" style="width: 300px; margin-bottom: 0;">
        <i class="fas fa-search search-box-icon"></i>
        <input type="text" placeholder="搜索任务..." onkeypress="handleSearchKeyPress(event)">
      </div>
      <button class="btn btn-primary" onclick="searchTasks()" style="margin-left: 8px;">
        <i class="fas fa-search"></i> 查询
      </button>
      <div style="display: flex;margin-left:auto;">
        <button class="btn btn-primary" data-modal-target="addRealtimeTaskModal"><i class="fas fa-plus"></i> 新增任务</button>
    <!-- 新增三大功能入口按钮 -->
    <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="dataFilterPreprocessModal"><i class="fas fa-filter"></i> 数据预处理</button>
    <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="dataSecurityModal"><i class="fas fa-shield-alt"></i> 安全管理</button>
    <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="logAuditModal"><i class="fas fa-file-lines"></i> 日志审计</button>
    

  </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>任务名称</th>
              <th>数据源</th>
              <th>目标表</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>启动时间</th>
              <th>处理速率</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tbody>
  <tr>
    <td>用户行为数据采集</td>
    <td>Kafka-实时数据流</td>
    <td>dw.user_behavior</td>
    <td><span class="tag tag-success">运行中</span></td>
    <td>2023-07-10 15:30</td>
    <td>2023-07-15 08:00</td>
    <td>1,200 条/秒</td>
    <td>
      <button class="btn edit-btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal" data-task-id="1" title="编辑任务"><i class="fas fa-edit"></i></button>
      <button class="btn status-btn" style="color: var(--warning-color);" data-task-id="1" title="停止任务"><i class="fas fa-stop"></i></button>
      <button class="btn delete-btn" style="color: var(--danger-color);" data-task-id="1" title="删除任务"><i class="fas fa-trash"></i></button>
      <button class="btn" style="color: var(--primary-color);" title="查看监控"><i class="fas fa-chart-line"></i></button>
    </td>
  </tr>
  <tr>
    <td>产品库存监控</td>
    <td>API-第三方API</td>
    <td>dw.product_inventory</td>
    <td><span class="tag tag-online">已上线</span></td>
    <td>2023-07-12 10:15</td>
    <td>2023-07-14 00:00</td>
    <td>300 条/秒</td>
    <td>
      <button class="btn edit-btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal" data-task-id="2" title="编辑任务"><i class="fas fa-edit"></i></button>
      <button class="btn status-btn" style="color: var(--success-color);" data-task-id="2" title="启动任务"><i class="fas fa-play"></i></button>
      <button class="btn" style="color: var(--text-secondary);" title="下线任务"><i class="fas fa-sign-out-alt"></i></button>
      <button class="btn delete-btn" style="color: var(--danger-color);" data-task-id="2" title="删除任务"><i class="fas fa-trash"></i></button>
      <button class="btn" style="color: var(--primary-color);" title="查看监控"><i class="fas fa-chart-line"></i></button>
    </td>
  </tr>
  <tr>
    <td>订单实时处理</td>
    <td>Kafka-实时数据流</td>
    <td>dw.order_process</td>
    <td><span class="tag tag-danger">异常</span></td>
    <td>2023-07-13 14:45</td>
    <td>2023-07-15 09:30</td>
    <td>0 条/秒</td>
    <td>
      <button class="btn edit-btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal" data-task-id="3" title="编辑任务"><i class="fas fa-edit"></i></button>
      <button class="btn status-btn" style="color: var(--primary-color);" data-task-id="3" title="重启任务"><i class="fas fa-redo"></i></button>
      <button class="btn" style="color: var(--text-secondary);" title="下线任务"><i class="fas fa-sign-out-alt"></i></button>
      <button class="btn delete-btn" style="color: var(--danger-color);" data-task-id="3" title="删除任务"><i class="fas fa-trash"></i></button>
      <button class="btn" style="color: var(--primary-color);" title="查看监控"><i class="fas fa-chart-line"></i></button>
    </td>
  </tr>
  <tr>
    <td>设备状态监控</td>
    <td>API-物联网平台</td>
    <td>dw.device_status</td>
    <td><span class="tag tag-warning">已停止</span></td>
    <td>2023-07-14 09:20</td>
    <td>-</td>
    <td>0 条/秒</td>
    <td>
      <button class="btn edit-btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal" data-task-id="4" title="编辑任务"><i class="fas fa-edit"></i></button>
      <button class="btn status-btn" style="color: var(--success-color);" data-task-id="4" title="启动任务"><i class="fas fa-play"></i></button>
      <button class="btn" style="color: var(--text-secondary);" title="下线任务"><i class="fas fa-sign-out-alt"></i></button>
      <button class="btn delete-btn" style="color: var(--danger-color);" data-task-id="4" title="删除任务"><i class="fas fa-trash"></i></button>
      <button class="btn" style="color: var(--primary-color);" title="查看监控"><i class="fas fa-chart-line"></i></button>
    </td>
  </tr>
  <tr>
    <td>系统日志采集</td>
    <td>File-服务器日志</td>
    <td>dw.system_logs</td>
    <td><span class="tag tag-offline">已下线</span></td>
    <td>2023-07-11 11:30</td>
    <td>-</td>
    <td>0 条/秒</td>
    <td>
      <button class="btn edit-btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal" data-task-id="5" title="编辑任务"><i class="fas fa-edit"></i></button>
      <button class="btn status-btn" style="color: var(--primary-color);" data-task-id="5" title="上线任务"><i class="fas fa-sign-in-alt"></i></button>
      <button class="btn delete-btn" style="color: var(--danger-color);" data-task-id="5" title="删除任务"><i class="fas fa-trash"></i></button>
      <button class="btn" style="color: var(--primary-color);" title="查看监控"><i class="fas fa-chart-line"></i></button>
    </td>
  </tr>
</tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增实时任务模态框 -->
  <div class="modal" id="addRealtimeTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增实时采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addRealtimeTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="dataSource">数据源</label>
            <select id="dataSource" name="dataSource" required>
              <option value="">请选择数据源</option>
              <option value="kafka_stream">Kafka-实时数据流</option>
              <option value="api_third">API-第三方API</option>
              <option value="api_iot">API-物联网平台</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetTable">目标表</label>
            <input type="text" id="targetTable" name="targetTable" required placeholder="请输入目标表名">
          </div>
          <div class="form-group">
            <label for="consumerGroup">消费者组</label>
            <input type="text" id="consumerGroup" name="consumerGroup" required placeholder="请输入消费者组名">
          </div>
          <div class="form-group">
            <label for="batchSize">批次大小</label>
            <input type="number" id="batchSize" name="batchSize" required placeholder="请输入批次大小" value="1000">
          </div>
          <div class="form-group">
            <label for="parallelism">并行度</label>
            <input type="number" id="parallelism" name="parallelism" required placeholder="请输入并行度" value="4">
          </div>
          <div class="form-group">
            <label for="dataPreprocess">数据预处理</label>
            <textarea id="dataPreprocess" name="dataPreprocess" rows="3" placeholder="请输入数据预处理脚本"></textarea>
          </div>
          <div class="form-group">
            <label for="securityLevel">安全级别</label>
            <select id="securityLevel" name="securityLevel">
              <option value="low">低</option>
              <option value="medium" selected>中</option>
              <option value="high">高</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" name="taskDescription" rows="3" placeholder="请输入任务描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addRealtimeTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addRealtimeTaskForm').submit()">保存并启动</button>
      </div>
    </div>
  </div>

  <!-- 编辑实时任务模态框 -->
  <div class="modal" id="editRealtimeTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑实时采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑实时采集任务表单内容与新增表单类似，实际应用中会加载任务的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editRealtimeTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>


 <!-- 新增三大功能入口按钮 -->
            <!-- <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="dataFilterPreprocessModal"><i class="fas fa-filter"></i> 数据预处理</button>
            <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="dataSecurityModal"><i class="fas fa-shield-alt"></i> 安全管理</button>
            <button class="btn btn-primary" style="margin-left:12px;" data-modal-target="logAuditModal"><i class="fas fa-file-lines"></i> 日志审计</button> -->
        </div>
    </div>

    <!-- 实时数据过滤预处理模态框 -->
    <div class="modal" id="dataFilterPreprocessModal">
        <div class="modal-content" style="width: 850px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <div class="modal-title"><i class="fas fa-filter"></i> 实时数据过滤预处理</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 标签页导航（功能分组） -->
                <div style="border-bottom: 1px solid var(--border-color); margin-bottom: 20px; display: flex;">
                    <button class="tab-btn active" data-tab="parse-clean" style="padding: 8px 16px; border: none; background: none; cursor: pointer; font-weight: 500;">解析与清洗</button>
                    <button class="tab-btn" data-tab="convert-filter" style="padding: 8px 16px; border: none; background: none; cursor: pointer; font-weight: 500;">转换与筛选</button>
                    <button class="tab-btn" data-tab="log-fill" style="padding: 8px 16px; border: none; background: none; cursor: pointer; font-weight: 500;">日志与填充</button>
                </div>

                <!-- 1. 解析与清洗标签页 -->
                <div class="tab-content" id="parse-clean">
                    <!-- 1.1 数据格式解析功能 -->
                    <div class="card" style="padding: 16px; margin-bottom: 20px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-code"></i> 数据格式解析</h4>
                        <div class="form-group">
                            <label for="parseRule">解析规则</label>
                            <input type="text" id="parseRule" placeholder="例如：JSONPath表达式/CSV分隔符规则" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="dataFormat">数据格式</label>
                            <select id="dataFormat" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                                <option value="xml">XML</option>
                                <option value="custom">自定义格式</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 10px; margin: 12px 0;">
                            <button class="btn btn-primary" id="queryParseResult"><i class="fas fa-search"></i> 查询解析结果</button>
                            <button class="btn btn-primary" id="saveParseInfo"><i class="fas fa-save"></i> 保存解析信息</button>
                        </div>
                        <div class="progress-container" id="parseProgress" style="display: none;">
                            <div class="progress-bar" id="parseProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="parseResult">
                                解析结果将展示于此（示例：成功解析200条数据，字段匹配率98%）
                            </div>
                            <div class="data-table-container" id="parseDataTableContainer" style="display: none;">
                                <table class="table" id="parseDataTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>字段1</th>
                                            <th>字段2</th>
                                            <th>字段3</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 1.2 清洗用户数据功能 -->
                    <div class="card" style="padding: 16px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-broom"></i> 清洗用户数据</h4>
                        <div class="form-group">
                            <label for="cleanScope">清洗范围</label>
                            <input type="text" id="cleanScope" placeholder="例如：user_name, phone, email" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="cleanRule">清洗规则</label>
                            <textarea id="cleanRule" rows="2" placeholder="例如：phone=替换空格；email=小写转换；user_name=去除特殊字符" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;"></textarea>
                        </div>
                        <div style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                            <p>默认值详情：<span>空值替换为"未知"、日期格式统一为yyyy-MM-dd</span></p>
                        </div>
                        <div style="display: flex; gap: 10px; margin: 12px 0;">
                            <button class="btn btn-primary" id="runClean"><i class="fas fa-cog"></i> 清洗运算</button>
                            <button class="btn btn-primary" id="saveCleanResult"><i class="fas fa-save"></i> 保存清洗结果</button>
                        </div>
                        <div class="progress-container" id="cleanProgress" style="display: none;">
                            <div class="progress-bar" id="cleanProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="cleanResult">
                                清洗结果将展示于此（示例：清洗完成300条数据，修复异常手机号23条）
                            </div>
                            <div class="data-table-container" id="cleanDataTableContainer" style="display: none;">
                                <table class="table" id="cleanDataTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>原始值</th>
                                            <th>清洗后值</th>
                                            <th>清洗类型</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 转换与筛选标签页 -->
                <div class="tab-content" id="convert-filter" style="display: none;">
                    <!-- 2.1 实时数据格式转换功能 -->
                    <div class="card" style="padding: 16px; margin-bottom: 20px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-exchange-alt"></i> 数据格式转换</h4>
                        <div class="form-group">
                            <label for="sourceFormat">数据原格式</label>
                            <input type="text" id="sourceFormat" placeholder="例如：MySQL表结构(user_id int, user_name varchar)" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="govFormat">政企域目标格式</label>
                            <input type="text" id="govFormat" placeholder="例如：政企数据标准(USER_ID string, USER_NAME string)" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div style="display: flex; gap: 10px; margin: 12px 0;">
                            <button class="btn btn-primary" id="runConvert"><i class="fas fa-cog"></i> 转换处理</button>
                            <button class="btn btn-primary" id="saveConvertResult"><i class="fas fa-save"></i> 保存转换格式</button>
                        </div>
                        <div class="progress-container" id="convertProgress" style="display: none;">
                            <div class="progress-bar" id="convertProgressBar"></div>
                        </div>
                        <div class="progress-container" id="saveConvertProgress" style="display: none;">
                            <div class="progress-bar" id="saveConvertProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="convertResult">
                                转换结果将展示于此（示例：字段映射完成，类型转换成功率100%）
                            </div>
                            <div class="data-table-container" id="convertDataTableContainer" style="display: none;">
                                <table class="table" id="convertDataTable">
                                    <thead>
                                        <tr>
                                            <th>原字段</th>
                                            <th>原类型</th>
                                            <th>目标字段</th>
                                            <th>目标类型</th>
                                            <th>转换状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 2.2 筛选政企用户数据功能 -->
                    <div class="card" style="padding: 16px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-filter-circle-dollar"></i> 筛选政企用户数据</h4>
                        <div class="form-group">
                            <label for="filterMethod">过滤方式</label>
                            <select id="filterMethod" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                                <option value="include">包含关键词（如"企业"、"政府"）</option>
                                <option value="exclude">排除关键词（如"个人"、"家庭"）</option>
                                <option value="range">范围筛选（如注册时间>2023-01-01）</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="govUserData">政企域用户数据</label>
                            <textarea id="govUserData" rows="3" placeholder="输入待筛选的用户数据JSON/CSV" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px; margin: 12px 0;">
                            <button class="btn btn-primary" id="runFilter"><i class="fas fa-cog"></i> 筛选运算</button>
                        </div>
                        <div class="progress-container" id="filterProgress" style="display: none;">
                            <div class="progress-bar" id="filterProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="filterResult">
                                筛选结果将展示于此（示例：符合条件数据156条，已过滤无效数据24条）
                            </div>
                            <div class="data-table-container" id="filterDataTableContainer" style="display: none;">
                                <table class="table" id="filterDataTable">
                                    <thead>
                                        <tr>
                                            <th>用户ID</th>
                                            <th>用户名称</th>
                                            <th>单位类型</th>
                                            <th>注册时间</th>
                                            <th>筛选结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3. 日志与填充标签页 -->
                <div class="tab-content" id="log-fill" style="display: none;">
                    <!-- 3.1 记录流采集操作日志功能 -->
                    <div class="card" style="padding: 16px; margin-bottom: 20px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-clipboard-list"></i> 流采集操作日志</h4>
                        <div class="form-group">
                            <label for="logScope">采集信息范围</label>
                            <input type="text" id="logScope" placeholder="例如：用户行为流/订单实时流" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="logType">采集信息类型</label>
                            <select id="logType" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                                <option value="success">采集成功</option>
                                <option value="fail">采集失败</option>
                                <option value="timeout">采集超时</option>
                                <option value="all">全部类型</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" id="recordLog" style="margin: 12px 0;"><i class="fas fa-pencil-alt"></i> 记录日志</button>
                        <div class="progress-container" id="logProgress" style="display: none;">
                            <div class="progress-bar" id="logProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="logResult">
                                日志记录结果将展示于此（示例：已记录32条操作日志，保存至op_log表）
                            </div>
                            <div class="data-table-container" id="logDataTableContainer" style="display: none;">
                                <table class="table" id="logDataTable">
                                    <thead>
                                        <tr>
                                            <th>日志ID</th>
                                            <th>操作时间</th>
                                            <th>操作类型</th>
                                            <th>操作范围</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 3.2 空值填充策略功能 -->
                    <div class="card" style="padding: 16px;">
                        <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-fill"></i> 空值填充策略</h4>
                        <div class="form-group">
                            <label for="fillCondition">填充条件</label>
                            <input type="text" id="fillCondition" placeholder="例如：age IS NULL; address IS NULL" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="fillValue">填充结果</label>
                            <input type="text" id="fillValue" placeholder="例如：age=30（默认值）; address=未知地址" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div style="display: flex; gap: 10px; margin: 12px 0;">
                            <button class="btn btn-primary" id="queryFillStatus"><i class="fas fa-search"></i> 查询填充状态</button>
                            <button class="btn btn-primary" id="saveFillStrategy"><i class="fas fa-save"></i> 保存填充策略</button>
                        </div>
                        <div class="progress-container" id="fillProgress" style="display: none;">
                            <div class="progress-bar" id="fillProgressBar"></div>
                        </div>
                        <div class="data-result-container">
                            <div class="result-stats" id="fillResult">
                                填充结果将展示于此（示例：已创建2条填充策略，覆盖85%空值场景）
                            </div>
                            <div class="data-table-container" id="fillDataTableContainer" style="display: none;">
                                <table class="table" id="fillDataTable">
                                    <thead>
                                        <tr>
                                            <th>字段名</th>
                                            <th>原始值</th>
                                            <th>填充后值</th>
                                            <th>填充策略</th>
                                            <th>填充时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('dataFilterPreprocessModal').classList.remove('show')">关闭</button>
            </div>
        </div>
    </div>

     <!-- 实时数据安全管理模态框 -->
    <div class="modal" id="dataSecurityModal">
        <div class="modal-content" style="width: 850px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <div class="modal-title"><i class="fas fa-shield-alt"></i> 实时数据安全管理</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 1. 数据标签与拆分 -->
                <div class="card" style="padding: 16px; margin-bottom: 20px;">
                    <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-tags"></i> 数据标签与拆分</h4>
                    <div class="form-group">
                        <label for="dataCategory">数据类别</label>
                        <select id="dataCategory" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="user">用户数据</option>
                            <option value="order">订单数据</option>
                            <option value="device">设备数据</option>
                            <option value="gov">政企专属数据</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dataSource">数据源</label>
                        <select id="dataSource" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="kafka">Kafka-实时数据流</option>
                            <option value="api_third">API-第三方API</option>
                            <option value="api_iot">API-物联网平台</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="dataMarker">数据标记（用于拆分）</label>
                        <input type="text" id="dataMarker" placeholder="例如：gov=1; enterprise=0" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    </div>
                    <div style="display: flex; gap: 10px; margin: 12px 0;">
                        <button class="btn btn-primary" id="assignTag"><i class="fas fa-tag"></i> 分配流数据标签</button>
                        <button class="btn btn-primary" id="splitData"><i class="fas fa-code-branch"></i> 拆分政企数据</button>
                        <!-- 去掉创建数据源通道按钮 -->
                    </div>
                    <div class="progress-container" id="tagSplitProgress" style="display: none;">
                        <div class="progress-bar" id="tagSplitProgressBar"></div>
                    </div>
                    <div class="data-result-container">
                        <div class="result-stats" id="tagSplitResult">
                            操作结果将展示于此
                        </div>
                        <div class="data-table-container" id="tagSplitDataTableContainer" style="display: none;">
                            <table class="table" id="tagSplitDataTable">
                                <thead>
                                    <tr>
                                        <th>数据ID</th>
                                        <th>数据类别</th>
                                        <th>数据源</th>
                                        <th>标签</th>
                                        <th>处理状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 数据将通过JS动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 2. 数据脱敏与访问控制 -->
                <div class="card" style="padding: 16px; margin-bottom: 20px;">
                    <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-lock"></i> 数据脱敏与访问控制</h4>
                    <div class="form-group">
                        <label for="isSensitive">是否敏感数据</label>
                        <select id="isSensitive" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sensitiveType">敏感数据类型</label>
                        <select id="sensitiveType" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="id">身份证号</option>
                            <option value="phone">手机号</option>
                            <option value="address">详细地址</option>
                            <option value="enterprise_code">企业代码</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="pageControl">数据分页控制（条数/页）</label>
                        <input type="number" id="pageControl" value="20" min="10" max="100" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    </div>
                    <div style="display: flex; gap: 10px; margin: 12px 0;">
                        <button class="btn btn-primary" id="desensitizeData"><i class="fas fa-eye-slash"></i> 执行数据脱敏</button>
                        <!-- 去掉设置分页控制按钮 -->
                    </div>
                    <div class="progress-container" id="desensitizeProgress" style="display: none;">
                        <div class="progress-bar" id="desensitizeProgressBar"></div>
                    </div>
                    <div class="data-result-container">
                        <div class="result-stats" id="desensitizeResult">
                            操作结果将展示于此
                        </div>
                        <div class="data-table-container" id="desensitizeDataTableContainer" style="display: none;">
                            <table class="table" id="desensitizeDataTable">
                                <thead>
                                    <tr>
                                        <th>数据ID</th>
                                        <th>原始值</th>
                                        <th>脱敏后值</th>
                                        <th>脱敏规则</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 数据将通过JS动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 3. 监控与调度 -->
                <div class="card" style="padding: 16px;">
                    <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-tachometer-alt"></i> 监控与调度</h4>
                    <div class="form-group">
                        <label for="monitorStream">实时数据流（监控对象）</label>
                        <select id="monitorStream" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="user_behavior">用户行为流</option>
                            <option value="order_stream">订单实时流</option>
                            <option value="device_status">设备状态流</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taskPriority">任务优先级</label>
                        <select id="taskPriority" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="1">P1（最高，如支付数据）</option>
                            <option value="2">P2（高，如订单数据）</option>
                            <option value="3">P3（中，如用户行为）</option>
                            <option value="4">P4（低，如日志数据）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="flowThreshold">实时数据阈值（条/秒，用于控制阀）</label>
                        <input type="number" id="flowThreshold" value="5000" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    </div>
                    <div style="display: flex; gap: 10px; margin: 12px 0;">
                        <button class="btn btn-primary" id="startMonitor"><i class="fas fa-eye"></i> 开启数据监听</button>
                        <button class="btn btn-primary" id="savePriority"><i class="fas fa-flag-checkered"></i> 保存优先级调度</button>
                        <button class="btn btn-primary" id="setFlowControl"><i class="fas fa-sliders"></i> 设置数据控制阀</button>
                    </div>
                    <div class="progress-container" id="monitorProgress" style="display: none;">
                        <div class="progress-bar" id="monitorProgressBar"></div>
                    </div>
                    <div class="data-result-container">
                        <div class="result-stats" id="monitorResult">
                            操作结果将展示于此
                        </div>
                        <div class="data-table-container" id="monitorDataTableContainer" style="display: none;">
                            <table class="table" id="monitorDataTable">
                                <thead>
                                    <tr>
                                        <th>监控项</th>
                                        <th>当前值</th>
                                        <th>阈值</th>
                                        <th>状态</th>
                                        <th>更新时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 数据将通过JS动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('dataSecurityModal').classList.remove('show')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 实时采集日志审计与统计模态框 -->
    <div class="modal" id="logAuditModal">
        <div class="modal-content" style="width: 900px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <div class="modal-title"><i class="fas fa-file-lines"></i> 实时采集日志审计与统计</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 1. 实时采集任务执行日志 -->
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-list-alt"></i> 任务执行日志查询</h4>
                    <div style="display: flex; gap: 12px; margin-bottom: 16px;">
                        <div style="flex: 1;">
                            <label for="logTaskId">任务ID</label>
                            <input type="text" id="logTaskId" placeholder="输入任务ID（留空查全部）" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div style="flex: 1;">
                            <label for="logTimeRange">时间范围</label>
                            <input type="text" id="logTimeRange" placeholder="例如：2023-07-01至2023-07-31" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        </div>
                        <div style="align-self: flex-end;">
                            <button class="btn btn-primary" id="queryTaskLog"><i class="fas fa-search"></i> 查询</button>
                        </div>
                    </div>
                    <!-- 日志表格 -->
                    <div class="table-container" style="border: 1px solid var(--border-color); border-radius: 4px; overflow: hidden;">
                        <table class="table">
                            <thead style="background: var(--bg-primary-light);">
                                <tr>
                                    <th>任务ID</th>
                                    <th>任务名称</th>
                                    <th>执行时间</th>
                                    <th>执行状态</th>
                                    <th>日志详情</th>
                                </tr>
                            </thead>
                            <tbody id="taskLogTable">
                                <!-- 数据将通过JS动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    <div style="margin-top: 12px; display: flex; justify-content: flex-end;">
                        <button class="btn btn-primary" id="exportLog"><i class="fas fa-download"></i> 导出日志</button>
                    </div>
                </div>

                <!-- 2. 数据来源流向与统计 -->
                <div>
                    <h4 style="margin-top: 0; color: var(--text-primary);"><i class="fas fa-project-diagram"></i> 数据来源流向与统计</h4>
                    <div class="form-group" style="margin-bottom: 16px;">
                        <label for="flowTime">采集数据时间</label>
                        <input type="text" id="flowTime" placeholder="例如：2023-07-15" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    </div>
                    <div class="form-group" style="margin-bottom: 16px;">
                        <label for="reportCondition">统计条件</label>
                        <select id="reportCondition" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                            <option value="source">按数据源统计</option>
                            <option value="task">按任务统计</option>
                            <option value="success_rate">按成功率统计</option>
                        </select>
                    </div>
                    <div style="display: flex; gap: 10px; margin-bottom: 16px;">
                        <button class="btn btn-primary" id="queryFlow"><i class="fas fa-search"></i> 查询数据来源流向</button>
                        <button class="btn btn-primary" id="generateReport"><i class="fas fa-file-pdf"></i> 生成采集结果报告</button>
                    </div>
                    
                    <div class="progress-container" id="reportProgress" style="display: none;">
                        <div class="progress-bar" id="reportProgressBar"></div>
                    </div>

                    <!-- 来源流向展示 -->
                    <div class="card" style="padding: 16px; margin-bottom: 16px;">
                        <h5 style="margin-top: 0; color: var(--text-secondary);">数据来源流向详情</h5>
                        <div class="data-result-container">
                            <div class="result-stats" id="flowResult">
                                请点击"查询数据来源流向"按钮获取数据
                            </div>
                            <div class="data-table-container" id="flowDataTableContainer" style="display: none;">
                                <table class="table" id="flowDataTable">
                                    <thead>
                                        <tr>
                                            <th>数据源</th>
                                            <th>数据量(条/秒)</th>
                                            <th>处理步骤</th>
                                            <th>目标存储</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 统计报告展示 -->
                    <div class="card" style="padding: 16px;">
                        <h5 style="margin-top: 0; color: var(--text-secondary);">采集结果报告统计</h5>
                        <div id="reportResult" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); min-height: 200px;">
                            请点击"生成采集结果报告"按钮生成报告
                        </div>
                        <div style="margin-top: 12px; display: flex; justify-content: flex-end;">
                            <button class="btn btn-primary" id="printReport"><i class="fas fa-print"></i> 打印报告</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('logAuditModal').classList.remove('show')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 新增任务模态框 -->
    <div class="modal" id="addRealtimeTaskModal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <div class="modal-title"><i class="fas fa-plus"></i> 新增实时任务</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>新增任务功能正在开发中...</p>
            </div>
            <div class="modal-footer">
                <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addRealtimeTaskModal').classList.remove('show')">关闭</button>
            </div>
        </div>
    </div>
    <script>
        // 模拟进度条加载函数
        function simulateProgress(progressContainer, progressBar, resultElement, successMessage, duration = 1500, callback = null) {
            // 显示进度条
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            
            // 清空结果区域
            resultElement.textContent = '处理中...';
            
            // 模拟进度增长
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    
                    // 进度完成后显示结果
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                        resultElement.textContent = successMessage;
                        if (callback) callback();
                    }, 300);
                }
                progressBar.style.width = `${progress}%`;
            }, duration / 10);
        }

        // 生成解析数据列表
        function generateParseDataTable(count) {
            const tableBody = document.querySelector('#parseDataTable tbody');
            tableBody.innerHTML = '';
            
            const statuses = [
                {class: 'tag-success', text: '解析成功'},
                {class: 'tag-danger', text: '解析失败'}
            ];
            
            for (let i = 1; i <= count; i++) {
                const randomStatus = statuses[Math.random() > 0.1 ? 0 : 1]; // 90%成功率
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${i}</td>
                    <td>值${i}-1</td>
                    <td>值${i}-2</td>
                    <td>值${i}-3</td>
                    <td><span class="tag ${randomStatus.class}">${randomStatus.text}</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('parseDataTableContainer').style.display = 'block';
        }

        // 生成清洗数据列表
        function generateCleanDataTable(count) {
            const tableBody = document.querySelector('#cleanDataTable tbody');
            tableBody.innerHTML = '';
            
            const cleanTypes = ['格式修正', '空值填充', '特殊字符去除', '大小写转换'];
            
            for (let i = 1; i <= count; i++) {
                const cleanType = cleanTypes[Math.floor(Math.random() * cleanTypes.length)];
                const originalValue = i % 3 === 0 ? '' : `原始值${i}${i % 4 === 0 ? '###' : ''}`;
                const cleanedValue = originalValue === '' ? '未知' : 
                                    originalValue.includes('###') ? originalValue.replace('###', '') : 
                                    originalValue;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${i}</td>
                    <td>${originalValue}</td>
                    <td>${cleanedValue}</td>
                    <td>${cleanType}</td>
                    <td><span class="tag tag-success">清洗完成</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('cleanDataTableContainer').style.display = 'block';
        }

        // 生成转换数据列表
        function generateConvertDataTable() {
            const tableBody = document.querySelector('#convertDataTable tbody');
            tableBody.innerHTML = '';
            
            const fields = [
                {original: 'user_id', originalType: 'int', target: 'USER_ID', targetType: 'string'},
                {original: 'user_name', originalType: 'varchar', target: 'USER_NAME', targetType: 'string'},
                {original: 'register_time', originalType: 'datetime', target: 'REGISTER_TIME', targetType: 'timestamp'},
                {original: 'status', originalType: 'tinyint', target: 'STATUS', targetType: 'string'},
                {original: 'phone', originalType: 'varchar', target: 'PHONE_NUMBER', targetType: 'string'}
            ];
            
            fields.forEach((field, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${field.original}</td>
                    <td>${field.originalType}</td>
                    <td>${field.target}</td>
                    <td>${field.targetType}</td>
                    <td><span class="tag tag-success">转换成功</span></td>
                `;
                tableBody.appendChild(row);
            });
            
            document.getElementById('convertDataTableContainer').style.display = 'block';
        }

        // 生成筛选数据列表
        function generateFilterDataTable(count) {
            const tableBody = document.querySelector('#filterDataTable tbody');
            tableBody.innerHTML = '';
            
            const types = ['政府机构', '国有企业', '私营企业', '个人用户', '事业单位'];
            const results = [
                {class: 'tag-success', text: '符合条件'},
                {class: 'tag-danger', text: '不符合'}
            ];
            
            for (let i = 1; i <= count; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                const result = type === '个人用户' ? results[1] : results[0];
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 365));
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>U${i.toString().padStart(6, '0')}</td>
                    <td>${type}用户${i}</td>
                    <td>${type}</td>
                    <td>${date.toLocaleDateString()}</td>
                    <td><span class="tag ${result.class}">${result.text}</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('filterDataTableContainer').style.display = 'block';
        }

        // 生成日志数据列表
        function generateLogDataTable(count) {
            const tableBody = document.querySelector('#logDataTable tbody');
            tableBody.innerHTML = '';
            
            const types = ['数据采集', '格式转换', '数据清洗', '系统监控'];
            const statuses = [
                {class: 'tag-success', text: '成功'},
                {class: 'tag-danger', text: '失败'},
                {class: 'tag-warning', text: '超时'}
            ];
            
            for (let i = 1; i <= count; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                const status = statuses[Math.random() > 0.15 ? 0 : Math.random() > 0.5 ? 1 : 2];
                const date = new Date();
                date.setMinutes(date.getMinutes() - Math.floor(Math.random() * 120));
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>L${i.toString().padStart(8, '0')}</td>
                    <td>${date.toLocaleString()}</td>
                    <td>${type}</td>
                    <td>${document.getElementById('logScope').value || '默认范围'}</td>
                    <td><span class="tag ${status.class}">${status.text}</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('logDataTableContainer').style.display = 'block';
        }

        // 生成填充数据列表
        function generateFillDataTable(count) {
            const tableBody = document.querySelector('#fillDataTable tbody');
            tableBody.innerHTML = '';
            
            const fields = ['age', 'address', 'phone', 'email', 'department'];
            const strategies = ['默认值填充', '平均值填充', '前值填充', '后值填充', '自定义填充'];
            
            for (let i = 1; i <= count; i++) {
                const field = fields[Math.floor(Math.random() * fields.length)];
                const strategy = strategies[Math.floor(Math.random() * strategies.length)];
                const date = new Date();
                date.setMinutes(date.getMinutes() - Math.floor(Math.random() * 60));
                
                let originalValue = '';
                let filledValue = '';
                
                switch(field) {
                    case 'age':
                        originalValue = '';
                        filledValue = '30';
                        break;
                    case 'address':
                        originalValue = '';
                        filledValue = '未知地址';
                        break;
                    case 'phone':
                        originalValue = '';
                        filledValue = '未知号码';
                        break;
                    default:
                        originalValue = '';
                        filledValue = '未知';
                }
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${field}</td>
                    <td>${originalValue}</td>
                    <td>${filledValue}</td>
                    <td>${strategy}</td>
                    <td>${date.toLocaleString()}</td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('fillDataTableContainer').style.display = 'block';
        }

        // 生成标签与拆分数据列表
        function generateTagSplitDataTable(count, actionType) {
            const tableBody = document.querySelector('#tagSplitDataTable tbody');
            tableBody.innerHTML = '';
            
            const categories = ['用户数据', '订单数据', '设备数据', '政企专属数据'];
            const sources = ['Kafka-实时数据流', 'API-第三方API', 'API-物联网平台'];
            const tags = ['gov_data', 'enterprise_data', 'public_data', 'sensitive_data', 'normal_data'];
            
            for (let i = 1; i <= count; i++) {
                const category = categories[Math.floor(Math.random() * categories.length)];
                const source = sources[Math.floor(Math.random() * sources.length)];
                const tag = tags[Math.floor(Math.random() * tags.length)];
                const actionText = actionType === 'tag' ? '标签分配' : '数据拆分';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>D${i.toString().padStart(8, '0')}</td>
                    <td>${category}</td>
                    <td>${source}</td>
                    <td>${tag}</td>
                    <td><span class="tag tag-success">${actionText}完成</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('tagSplitDataTableContainer').style.display = 'block';
        }

        // 生成脱敏数据列表
        function generateDesensitizeDataTable(count) {
            const tableBody = document.querySelector('#desensitizeDataTable tbody');
            tableBody.innerHTML = '';
            
            const sensitiveTypes = {
                'id': {
                    original: '110101199001011234',
                    desensitized: '110101********1234',
                    rule: '保留前6位和后4位'
                },
                'phone': {
                    original: '13812345678',
                    desensitized: '138****5678',
                    rule: '保留前3位和后4位'
                },
                'address': {
                    original: '北京市朝阳区建国路88号',
                    desensitized: '北京市朝阳区********',
                    rule: '保留到区县级'
                },
                'enterprise_code': {
                    original: '91110105MA00123456',
                    desensitized: '91110105MA********',
                    rule: '保留前10位'
                }
            };
            
            const selectedType = document.getElementById('sensitiveType').value;
            const typeData = sensitiveTypes[selectedType];
            
            for (let i = 1; i <= count; i++) {
                // 稍微变化原始值以模拟不同记录
                let originalValue = typeData.original;
                if (i > 1) {
                    originalValue = originalValue.substring(0, originalValue.length - 1) + (parseInt(originalValue.slice(-1)) + i) % 10;
                }
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>S${i.toString().padStart(6, '0')}</td>
                    <td>${originalValue}</td>
                    <td>${typeData.desensitized}</td>
                    <td>${typeData.rule}</td>
                    <td><span class="tag tag-success">脱敏完成</span></td>
                `;
                tableBody.appendChild(row);
            }
            
            document.getElementById('desensitizeDataTableContainer').style.display = 'block';
        }

        // 生成监控数据列表
        function generateMonitorDataTable(actionType) {
            const tableBody = document.querySelector('#monitorDataTable tbody');
            tableBody.innerHTML = '';
            
            const streamName = document.getElementById('monitorStream').options[document.getElementById('monitorStream').selectedIndex].text;
            const priority = document.getElementById('taskPriority').options[document.getElementById('taskPriority').selectedIndex].text;
            const threshold = document.getElementById('flowThreshold').value;
            
            // 当前值略低于阈值，模拟正常状态
            const currentValue = Math.floor(threshold * (0.8 + Math.random() * 0.15));
            
            const monitorItems = [];
            
            if (actionType === 'monitor' || actionType === 'all') {
                monitorItems.push({
                    name: '数据流量',
                    current: `${currentValue}条/秒`,
                    threshold: `${threshold}条/秒`,
                    status: currentValue < threshold ? '正常' : '超限',
                    statusClass: currentValue < threshold ? 'tag-success' : 'tag-danger'
                });
                monitorItems.push({
                    name: '数据延迟',
                    current: `${Math.floor(Math.random() * 100)}ms`,
                    threshold: '500ms',
                    status: '正常',
                    statusClass: 'tag-success'
                });
                monitorItems.push({
                    name: '错误率',
                    current: `${(Math.random() * 0.5).toFixed(2)}%`,
                    threshold: '1%',
                    status: '正常',
                    statusClass: 'tag-success'
                });
            }
            
            if (actionType === 'priority' || actionType === 'all') {
                monitorItems.push({
                    name: '任务优先级',
                    current: priority,
                    threshold: '-',
                    status: '已设置',
                    statusClass: 'tag-success'
                });
            }
            
            if (actionType === 'flow' || actionType === 'all') {
                monitorItems.push({
                    name: '流量控制阀',
                    current: `开启 (${threshold}条/秒)`,
                    threshold: '-',
                    status: '已激活',
                    statusClass: 'tag-success'
                });
            }
            
            monitorItems.forEach((item, index) => {
                const date = new Date();
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.current}</td>
                    <td>${item.threshold}</td>
                    <td><span class="tag ${item.statusClass}">${item.status}</span></td>
                    <td>${date.toLocaleTimeString()}</td>
                `;
                tableBody.appendChild(row);
            });
            
            document.getElementById('monitorDataTableContainer').style.display = 'block';
        }

        // 生成数据来源流向列表
        function generateFlowDataTable() {
            const tableBody = document.querySelector('#flowDataTable tbody');
            tableBody.innerHTML = '';
            
            const dataSources = [
                {name: 'Kafka-用户行为流', steps: '清洗→转换→存储', target: 'dw.user_behavior'},
                {name: 'Kafka-订单实时流', steps: '验证→转换→存储', target: 'dw.order_stream'},
                {name: 'API-第三方用户数据', steps: '脱敏→映射→存储', target: 'dw.third_party_user'},
                {name: 'API-物联网设备数据', steps: '解析→过滤→存储', target: 'dw.device_data'},
                {name: '政企数据专线', steps: '加密验证→格式转换→存储', target: 'dw.gov_enterprise_data'}
            ];
            
            dataSources.forEach((source, index) => {
                const dataVolume = Math.floor(Math.random() * 1500) + 500;
                const status = Math.random() > 0.05 ? '正常' : '异常';
                const statusClass = status === '正常' ? 'tag-success' : 'tag-danger';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${source.name}</td>
                    <td>${dataVolume}</td>
                    <td>${source.steps}</td>
                    <td>${source.target}</td>
                    <td><span class="tag ${statusClass}">${status}</span></td>
                `;
                tableBody.appendChild(row);
            });
            
            document.getElementById('flowDataTableContainer').style.display = 'block';
        }

        // 标签页切换逻辑
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有标签页的active状态
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.style.display = 'none');
                // 激活当前标签页
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).style.display = 'block';
            });
        });

                // 数据预处理模块交互
        document.getElementById('queryParseResult').addEventListener('click', async function() {
          const parseRule = document.getElementById('parseRule').value || '默认JSON解析规则';
          const dataFormat = document.getElementById('dataFormat').options[document.getElementById('dataFormat').selectedIndex].text;
          
          try {
            // 调用查询解析结果接口
            await apiRequest(`${API_BASE_URL}/realtime-data/parse-result`, 'POST', {
              parseRule: parseRule,
              dataFormat: dataFormat
            });
            console.log('查询解析结果成功');
          } catch (error) {
            console.error('查询解析结果失败:', error);
          }
          
          // 保持原有逻辑
          const recordCount = Math.floor(Math.random() * 500) + 100;
          const successRate = Math.floor(Math.random() * 10) + 90;
          const successMessage = `已解析规则：${parseRule}，格式：${dataFormat}，成功匹配${recordCount}条数据，字段完整率${successRate}%`;
          
          simulateProgress(
            document.getElementById('parseProgress'),
            document.getElementById('parseProgressBar'),
            document.getElementById('parseResult'),
            successMessage,
            1500,
            () => generateParseDataTable(Math.min(10, recordCount))
          );
        });

                document.getElementById('saveParseInfo').addEventListener('click', async function() {
          const parseRule = document.getElementById('parseRule').value || '默认JSON解析规则';
          const dataFormat = document.getElementById('dataFormat').options[document.getElementById('dataFormat').selectedIndex].text;
          
          try {
            // 调用保存解析信息接口
            await apiRequest(`${API_BASE_URL}/realtime-data/save-parse-info`, 'POST', {
              parseRule: parseRule,
              dataFormat: dataFormat
            });
            console.log('保存解析信息成功');
          } catch (error) {
            console.error('保存解析信息失败:', error);
          }
          
          // 保持原有逻辑
          alert('数据格式解析信息已保存至关系型数据库！');
        });

                document.getElementById('runClean').addEventListener('click', async function() {
          const cleanScope = document.getElementById('cleanScope').value || '所有用户字段';
          const cleanRule = document.getElementById('cleanRule').value || '默认清洗规则';
          
          try {
            // 调用清洗运算接口
            await apiRequest(`${API_BASE_URL}/realtime-data/clean`, 'POST', {
              cleanScope: cleanScope,
              cleanRule: cleanRule
            });
            console.log('清洗运算成功');
          } catch (error) {
            console.error('清洗运算失败:', error);
          }
          
          // 保持原有逻辑
          const totalRecords = Math.floor(Math.random() * 1000) + 200;
          const fixedRecords = Math.floor(Math.random() * 50) + 5;
          const successMessage = `清洗完成${totalRecords}条数据，涉及范围：${cleanScope}，修复异常值${fixedRecords}条`;
          
          simulateProgress(
            document.getElementById('cleanProgress'),
            document.getElementById('cleanProgressBar'),
            document.getElementById('cleanResult'),
            successMessage,
            1500,
            () => generateCleanDataTable(Math.min(10, totalRecords))
          );
        });

                document.getElementById('saveCleanResult').addEventListener('click', async function() {
          try {
            // 调用保存清洗结果接口
            await apiRequest(`${API_BASE_URL}/realtime-data/save-clean-result`, 'POST', {
              timestamp: new Date().toISOString()
            });
            console.log('保存清洗结果成功');
          } catch (error) {
            console.error('保存清洗结果失败:', error);
          }
          
          // 保持原有逻辑
          alert('清洗结果已成功保存至数据库！');
        });

                document.getElementById('runConvert').addEventListener('click', async function() {
          const sourceFormat = document.getElementById('sourceFormat').value || '原始数据格式';
          const govFormat = document.getElementById('govFormat').value || '政企标准格式';
          
          try {
            // 调用转换处理接口
            await apiRequest(`${API_BASE_URL}/realtime-data/convert`, 'POST', {
              sourceFormat: sourceFormat,
              govFormat: govFormat
            });
            console.log('转换处理成功');
          } catch (error) {
            console.error('转换处理失败:', error);
          }
          
          // 保持原有逻辑
          const successRate = Math.floor(Math.random() * 5) + 95;
          const totalRecords = Math.floor(Math.random() * 800) + 200;
          const successMessage = `格式转换完成，从${sourceFormat}转换为${govFormat}，转换成功率${successRate}%，处理记录${totalRecords}条`;
          
          simulateProgress(
            document.getElementById('convertProgress'),
            document.getElementById('convertProgressBar'),
            document.getElementById('convertResult'),
            successMessage,
            1500,
            generateConvertDataTable
          );
        });

                document.getElementById('saveConvertResult').addEventListener('click', async function() {
          const sourceFormat = document.getElementById('sourceFormat').value || '原始数据格式';
          const govFormat = document.getElementById('govFormat').value || '政企标准格式';
          
          try {
            // 调用保存转换格式接口
            await apiRequest(`${API_BASE_URL}/realtime-data/save-convert-format`, 'POST', {
              sourceFormat: sourceFormat,
              govFormat: govFormat
            });
            console.log('保存转换格式成功');
          } catch (error) {
            console.error('保存转换格式失败:', error);
          }
          
          // 保持原有逻辑
          const successMessage = `转换格式已成功保存，可在后续任务中直接应用`;
          
          simulateProgress(
            document.getElementById('saveConvertProgress'),
            document.getElementById('saveConvertProgressBar'),
            document.getElementById('convertResult'),
            successMessage
          );
        });

                document.getElementById('runFilter').addEventListener('click', async function() {
          const filterMethod = document.getElementById('filterMethod').options[document.getElementById('filterMethod').selectedIndex].text;
          const govUserData = document.getElementById('govUserData').value || '默认政企用户数据';
          
          try {
            // 调用筛选运算接口
            await apiRequest(`${API_BASE_URL}/realtime-data/filter`, 'POST', {
              filterMethod: filterMethod,
              govUserData: govUserData
            });
            console.log('筛选运算成功');
          } catch (error) {
            console.error('筛选运算失败:', error);
          }
          
          // 保持原有逻辑
          const totalRecords = Math.floor(Math.random() * 1000) + 500;
          const matchedRecords = Math.floor(totalRecords * (Math.random() * 0.5 + 0.2));
          const successMessage = `筛选完成，共处理${totalRecords}条数据，使用过滤方式：${filterMethod}，匹配成功${matchedRecords}条，过滤无效数据${totalRecords - matchedRecords}条`;
          
          simulateProgress(
            document.getElementById('filterProgress'),
            document.getElementById('filterProgressBar'),
            document.getElementById('filterResult'),
            successMessage,
            1500,
            () => generateFilterDataTable(Math.min(10, matchedRecords))
          );
        });

                document.getElementById('recordLog').addEventListener('click', async function() {
          const logScope = document.getElementById('logScope').value || '全部采集范围';
          const logType = document.getElementById('logType').options[document.getElementById('logType').selectedIndex].text;
          
          try {
            // 调用记录日志接口
            await apiRequest(`${API_BASE_URL}/realtime-data/record-log`, 'POST', {
              logScope: logScope,
              logType: logType
            });
            console.log('记录日志成功');
          } catch (error) {
            console.error('记录日志失败:', error);
          }
          
          // 保持原有逻辑
          const logCount = Math.floor(Math.random() * 100) + 10;
          const successMessage = `已记录${logCount}条${logType}类型日志，采集范围：${logScope}，已保存至op_log表`;
          
          simulateProgress(
            document.getElementById('logProgress'),
            document.getElementById('logProgressBar'),
            document.getElementById('logResult'),
            successMessage,
            1500,
            () => generateLogDataTable(Math.min(10, logCount))
          );
        });

                document.getElementById('queryFillStatus').addEventListener('click', async function() {
          const fillCondition = document.getElementById('fillCondition').value || '默认空值条件';
          const fillValue = document.getElementById('fillValue').value || '系统默认值';
          
          try {
            // 调用查询填充状态接口
            await apiRequest(`${API_BASE_URL}/realtime-data/query-fill-status`, 'POST', {
              fillCondition: fillCondition,
              fillValue: fillValue
            });
            console.log('查询填充状态成功');
          } catch (error) {
            console.error('查询填充状态失败:', error);
          }
          
          // 保持原有逻辑
          const fillCount = Math.floor(Math.random() * 100) + 10;
          const successMessage = `查询到${fillCount}条符合填充条件${fillCondition}的记录，已使用${fillValue}进行填充`;
          
          simulateProgress(
            document.getElementById('fillProgress'),
            document.getElementById('fillProgressBar'),
            document.getElementById('fillResult'),
            successMessage,
            1500,
            () => generateFillDataTable(Math.min(10, fillCount))
          );
        });

                document.getElementById('saveFillStrategy').addEventListener('click', async function() {
          const fillCondition = document.getElementById('fillCondition').value || '默认空值条件';
          const fillValue = document.getElementById('fillValue').value || '系统默认值';
          
          try {
            // 调用保存填充策略接口
            await apiRequest(`${API_BASE_URL}/realtime-data/save-fill-strategy`, 'POST', {
              fillCondition: fillCondition,
              fillValue: fillValue
            });
            console.log('保存填充策略成功');
          } catch (error) {
            console.error('保存填充策略失败:', error);
          }
          
          // 保持原有逻辑
          alert('空值填充策略已保存，将在后续数据处理中自动应用！');
        });

                // 数据安全管理模块交互
        document.getElementById('assignTag').addEventListener('click', async function() {
          const dataCategory = document.getElementById('dataCategory').options[document.getElementById('dataCategory').selectedIndex].text;
          const dataMarker = document.getElementById('dataMarker').value || '默认标记规则';
          
          try {
            // 调用分配标签接口
            await apiRequest(`${API_BASE_URL}/realtime-security/assign-tag`, 'POST', {
              dataCategory: dataCategory,
              dataMarker: dataMarker
            });
            console.log('分配标签成功');
          } catch (error) {
            console.error('分配标签失败:', error);
          }
          
          // 保持原有逻辑
          const tagCount = Math.floor(Math.random() * 200) + 50;
          const successMessage = `已成功为${tagCount}条${dataCategory}分配标签，使用标记规则：${dataMarker}`;
          
          simulateProgress(
            document.getElementById('tagSplitProgress'),
            document.getElementById('tagSplitProgressBar'),
            document.getElementById('tagSplitResult'),
            successMessage,
            1500,
            () => generateTagSplitDataTable(Math.min(10, tagCount), 'tag')
          );
        });

                document.getElementById('splitData').addEventListener('click', async function() {
          const dataCategory = document.getElementById('dataCategory').options[document.getElementById('dataCategory').selectedIndex].text;
          
          try {
            // 调用拆分政企数据接口
            await apiRequest(`${API_BASE_URL}/realtime-security/split-data`, 'POST', {
              dataCategory: dataCategory
            });
            console.log('拆分政企数据成功');
          } catch (error) {
            console.error('拆分政企数据失败:', error);
          }
          
          // 保持原有逻辑
          const splitCount = Math.floor(Math.random() * 300) + 50;
          const successMessage = `已成功拆分${dataCategory}，共得到${splitCount}条政企数据，已分配至专用存储区域`;
          
          simulateProgress(
            document.getElementById('tagSplitProgress'),
            document.getElementById('tagSplitProgressBar'),
            document.getElementById('tagSplitResult'),
            successMessage,
            1500,
            () => generateTagSplitDataTable(Math.min(10, splitCount), 'split')
          );
        });

                document.getElementById('desensitizeData').addEventListener('click', async function() {
          const type = document.getElementById('sensitiveType').options[document.getElementById('sensitiveType').selectedIndex].text;
          const isSensitive = document.getElementById('isSensitive').value;
          
          try {
            // 调用执行数据脱敏接口
            await apiRequest(`${API_BASE_URL}/realtime-security/desensitize`, 'POST', {
              sensitiveType: type,
              isSensitive: isSensitive
            });
            console.log('执行数据脱敏成功');
          } catch (error) {
            console.error('执行数据脱敏失败:', error);
          }
          
          // 保持原有逻辑
          const count = Math.floor(Math.random() * 500) + 100;
          const successMessage = `${type}已按规则脱敏处理，共脱敏${count}条记录，脱敏后格式符合隐私保护规范`;
          
          simulateProgress(
            document.getElementById('desensitizeProgress'),
            document.getElementById('desensitizeProgressBar'),
            document.getElementById('desensitizeResult'),
            successMessage,
            1500,
            () => generateDesensitizeDataTable(Math.min(10, count))
          );
        });

                document.getElementById('startMonitor').addEventListener('click', async function() {
          const streamName = document.getElementById('monitorStream').options[document.getElementById('monitorStream').selectedIndex].text;
          
          try {
            // 调用开启数据监听接口
            await apiRequest(`${API_BASE_URL}/realtime-security/start-monitor`, 'POST', {
              streamName: streamName
            });
            console.log('开启数据监听成功');
          } catch (error) {
            console.error('开启数据监听失败:', error);
          }
          
          // 保持原有逻辑
          const successMessage = `已成功开启${streamName}的数据监听，实时监控数据流量、延迟和错误率`;
          
          simulateProgress(
            document.getElementById('monitorProgress'),
            document.getElementById('monitorProgressBar'),
            document.getElementById('monitorResult'),
            successMessage,
            1500,
            () => generateMonitorDataTable('monitor')
          );
        });

                document.getElementById('savePriority').addEventListener('click', async function() {
          const priority = document.getElementById('taskPriority').options[document.getElementById('taskPriority').selectedIndex].text;
          const streamName = document.getElementById('monitorStream').options[document.getElementById('monitorStream').selectedIndex].text;
          
          try {
            // 调用保存优先级调度接口
            await apiRequest(`${API_BASE_URL}/realtime-security/save-priority`, 'POST', {
              priority: priority,
              streamName: streamName
            });
            console.log('保存优先级调度成功');
          } catch (error) {
            console.error('保存优先级调度失败:', error);
          }
          
          // 保持原有逻辑
          const successMessage = `已成功将${streamName}的任务优先级设置为${priority}，系统将优先处理该任务`;
          
          simulateProgress(
            document.getElementById('monitorProgress'),
            document.getElementById('monitorProgressBar'),
            document.getElementById('monitorResult'),
            successMessage,
            1500,
            () => generateMonitorDataTable('priority')
          );
        });

                document.getElementById('setFlowControl').addEventListener('click', async function() {
          const threshold = document.getElementById('flowThreshold').value;
          const streamName = document.getElementById('monitorStream').options[document.getElementById('monitorStream').selectedIndex].text;
          
          try {
            // 调用设置数据控制阀接口
            await apiRequest(`${API_BASE_URL}/realtime-security/set-flow-control`, 'POST', {
              threshold: threshold,
              streamName: streamName
            });
            console.log('设置数据控制阀成功');
          } catch (error) {
            console.error('设置数据控制阀失败:', error);
          }
          
          // 保持原有逻辑
          const successMessage = `已成功为${streamName}设置数据控制阀，阈值为${threshold}条/秒，超过阈值将自动限流`;
          
          simulateProgress(
            document.getElementById('monitorProgress'),
            document.getElementById('monitorProgressBar'),
            document.getElementById('monitorResult'),
            successMessage,
            1500,
            () => generateMonitorDataTable('flow')
          );
        });

                // 日志审计模块交互
        document.getElementById('queryTaskLog').addEventListener('click', async function() {
          const taskId = document.getElementById('logTaskId').value || '全部任务';
          const timeRange = document.getElementById('logTimeRange').value || '全部时间';
          
          try {
            // 调用查询任务日志接口
            await apiRequest(`${API_BASE_URL}/realtime-audit/query-task-log`, 'POST', {
              taskId: taskId,
              timeRange: timeRange
            });
            console.log('查询任务日志成功');
          } catch (error) {
            console.error('查询任务日志失败:', error);
          }
          
          // 保持原有逻辑
          // 清空现有日志
          const logTable = document.getElementById('taskLogTable');
          logTable.innerHTML = '';
          
          // 生成随机日志数据
          const logCount = Math.floor(Math.random() * 5) + 3;
          const statuses = [
            {class: 'tag-success', text: '成功'},
            {class: 'tag-danger', text: '失败'},
            {class: 'tag-warning', text: '警告'}
          ];
          
          for (let i = 0; i < logCount; i++) {
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 10));
            
            const row = document.createElement('tr');
            row.innerHTML = `
              <td>T${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2,'0')}${date.getDate().toString().padStart(2,'0')}${i+1}</td>
              <td>${['用户行为采集', '订单数据同步', '设备状态监控', '政企数据对接'][Math.floor(Math.random() * 4)]}</td>
              <td>${date.toLocaleString()}</td>
              <td><span class="tag ${randomStatus.class}">${randomStatus.text}</span></td>
              <td>${randomStatus.text === '成功' ? '执行正常，无异常' : 
                 randomStatus.text === '失败' ? '连接超时，已重试' : 
                 '数据量过大，处理延迟'}</td>
            `;
            logTable.appendChild(row);
          }
          
          alert(`已查询${timeRange}的${taskId}日志，共${logCount}条记录`);
        });

        document.getElementById('queryFlow').addEventListener('click', async function() {
            const flowTime = document.getElementById('flowTime').value || new Date().toLocaleDateString();
            
            try {
              // 调用查询数据来源流向接口
              await apiRequest(`${API_BASE_URL}/realtime-audit/query-flow`, 'POST', {
                flowTime: flowTime
              });
              console.log('查询数据来源流向成功');
            } catch (error) {
              console.error('查询数据来源流向失败:', error);
            }
            
            // 保持原有逻辑
            const successMessage = `已查询${flowTime}的数据来源流向信息，共获取5个数据源的实时流向数据`;
            
            document.getElementById('flowResult').textContent = successMessage;
            generateFlowDataTable();
        });

        document.getElementById('generateReport').addEventListener('click', async function() {
            const flowTime = document.getElementById('flowTime').value || new Date().toLocaleDateString();
            const reportCondition = document.getElementById('reportCondition').options[document.getElementById('reportCondition').selectedIndex].text;
            
            try {
              // 调用生成采集结果报告接口
              await apiRequest(`${API_BASE_URL}/realtime-audit/generate-report`, 'POST', {
                flowTime: flowTime,
                reportCondition: reportCondition
              });
              console.log('生成采集结果报告成功');
            } catch (error) {
              console.error('生成采集结果报告失败:', error);
            }
            
            // 保持原有逻辑
            simulateProgress(
                document.getElementById('reportProgress'),
                document.getElementById('reportProgressBar'),
                document.getElementById('reportResult'),
                '',
                2000
            );
            
            // 报告生成完成后显示详细报告（保持显示直到重新生成或关闭弹窗）
            setTimeout(() => {
                const totalTasks = Math.floor(Math.random() * 10) + 3;
                const runningTasks = Math.floor(Math.random() * totalTasks);
                const errorTasks = Math.floor(Math.random() * (totalTasks - runningTasks));
                const stoppedTasks = totalTasks - runningTasks - errorTasks;
                
                const totalRecords = Math.floor(Math.random() * 1000000) + 500000;
                const successRate = (95 + Math.random() * 5).toFixed(1);
                
                const kafkaPercent = Math.floor(Math.random() * 40) + 50;
                const apiPercent = 100 - kafkaPercent;
                
                let reportContent = `
                    <div class="report-section">
                        <h4 class="report-title">实时数据采集统计报告</h4>
                        <p>报告日期：${flowTime}</p>
                        <p>统计条件：${reportCondition}</p>
                    </div>
                    
                    <div class="report-section">
                        <h5 class="report-title">任务执行概况</h5>
                        <p>• 总任务数：${totalTasks}个</p>
                        <p>• 运行中：${runningTasks}个 (${(runningTasks/totalTasks*100).toFixed(1)}%)</p>
                        <p>• 异常：${errorTasks}个 (${(errorTasks/totalTasks*100).toFixed(1)}%)</p>
                        <p>• 已停止：${stoppedTasks}个 (${(stoppedTasks/totalTasks*100).toFixed(1)}%)</p>
                    </div>
                    
                    <div class="report-section">
                        <h5 class="report-title">数据处理量统计</h5>
                        <p>• 总处理量：${totalRecords.toLocaleString()}条</p>
                        <p>• 平均处理速率：${Math.floor(Math.random() * 1500) + 500}条/秒</p>
                        <p>• 处理成功率：${successRate}%</p>
                        
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-bar fa-2x"></i>
                            <span style="margin-left: 8px;">数据处理趋势图</span>
                        </div>
                    </div>
                    
                    <div class="report-section">
                        <h5 class="report-title">数据源分布</h5>
                        <p>• Kafka实时流：${kafkaPercent}%</p>
                        <p>• API接口：${apiPercent}%</p>
                        
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie fa-2x"></i>
                            <span style="margin-left: 8px;">数据源占比饼图</span>
                        </div>
                    </div>
                    
                    <div class="report-section">
                        <h5 class="report-title">异常情况分析</h5>
                        <p>• 主要异常类型：${errorTasks > 0 ? ['连接超时', '数据格式错误', '权限不足'][Math.floor(Math.random() * 3)] : '无异常'}</p>
                        <p>• 异常处理建议：${errorTasks > 0 ? '检查网络连接，优化数据校验规则，更新访问凭证' : '保持当前配置'}</p>
                    </div>
                `;
                
                document.getElementById('reportResult').innerHTML = reportContent;
            }, 2300);
        });

        document.getElementById('printReport').addEventListener('click', async function() {
            try {
              // 调用打印报告接口
              await apiRequest(`${API_BASE_URL}/realtime-audit/print-report`, 'POST', {
                timestamp: new Date().toISOString()
              });
              console.log('打印报告成功');
            } catch (error) {
              console.error('打印报告失败:', error);
            }
            
            // 保持原有逻辑
            alert('报告打印功能已触发，正在准备打印文档...');
            // 实际项目中可调用window.print()
        });

        // 模态框显示/隐藏逻辑
        document.querySelectorAll('[data-modal-target]').forEach(btn => {
            btn.addEventListener('click', function() {
                const modalId = this.getAttribute('data-modal-target');
                document.getElementById(modalId).classList.add('show');
            });
        });

        document.querySelectorAll('.modal-close, .modal-footer .btn:not(.btn-primary)').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.modal').classList.remove('show');
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.classList.remove('show');
            }
        });
    </script></body>
</html>
  <script src="js/common.js"></script>
  <script>
    // API基础URL
    const API_BASE_URL = 'http://localhost:8000/api';

    // 封装fetch请求函数
    async function apiRequest(url, method, data = null) {
      try {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
          }
        };

        if (data) {
          options.body = JSON.stringify(data);
        }

        console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
        const response = await fetch(url, options);
        const result = await response.json();
        console.log(`接口响应:`, result);

        if (!response.ok) {
          throw new Error(result.message || `请求失败: ${response.status}`);
        }

        return result;
      } catch (error) {
        console.error('API请求错误:', error);
        throw error;
      }
    }

    // 显示通知
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = 'notification ' + type;
      notification.innerText = message;
      document.body.appendChild(notification);

      // 显示通知
      setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
      }, 10);

      // 3秒后隐藏通知
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }

    // 初始化操作列按钮事件
    function initOperationButtons() {
      // 编辑按钮点击事件
      document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
          const taskId = this.getAttribute('data-task-id');
          try {
            // 调用获取任务详情接口
            await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}`, 'GET');
            console.log('获取任务详情成功');
          } catch (error) {
            console.error('获取任务详情失败:', error);
          }
          // 保持原有逻辑
          loadTaskData(taskId);
        });
      });

      // 删除按钮点击事件
      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
          const taskId = this.getAttribute('data-task-id');
          if (confirm(`确定要删除任务ID为${taskId}的实时采集任务吗？`)) {
            try {
              // 调用删除任务接口
              await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}`, 'DELETE');
              console.log('删除任务成功');
            } catch (error) {
              console.error('删除任务失败:', error);
            }
            // 保持原有逻辑
            simulateDeleteTask(taskId);
          }
        });
      });

      // 状态切换按钮点击事件
      document.querySelectorAll('.status-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
          const taskId = this.getAttribute('data-task-id');
          const btnIcon = this.querySelector('i');
          const btnStyle = this.getAttribute('style');

          // 根据当前图标判断操作类型
          if (btnIcon.classList.contains('fa-stop')) {
            if (confirm(`确定要停止任务ID为${taskId}的实时采集任务吗？`)) {
              try {
                // 调用停止任务接口
                await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}/stop`, 'PUT');
                console.log('停止任务成功');
              } catch (error) {
                console.error('停止任务失败:', error);
              }
              // 保持原有逻辑
              btnIcon.classList.remove('fa-stop');
              btnIcon.classList.add('fa-play');
              this.setAttribute('style', btnStyle.replace('var(--warning-color)', 'var(--success-color)'));
              this.title = '启动任务';
              updateTaskStatus(taskId, '已停止', 'warning');
            }
          } else if (btnIcon.classList.contains('fa-play')) {
            try {
              // 调用启动任务接口
              await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}/start`, 'PUT');
              console.log('启动任务成功');
            } catch (error) {
              console.error('启动任务失败:', error);
            }
            // 保持原有逻辑
            btnIcon.classList.remove('fa-play');
            btnIcon.classList.add('fa-stop');
            this.setAttribute('style', btnStyle.replace('var(--success-color)', 'var(--warning-color)'));
            this.title = '停止任务';
            updateTaskStatus(taskId, '运行中', 'success');
          } else if (btnIcon.classList.contains('fa-redo')) {
            if (confirm(`确定要重启任务ID为${taskId}的实时采集任务吗？`)) {
              try {
                // 调用重启任务接口
                await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}/restart`, 'PUT');
                console.log('重启任务成功');
              } catch (error) {
                console.error('重启任务失败:', error);
              }
              // 保持原有逻辑
              alert(`任务ID为${taskId}的实时采集任务已重启！`);
              updateTaskStatus(taskId, '运行中', 'success');
            }
          } else if (btnIcon.classList.contains('fa-sign-in-alt')) {
            if (confirm(`确定要上线任务ID为${taskId}的实时采集任务吗？`)) {
              try {
                // 调用上线任务接口
                await apiRequest(`${API_BASE_URL}/realtime-tasks/${taskId}/online`, 'PUT');
                console.log('上线任务成功');
              } catch (error) {
                console.error('上线任务失败:', error);
              }
              // 保持原有逻辑
              btnIcon.classList.remove('fa-sign-in-alt');
              btnIcon.classList.add('fa-stop');
              this.setAttribute('style', btnStyle.replace('var(--primary-color)', 'var(--warning-color)'));
              this.title = '停止任务';
              updateTaskStatus(taskId, '运行中', 'success');
            }
          }
        });
      });

      // 查看监控按钮点击事件
      document.querySelectorAll('button[title="查看监控"]').forEach(btn => {
        btn.addEventListener('click', async function() {
          const row = this.closest('tr');
          const taskName = row.querySelector('td:first-child').textContent;
          
          try {
            // 调用查看监控接口
            await apiRequest(`${API_BASE_URL}/realtime-tasks/monitor`, 'POST', {
              taskName: taskName
            });
            console.log('查看监控成功');
          } catch (error) {
            console.error('查看监控失败:', error);
          }
          
          // 保持原有逻辑
          alert(`正在查看任务"${taskName}"的监控信息...`);
        });
      });

      // 下线任务按钮点击事件
      document.querySelectorAll('button[title="下线任务"]').forEach(btn => {
        btn.addEventListener('click', async function() {
          const row = this.closest('tr');
          const taskName = row.querySelector('td:first-child').textContent;
          
          if (confirm(`确定要下线任务"${taskName}"吗？`)) {
            try {
              // 调用下线任务接口
              await apiRequest(`${API_BASE_URL}/realtime-tasks/offline`, 'PUT', {
                taskName: taskName
              });
              console.log('下线任务成功');
            } catch (error) {
              console.error('下线任务失败:', error);
            }
            
            // 保持原有逻辑
            alert(`任务"${taskName}"已下线！`);
            updateTaskStatus(row.querySelector('.status-btn').getAttribute('data-task-id'), '已下线', 'offline');
          }
        });
      });
    }

    // 模拟加载任务数据
    function loadTaskData(taskId) {
      // 这里模拟从服务器加载任务数据
      // 在实际应用中，应该通过AJAX请求获取数据
      const taskName = document.querySelector(`.edit-btn[data-task-id="${taskId}"]`).closest('tr').querySelector('td:first-child').textContent;
      const dataSource = document.querySelector(`.edit-btn[data-task-id="${taskId}"]`).closest('tr').querySelector('td:nth-child(2)').textContent;
      const targetTable = document.querySelector(`.edit-btn[data-task-id="${taskId}"]`).closest('tr').querySelector('td:nth-child(3)').textContent;

      // 假设编辑表单存在这些字段
      if (document.getElementById('editTaskName')) document.getElementById('editTaskName').value = taskName;
      if (document.getElementById('editDataSource')) {
        const select = document.getElementById('editDataSource');
        for (let i = 0; i < select.options.length; i++) {
          if (select.options[i].text === dataSource) {
            select.selectedIndex = i;
            break;
          }
        }
      }
      if (document.getElementById('editTargetTable')) document.getElementById('editTargetTable').value = targetTable;
    }

    // 模拟删除任务
    function simulateDeleteTask(taskId) {
      // 这里模拟删除任务的API调用
      alert(`任务ID为${taskId}的实时采集任务已删除！`);
      // 实际应用中，应该刷新任务列表或移除对应的行
      const row = document.querySelector(`.delete-btn[data-task-id="${taskId}"]`).closest('tr');
      if (row) {
        row.style.backgroundColor = '#FFF2F0';
        setTimeout(() => {
          row.remove();
        }, 500);
      }
    }

    // 更新任务状态
    function updateTaskStatus(taskId, status, tagClass) {
      // 这里模拟更新任务状态的API调用
      const row = document.querySelector(`.status-btn[data-task-id="${taskId}"]`).closest('tr');
      if (row) {
        const statusCell = row.querySelector('td:nth-child(4) span');
        if (statusCell) {
          statusCell.textContent = status;
          statusCell.className = `tag tag-${tagClass}`;
        }
      }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      initOperationButtons();
    });

    // 查询任务列表
    async function searchTasks() {
      const searchInput = document.querySelector('.search-box input');
      const statusSelect = document.querySelector('select');
      const searchKeyword = searchInput.value.trim();
      const selectedStatus = statusSelect.value;
      
      try {
        const searchBtn = document.querySelector('button[onclick="searchTasks()"]');
        const originalText = searchBtn.innerHTML;
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        
        await apiRequest(`${API_BASE_URL}/realtime-tasks/search`, 'POST', {
          keyword: searchKeyword,
          status: selectedStatus
        });
        console.log('查询任务列表成功');
        showNotification('查询完成！', 'success');
        searchBtn.disabled = false;
        searchBtn.innerHTML = originalText;
        
      } catch (error) {
        console.error('查询任务列表失败:', error);
        showNotification('查询失败，请重试！', 'error');
        const searchBtn = document.querySelector('button[onclick="searchTasks()"]');
        searchBtn.disabled = false;
        searchBtn.innerHTML = '<i class="fas fa-search"></i> 查询';
      }
    }

    // 处理搜索框回车键事件
    function handleSearchKeyPress(event) {
      if (event.key === 'Enter') {
        searchTasks();
      }
    }
  </script>
  <script>
    // 新增实时任务表单提交
    document.getElementById('addRealtimeTaskForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('addRealtimeTaskForm')) {
        try {
          // 调用创建实时任务接口
          await apiRequest(`${API_BASE_URL}/realtime-tasks`, 'POST', {
            name: document.getElementById('taskName').value,
            dataSource: document.getElementById('dataSource').value,
            targetTable: document.getElementById('targetTable').value,
            consumerGroup: document.getElementById('consumerGroup').value,
            batchSize: document.getElementById('batchSize').value,
            parallelism: document.getElementById('parallelism').value,
            dataPreprocess: document.getElementById('dataPreprocess').value,
            securityLevel: document.getElementById('securityLevel').value,
            description: document.getElementById('taskDescription').value
          });
          console.log('创建实时任务成功');
        } catch (error) {
          console.error('创建实时任务失败:', error);
        }
        // 保持原有逻辑
        alert('实时采集任务创建成功并已启动！');
        document.getElementById('addRealtimeTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>