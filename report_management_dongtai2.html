<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=0.8">
  <title>数智化运营平台 - 动态报告生成管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #1890ff;
      --primary-dark: #096dd9;
      --primary-light: #e6f7ff;
      --secondary-color: #6c757d;
      --text-primary: #333333;
      --text-secondary: #6c757d;
      --bg-color: #f5f7fa;
      --card-bg: #ffffff;
      --border-color: #e8e8e8;
      --success-color: #52c41a;
      --danger-color: #ff4d4f;
      --warning-color: #faad14;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
      --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
      --radius: 6px;
      --radius-lg: 8px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    }

    body {
      background-color: var(--bg-color);
      color: var(--text-primary);
      line-height: 1.6;
    }

    .container {
      display: flex;
      min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
      width: 240px;
      background-color: white;
      color: black;
      padding: 20px 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      transition: var(--transition);
      box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
      border-right: 1px solid var(--border-color);
    }

    .sidebar-header {
      padding: 0 20px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-header h2 {
      font-size: 1.5rem;
      margin: 0;
      color: black;
      font-weight: 500;
    }

    .menu {
      list-style: none;
      padding: 20px 0;
    }

    .menu-item {
      margin-bottom: 5px;
    }

    .menu-item a {
      display: block;
      padding: 10px 20px;
      color: rgba(0, 0, 0, 0.7);
      text-decoration: none;
      transition: var(--transition);
      border-left: 3px solid transparent;
    }

    .menu-item a:hover,
    .menu-item a.active {
      background-color: rgba(0, 0, 0, 0.05);
      color: black;
      border-left-color: var(--primary-color);
    }

    /* 主内容区域样式 */
    .main-content {
      flex: 1;
      margin-left: 240px;
      padding: 24px;
      transition: var(--transition);
    }

    .header {
      background-color: #fff;
      padding: 16px 24px;
      box-shadow: var(--shadow-sm);
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: var(--radius-lg);
    }

    .header h1 {
      font-size: 1.8rem;
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    .user-info {
      display: flex;
      align-items: center;
    }

    .user-info span {
      margin-right: 12px;
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
    }

    /* 表格美化 */
    .table {
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
      width: 100%;
      border-collapse: collapse;
    }
    .table th {
      background-color: var(--primary-light);
      color: var(--primary-color);
      font-weight: 600;
      padding: 14px 16px;
      text-align: left;
      border-bottom: 2px solid var(--primary-color);
    }
    .table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }
    .table tr {
      transition: var(--transition);
    }
    .table tr:hover {
      background-color: rgba(24, 144, 255, 0.08);
      transform: translateY(-1px);
    }
    .table tr:nth-child(even) {
      background-color: #f9fcff;
    }
    .table tr:last-child td {
      border-bottom: none;
    }
    .table-container {
      overflow-x: auto;
      padding: 8px;
    }
    .table .action-buttons {
      display: flex;
      gap: 8px;
    }

    /* 表单美化 */
    .form-group input,
    .form-group select,
    .form-group textarea {
      transition: var(--transition);
      padding: 10px 14px;
      border-radius: var(--radius);
      border: 1px solid var(--border-color);
      background-color: white;
      width: 100%;
      font-size: 14px;
    }
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
      outline: none;
    }
    .form-group label {
      color: var(--text-primary);
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 24px;
    }

    /* 按钮美化 */
    .btn {
      padding: 8px 16px;
      border-radius: var(--radius);
      font-weight: 500;
      transition: var(--transition);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      border: none;
      cursor: pointer;
      outline: none;
      font-size: 14px;
    }
    .btn-primary {
      background-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    }
    .btn-primary:hover {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
    }
    .btn-primary:active {
      transform: translateY(0);
    }
    .btn-danger {
      background-color: var(--danger-color);
      color: white;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    }
    .btn-danger:hover {
      background-color: #e53935;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    }
    .btn-danger:active {
      transform: translateY(0);
    }
    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--border-color);
      color: var(--text-primary);
    }
    .btn-outline:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
      background-color: var(--primary-light);
      transform: translateY(-1px);
    }
    .btn + .btn {
      margin-left: 8px;
    }

    /* 模态框美化 */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
    }
    .modal.active {
      opacity: 1;
      visibility: visible;
    }
    .modal-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: -1;
      backdrop-filter: blur(2px);
    }
    .modal-container {
      background-color: white;
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      transform: translateY(-20px);
      transition: transform 0.3s ease;
      max-width: 90%;
      width: 800px;
      max-height: 90vh;
      overflow-y: auto;
    }
    .modal.active .modal-container {
      transform: translateY(0);
    }
    .modal-header {
      background-color: var(--primary-color);
      color: white;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .modal-title {
      font-size: 18px;
      font-weight: 600;
    }
    .modal-close {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      opacity: 0.8;
      transition: opacity 0.2s;
    }
    .modal-close:hover {
      opacity: 1;
    }
    .modal-body {
      padding: 24px;
    }
    .modal-footer {
      padding: 16px 24px;
      background-color: #f9f9f9;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      border-top: 1px solid var(--border-color);
    }

    /* 搜索区域美化 */
    .search-container {
      display: flex;
      max-width: 500px;
      width: 100%;
      box-shadow: var(--shadow-sm);
      border-radius: var(--radius);
      overflow: hidden;
    }
    .search-box {
      position: relative;
      display: flex;
      align-items: center;
      background-color: #fff;
      border: 1px solid var(--border-color);
      border-right: none;
      padding: 10px 16px;
      flex: 1;
    }
    .search-box-icon {
      color: var(--text-secondary);
      margin-right: 12px;
      font-size: 16px;
    }
    .search-box input {
      border: none;
      outline: none;
      flex: 1;
      padding: 0;
      font-size: 14px;
    }

    /* 面包屑美化 */
    .breadcrumb {
      display: flex;
      padding: 12px 16px;
      margin-bottom: 24px;
      list-style: none;
      background-color: var(--card-bg);
      border-radius: var(--radius);
      box-shadow: var(--shadow-sm);
      font-size: 14px;
    }
    .breadcrumb-item {
      margin-right: 10px;
      color: var(--text-secondary);
      position: relative;
    }
    .breadcrumb-item:not(:last-child)::after {
      content: '/';
      margin-left: 10px;
      color: var(--text-secondary);
    }
    .breadcrumb-item.active {
      color: var(--primary-color);
      font-weight: 500;
    }

    /* 卡片美化 */
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      overflow: hidden;
      margin-bottom: 24px;
    }
    .card:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    }
    .card-header {
      padding: 16px 24px;
      background-color: #f0f7ff;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
    }
    .card-body {
      padding: 24px;
    }

    /* 标签页样式 */
    .tabs {
      margin-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      overflow-x: auto;
      scrollbar-width: thin;
    }
    .tab {
      padding: 12px 24px;
      cursor: pointer;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      white-space: nowrap;
      font-weight: 500;
      color: var(--text-secondary);
    }
    .tab:hover {
      color: var(--primary-color);
      background-color: rgba(24, 144, 255, 0.05);
    }
    .tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    /* 功能模块样式 */
    .function-module {
      margin-bottom: 32px;
    }
    .function-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--primary-color);
      color: var(--text-primary);
    }
    .function-description {
      margin-bottom: 16px;
      color: var(--text-secondary);
    }
    .input-section,
    .result-section,
    .save-section,
    .notification-settings {
      margin-bottom: 24px;
    }
    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      color: var(--text-primary);
    }
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }
    .result-display {
      padding: 16px;
      background-color: #f9fcff;
      border-radius: 6px;
      border: 1px solid var(--border-color);
      max-height: 300px;
      overflow-y: auto;
    }
    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .loading-text {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: var(--text-secondary);
      padding: 24px 0;
    }
    .error-message {
      padding: 16px;
      background-color: #fff8f8;
      border-radius: 6px;
      border: 1px solid var(--danger-color);
      color: var(--danger-color);
    }

    /* 通知状态样式 */
    .status-delivered {
      color: var(--success-color);
      background-color: rgba(82, 196, 26, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
    }
    .status-failed {
      color: var(--danger-color);
      background-color: rgba(255, 77, 79, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
      .sidebar {
        width: 200px;
      }
      .main-content {
        margin-left: 200px;
      }
      .form-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }
    }
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        z-index: 999;
      }
      .sidebar.active {
        transform: translateX(0);
      }
      .main-content {
        margin-left: 0;
      }
      .menu-toggle {
        display: block;
      }
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营通报管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div> -->
    <!-- 新增通知中心菜单 -->
    <!-- <div class="menu-item">
      <i class="fas fa-bell menu-icon"></i>
      <span class="menu-text" onclick="switchToNotificationTab()">通知中心</span>
    </div>
  </div> -->

   <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;" data-href="report_management.html">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin_management.html">运营通报管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-generation.html">运营通报生成与审核</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->

  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      动态报告生成管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">动态报告生成管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="card" style="padding: 16px; margin-bottom: 24px;">
      <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: space-between;">
        <div style="display: flex; flex-wrap: wrap; gap: 16px; width: 100%;">
          <div class="search-container">
            <div class="search-box">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="搜索报告..." style="width: 100%;">
            </div>
            <button class="btn btn-primary"><i class="fas fa-search"></i> 搜索</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能模块区域 -->
    <div class="card">
      <div class="card-header">
        <div class="card-title">运营报告管理功能</div>
      </div>
      <div class="card-body">
        <!-- 标签页导航 -->
        <div class="tabs">
          <div class="tab active" data-tab="module1">章节目录生成</div>
          <div class="tab" data-tab="module2">章节描述生成</div>
          <div class="tab" data-tab="module3">章节小结生成</div>
          <div class="tab" data-tab="module4">整体概述生成</div>
          <div class="tab" data-tab="module5">整体总结生成</div>
          <div class="tab" data-tab="module6">报告全文生成</div>
          <!-- 新增通知中心标签页 -->
          <div class="tab" data-tab="notificationCenter">通知中心</div>
        </div>

        <!-- 功能模块1：运营报告章节目录生成 -->
        <div class="tab-content active" id="module1">
          <div class="function-module">
            <div class="function-title">运营报告章节目录生成</div>
           

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="templateCode">运营报告模板编码</label>
                  <input type="text" id="templateCode" placeholder="输入运营报告模板编码">
                </div>
                <div class="form-group">
                  <label for="templateType">模板类型</label>
                  <select id="templateType">
                    <option value="">请选择</option>
                    <option value="daily">日报</option>
                    <option value="weekly">周报</option>
                    <option value="monthly">月报</option>
                    <option value="quarterly">季报</option>
                    <option value="yearly">年报</option>
                  </select>
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateChapterCatalog()"><i class="fas fa-search"></i> 查询配置并生成目录</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="chapterCatalogResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"查询配置并生成目录"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients1">通知接收人</label>
                  <input type="text" id="notificationRecipients1" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod1" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod1" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="instanceId">运营报告实例ID</label>
                  <input type="text" id="instanceId" placeholder="输入运营报告实例ID">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveChapterCatalog()"><i class="fas fa-save"></i> 保存章节目录</button>
              </div>
              <div id="catalogSaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 功能模块2：运营报告章节描述归纳生成 -->
        <div class="tab-content" id="module2">
          <div class="function-module">
            <div class="function-title">运营报告章节描述归纳生成</div>
          

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="chapterTopic">章节主题</label>
                  <input type="text" id="chapterTopic" placeholder="输入章节主题">
                </div>
                <div class="form-group">
                  <label for="chapterData">相关数据</label>
                  <textarea id="chapterData" rows="4" placeholder="输入相关数据"></textarea>
                </div>
                <div class="form-group">
                  <label for="chapterStyle">描述风格</label>
                  <select id="chapterStyle">
                    <option value="">请选择</option>
                    <option value="formal">正式</option>
                    <option value="concise">简洁</option>
                    <option value="detailed">详细</option>
                    <option value="technical">技术</option>
                  </select>
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateChapterDescription()"><i class="fas fa-magic"></i> 生成章节描述</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="chapterDescriptionResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成章节描述"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients2">通知接收人</label>
                  <input type="text" id="notificationRecipients2" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod2" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod2" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="saveChapterName">章节名称</label>
                  <input type="text" id="saveChapterName" placeholder="输入章节名称">
                </div>
                <div class="form-group">
                  <label for="saveChapterDescription">章节描述</label>
                  <input type="text" id="saveChapterDescription" placeholder="输入章节描述">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveChapterDescription()"><i class="fas fa-save"></i> 保存章节描述</button>
              </div>
              <div id="descriptionSaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 功能模块3：运营报告章节小结归纳生成 -->
        <div class="tab-content" id="module3">
          <div class="function-module">
            <div class="function-title">运营报告章节小结归纳生成</div>
           

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="summaryChapterId">章节ID</label>
                  <input type="text" id="summaryChapterId" placeholder="输入章节ID">
                </div>
                <div class="form-group">
                  <label for="summaryChapterContent">章节内容</label>
                  <textarea id="summaryChapterContent" rows="4" placeholder="输入章节内容"></textarea>
                </div>
                <div class="form-group">
                  <label for="summaryKeyPoints">关键要点</label>
                  <textarea id="summaryKeyPoints" rows="2" placeholder="输入关键要点，用逗号分隔"></textarea>
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateChapterSummary()"><i class="fas fa-magic"></i> 生成章节小结</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="chapterSummaryResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成章节小结"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients3">通知接收人</label>
                  <input type="text" id="notificationRecipients3" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod3" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod3" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="saveSummaryName">小结名称</label>
                  <input type="text" id="saveSummaryName" placeholder="输入小结名称">
                </div>
                <div class="form-group">
                  <label for="saveSummaryDescription">小结描述</label>
                  <input type="text" id="saveSummaryDescription" placeholder="输入小结描述">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveChapterSummary()"><i class="fas fa-save"></i> 保存章节小结</button>
              </div>
              <div id="summarySaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 功能模块4：运营报告整体概述归纳生成 -->
        <div class="tab-content" id="module4">
          <div class="function-module">
            <div class="function-title">运营报告整体概述归纳生成</div>
          

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="overviewReportId">报告ID</label>
                  <input type="text" id="overviewReportId" placeholder="输入报告ID">
                </div>
                <div class="form-group">
                  <label for="overviewKeyChapters">关键章节</label>
                  <textarea id="overviewKeyChapters" rows="2" placeholder="输入关键章节ID，用逗号分隔"></textarea>
                </div>
                <div class="form-group">
                  <label for="overviewKeyMetrics">关键指标</label>
                  <textarea id="overviewKeyMetrics" rows="2" placeholder="输入关键指标，用逗号分隔"></textarea>
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateReportOverview()"><i class="fas fa-magic"></i> 生成报告概述</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="reportOverviewResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成报告概述"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients4">通知接收人</label>
                  <input type="text" id="notificationRecipients4" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod4" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod4" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="saveOverviewName">概述名称</label>
                  <input type="text" id="saveOverviewName" placeholder="输入概述名称">
                </div>
                <div class="form-group">
                  <label for="saveOverviewDescription">概述描述</label>
                  <input type="text" id="saveOverviewDescription" placeholder="输入概述描述">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveReportOverview()"><i class="fas fa-save"></i> 保存报告概述</button>
              </div>
              <div id="overviewSaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 功能模块5：运营报告整体总结归纳生成 -->
        <div class="tab-content" id="module5">
          <div class="function-module">
            <div class="function-title">运营报告整体总结归纳生成</div>
           

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="conclusionReportId">报告ID</label>
                  <input type="text" id="conclusionReportId" placeholder="输入报告ID">
                </div>
                <div class="form-group">
                  <label for="conclusionKeyFindings">关键发现</label>
                  <textarea id="conclusionKeyFindings" rows="3" placeholder="输入关键发现"></textarea>
                </div>
                <div class="form-group">
                  <label for="conclusionRecommendations">建议措施</label>
                  <textarea id="conclusionRecommendations" rows="3" placeholder="输入建议措施"></textarea>
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateReportConclusion()"><i class="fas fa-magic"></i> 生成报告总结</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="reportConclusionResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成报告总结"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients5">通知接收人</label>
                  <input type="text" id="notificationRecipients5" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod5" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod5" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="saveConclusionName">总结名称</label>
                  <input type="text" id="saveConclusionName" placeholder="输入总结名称">
                </div>
                <div class="form-group">
                  <label for="saveConclusionDescription">总结描述</label>
                  <input type="text" id="saveConclusionDescription" placeholder="输入总结描述">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveReportConclusion()"><i class="fas fa-save"></i> 保存报告总结</button>
              </div>
              <div id="conclusionSaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 功能模块6：运营报告全文生成 -->
        <div class="tab-content" id="module6">
          <div class="function-module">
            <div class="function-title">运营报告全文生成</div>
           

            <div class="input-section">
              <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="fullReportTemplate">报告模板</label>
                  <select id="fullReportTemplate">
                    <option value="">请选择</option>
                    <option value="daily">日报模板</option>
                    <option value="weekly">周报模板</option>
                    <option value="monthly">月报模板</option>
                    <option value="quarterly">季报模板</option>
                    <option value="yearly">年报模板</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="fullReportChapters">包含章节</label>
                  <textarea id="fullReportChapters" rows="3" placeholder="输入章节ID，用逗号分隔"></textarea>
                </div>
                <div class="form-group">
                  <label for="fullReportFormat">报告格式</label>
                  <select id="fullReportFormat">
                    <option value="">请选择</option>
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="html">HTML</option>
                    <option value="word">Word</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="fullReportTitle">报告标题</label>
                  <input type="text" id="fullReportTitle" placeholder="输入报告标题">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="generateFullReport()"><i class="fas fa-magic"></i> 生成报告全文</button>
              </div>
            </div>

            <div class="result-section">
              <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
              <div id="fullReportResult" class="result-display">
                <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成报告全文"按钮获取结果</div>
              </div>
            </div>

            <!-- 新增通知设置区域 -->
            <div class="notification-settings">
              <div class="section-title"><i class="fas fa-bell"></i> 通知设置</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="notificationRecipients6">通知接收人</label>
                  <input type="text" id="notificationRecipients6" placeholder="输入接收人邮箱，多个用逗号分隔">
                </div>
                <div class="form-group">
                  <label>通知方式</label>
                  <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod6" value="inApp" checked> 站内消息
                    </label>
                    <label style="display: flex; align-items: center; gap: 6px;">
                      <input type="checkbox" name="notificationMethod6" value="email"> 邮件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-section">
              <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
              <div class="form-grid">
                <div class="form-group">
                  <label for="saveFullReportId">报告ID</label>
                  <input type="text" id="saveFullReportId" placeholder="输入报告ID">
                </div>
                <div class="form-group">
                  <label for="saveFullReportName">报告名称</label>
                  <input type="text" id="saveFullReportName" placeholder="输入报告名称">
                </div>
              </div>
              <div style="margin-top: 16px;">
                <button class="btn btn-primary" onclick="saveFullReport()"><i class="fas fa-save"></i> 保存报告全文</button>
              </div>
              <div id="fullReportSaveMessage" style="margin-top: 16px;"></div>
            </div>
          </div>
        </div>

        <!-- 新增通知中心模块 -->
        <div class="tab-content" id="notificationCenter">
          <div class="function-module">
            <div class="function-title">通知中心</div>
    

            <div class="result-section">
              <div class="section-title"><i class="fas fa-history"></i> 通知记录</div>
              <div class="table-container">
                <table class="table">
                  <thead>
                    <tr>
                      <th>报告名称</th>
                      <th>生成时间</th>
                      <th>通知时间</th>
                      <th>通知方式</th>
                      <th>接收人</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="notificationTableBody">
                    <!-- 通知记录将通过JavaScript动态生成 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 存储通知记录
    let notificationRecords = [];
    
    // 页面加载完成后初始化
    window.onload = function() {
      initNotificationRecords();
      addApiCallToButtons();
    };

    // 为各个按钮添加API调用功能
    function addApiCallToButtons() {
      // 搜索按钮
      const searchBtn = document.querySelector('button[onclick="generateChapterCatalog()"]');
      if (searchBtn) {
        searchBtn.addEventListener('click', function() {
          // 调用查询配置并生成目录接口
          fetch('http://localhost:8000/api/report/dynamic/generate_catalog', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_chapter_catalog',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成章节目录接口调用完成');
          });
        });
      }

      // 保存章节目录按钮
      const saveCatalogBtn = document.querySelector('button[onclick="saveChapterCatalog()"]');
      if (saveCatalogBtn) {
        saveCatalogBtn.addEventListener('click', function() {
          // 调用保存章节目录接口
          fetch('http://localhost:8000/api/report/dynamic/save_catalog', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_chapter_catalog',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存章节目录接口调用完成');
          });
        });
      }

      // 生成章节描述按钮
      const generateDescBtn = document.querySelector('button[onclick="generateChapterDescription()"]');
      if (generateDescBtn) {
        generateDescBtn.addEventListener('click', function() {
          // 调用生成章节描述接口
          fetch('http://localhost:8000/api/report/dynamic/generate_description', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_chapter_description',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成章节描述接口调用完成');
          });
        });
      }

      // 保存章节描述按钮
      const saveDescBtn = document.querySelector('button[onclick="saveChapterDescription()"]');
      if (saveDescBtn) {
        saveDescBtn.addEventListener('click', function() {
          // 调用保存章节描述接口
          fetch('http://localhost:8000/api/report/dynamic/save_description', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_chapter_description',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存章节描述接口调用完成');
          });
        });
      }

      // 生成章节小结按钮
      const generateSummaryBtn = document.querySelector('button[onclick="generateChapterSummary()"]');
      if (generateSummaryBtn) {
        generateSummaryBtn.addEventListener('click', function() {
          // 调用生成章节小结接口
          fetch('http://localhost:8000/api/report/dynamic/generate_summary', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_chapter_summary',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成章节小结接口调用完成');
          });
        });
      }

      // 保存章节小结按钮
      const saveSummaryBtn = document.querySelector('button[onclick="saveChapterSummary()"]');
      if (saveSummaryBtn) {
        saveSummaryBtn.addEventListener('click', function() {
          // 调用保存章节小结接口
          fetch('http://localhost:8000/api/report/dynamic/save_summary', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_chapter_summary',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存章节小结接口调用完成');
          });
        });
      }

      // 生成报告概述按钮
      const generateOverviewBtn = document.querySelector('button[onclick="generateReportOverview()"]');
      if (generateOverviewBtn) {
        generateOverviewBtn.addEventListener('click', function() {
          // 调用生成报告概述接口
          fetch('http://localhost:8000/api/report/dynamic/generate_overview', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_report_overview',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成报告概述接口调用完成');
          });
        });
      }

      // 保存报告概述按钮
      const saveOverviewBtn = document.querySelector('button[onclick="saveReportOverview()"]');
      if (saveOverviewBtn) {
        saveOverviewBtn.addEventListener('click', function() {
          // 调用保存报告概述接口
          fetch('http://localhost:8000/api/report/dynamic/save_overview', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_report_overview',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存报告概述接口调用完成');
          });
        });
      }

      // 生成报告总结按钮
      const generateConclusionBtn = document.querySelector('button[onclick="generateReportConclusion()"]');
      if (generateConclusionBtn) {
        generateConclusionBtn.addEventListener('click', function() {
          // 调用生成报告总结接口
          fetch('http://localhost:8000/api/report/dynamic/generate_conclusion', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_report_conclusion',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成报告总结接口调用完成');
          });
        });
      }

      // 保存报告总结按钮
      const saveConclusionBtn = document.querySelector('button[onclick="saveReportConclusion()"]');
      if (saveConclusionBtn) {
        saveConclusionBtn.addEventListener('click', function() {
          // 调用保存报告总结接口
          fetch('http://localhost:8000/api/report/dynamic/save_conclusion', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_report_conclusion',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存报告总结接口调用完成');
          });
        });
      }

      // 生成报告全文按钮
      const generateFullReportBtn = document.querySelector('button[onclick="generateFullReport()"]');
      if (generateFullReportBtn) {
        generateFullReportBtn.addEventListener('click', function() {
          // 调用生成报告全文接口
          fetch('http://localhost:8000/api/report/dynamic/generate_full_report', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'generate_full_report',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('生成报告全文接口调用完成');
          });
        });
      }

      // 保存报告全文按钮
      const saveFullReportBtn = document.querySelector('button[onclick="saveFullReport()"]');
      if (saveFullReportBtn) {
        saveFullReportBtn.addEventListener('click', function() {
          // 调用保存报告全文接口
          fetch('http://localhost:8000/api/report/dynamic/save_full_report', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'save_full_report',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('保存报告全文接口调用完成');
          });
        });
      }
    }

    // 标签页切换功能
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有标签和内容的active类
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
        
        // 为当前点击的标签和对应内容添加active类
        tab.classList.add('active');
        const tabId = tab.getAttribute('data-tab');
        document.getElementById(tabId).classList.add('active');
      });
    });

    // 切换到通知中心标签页
    function switchToNotificationTab() {
      document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
      
      document.querySelector('.tab[data-tab="notificationCenter"]').classList.add('active');
      document.getElementById('notificationCenter').classList.add('active');
    }

    // 初始化通知记录（模拟一些数据）
    function initNotificationRecords() {
      notificationRecords = [
        {
          id: 1,
          reportName: "月度运营报告",
          reportId: "REP-20230501",
          generateTime: "2023-05-01 10:30:00",
          notificationTime: "2023-05-01 10:32:15",
          method: "站内消息,邮件",
          recipient: "<EMAIL>,<EMAIL>",
          status: "已送达"
        },
        {
          id: 2,
          reportName: "周度销售分析",
          reportId: "REP-20230428",
          generateTime: "2023-04-28 15:45:00",
          notificationTime: "2023-04-28 15:47:30",
          method: "邮件",
          recipient: "<EMAIL>",
          status: "未送达"
        },
        {
          id: 3,
          reportName: "用户增长分析报告",
          reportId: "REP-20230425",
          generateTime: "2023-04-25 09:15:00",
          notificationTime: "2023-04-25 09:17:22",
          method: "站内消息",
          recipient: "<EMAIL>",
          status: "已送达"
        }
      ];
      renderNotificationRecords();
    }

    // 渲染通知记录
    function renderNotificationRecords() {
      const tableBody = document.getElementById('notificationTableBody');
      tableBody.innerHTML = '';
      
      if (notificationRecords.length === 0) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="7" style="text-align: center; padding: 24px;">
              暂无通知记录
            </td>
          </tr>
        `;
        return;
      }
      
      notificationRecords.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><a href="javascript:previewReport('${record.reportId}')" style="color: var(--primary-color); text-decoration: underline;">${record.reportName}</a></td>
          <td>${record.generateTime}</td>
          <td>${record.notificationTime}</td>
          <td>${record.method}</td>
          <td>${record.recipient}</td>
          <td><span class="${record.status === '已送达' ? 'status-delivered' : 'status-failed'}">${record.status}</span></td>
          <td>
            ${record.status === '未送达' ? 
              `<button class="btn btn-outline" onclick="resendNotification(${record.id})"><i class="fas fa-redo"></i> 重新发送</button>` : 
              ''}
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 发送新通知
    function sendNotification(moduleId, reportName, reportId) {
      // 获取通知设置
      const recipients = document.getElementById(`notificationRecipients${moduleId}`).value;
      if (!recipients) {
        alert('请输入通知接收人');
        return false;
      }
      
      const methods = [];
      document.querySelectorAll(`input[name="notificationMethod${moduleId}"]:checked`).forEach(checkbox => {
        methods.push(checkbox.value === 'inApp' ? '站内消息' : '邮件');
      });
      
      if (methods.length === 0) {
        alert('请选择通知方式');
        return false;
      }
      
      // 模拟发送通知（80%概率成功）
      const now = new Date();
      const formattedTime = now.getFullYear() + '-' + 
                           String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(now.getDate()).padStart(2, '0') + ' ' + 
                           String(now.getHours()).padStart(2, '0') + ':' + 
                           String(now.getMinutes()).padStart(2, '0') + ':' + 
                           String(now.getSeconds()).padStart(2, '0');
      
      // 随机生成状态（模拟网络状况）
      const status = Math.random() > 0.2 ? '已送达' : '未送达';
      
      // 添加新通知记录
      const newRecord = {
        id: notificationRecords.length > 0 ? Math.max(...notificationRecords.map(r => r.id)) + 1 : 1,
        reportName: reportName,
        reportId: reportId,
        generateTime: formattedTime,
        notificationTime: formattedTime,
        method: methods.join(','),
        recipient: recipients,
        status: status
      };
      
      notificationRecords.unshift(newRecord); // 添加到开头
      renderNotificationRecords();
      
      return true;
    }

    // 重新发送通知
    function resendNotification(id) {
      const recordIndex = notificationRecords.findIndex(record => record.id === id);
      if (recordIndex !== -1) {
        // 模拟重新发送
        const now = new Date();
        const formattedTime = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
        
        // 提高重新发送的成功率
        const status = Math.random() > 0.1 ? '已送达' : '未送达';
        
        notificationRecords[recordIndex].notificationTime = formattedTime;
        notificationRecords[recordIndex].status = status;
        
        renderNotificationRecords();
        
        // 显示提示
        alert(`通知已重新发送，状态：${status}`);
      }
    }

    // 预览报告
    function previewReport(reportId) {
      // 实际应用中应跳转到相应的报告预览页面
      alert(`正在预览报告 ID: ${reportId}`);
      // 示例：window.location.href = `report-preview.html?id=${reportId}`;
    }

    // 原有功能函数，已添加通知功能
    function generateChapterCatalog() {
      document.getElementById('chapterCatalogResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成章节目录...</div>';
      
      // 模拟异步操作
      setTimeout(() => {
        const reportName = "章节目录 - " + document.getElementById('templateCode').value || "未命名章节目录";
        const reportId = "CAT-" + new Date().getTime();
        
        document.getElementById('chapterCatalogResult').innerHTML = `
          <pre>1. 执行摘要
2. 运营指标总览
  2.1 日/周/月关键指标
  2.2 同比/环比分析
  2.3 目标达成情况
3. 业务板块分析
  3.1 核心业务表现
  3.2 新兴业务进展
4. 用户分析
  4.1 用户增长情况
  4.2 用户行为分析
  4.3 用户画像洞察
5. 问题与挑战
6. 建议与措施
7. 附录</pre>
        `;
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(1, reportName, reportId);
        }, 1000);
      }, 1500);
    }

    function saveChapterCatalog() {
      const instanceId = document.getElementById('instanceId').value;
      if (!instanceId) {
        document.getElementById('catalogSaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入运营报告实例ID</div>';
        return;
      }
      
      document.getElementById('catalogSaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">章节目录已成功保存到实例ID: ' + instanceId + '</div>';
    }

    function generateChapterDescription() {
      document.getElementById('chapterDescriptionResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成章节描述...</div>';
      setTimeout(() => {
        const reportName = "章节描述 - " + document.getElementById('chapterTopic').value || "未命名章节描述";
        const reportId = "DESC-" + new Date().getTime();
        
        document.getElementById('chapterDescriptionResult').innerHTML = '<pre>本章主要分析了用户增长情况，通过对每日/每周/每月的新用户注册数据进行追踪，发现本月用户增长率达到15.3%，较上月提升了3.2个百分点。其中移动端用户占比68.5%，较上月增长5.7%，显示移动端渠道已成为主要用户来源。用户留存方面，7日留存率为42.1%，较行业平均水平高出8.3个百分点，表明产品体验得到用户认可。</pre>';
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(2, reportName, reportId);
        }, 1000);
      }, 1500);
    }

    function saveChapterDescription() {
      const name = document.getElementById('saveChapterName').value;
      if (!name) {
        document.getElementById('descriptionSaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入章节名称</div>';
        return;
      }
      document.getElementById('descriptionSaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">章节描述已成功保存: ' + name + '</div>';
    }

    function generateChapterSummary() {
      document.getElementById('chapterSummaryResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成章节小结...</div>';
      setTimeout(() => {
        const reportName = "章节小结 - " + (document.getElementById('summaryChapterId').value || "未命名章节小结");
        const reportId = "SUM-" + new Date().getTime();
        
        document.getElementById('chapterSummaryResult').innerHTML = '<pre>本章节小结：1. 用户增长势头良好，月增长率达15.3%；2. 移动端用户占比持续提升，已成为核心渠道；3. 用户留存率高于行业平均水平，产品体验获得认可；4. 新增用户主要来自华东地区，占比达38.7%。</pre>';
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(3, reportName, reportId);
        }, 1000);
      }, 1500);
    }

    function saveChapterSummary() {
      const name = document.getElementById('saveSummaryName').value;
      if (!name) {
        document.getElementById('summarySaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入小结名称</div>';
        return;
      }
      document.getElementById('summarySaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">章节小结已成功保存: ' + name + '</div>';
    }

    function generateReportOverview() {
      document.getElementById('reportOverviewResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成报告概述...</div>';
      setTimeout(() => {
        const reportName = "报告概述 - " + (document.getElementById('overviewReportId').value || "未命名报告概述");
        const reportId = "OVER-" + new Date().getTime();
        
        document.getElementById('reportOverviewResult').innerHTML = '<pre>本月运营报告概述：本月整体运营情况良好，各项核心指标均呈现增长态势。用户规模较上月增长15.3%，达到历史新高；营收同比增长23.7%，超额完成月度目标；用户活跃度提升8.2个百分点，产品粘性持续增强。各业务板块中，核心业务保持稳定增长，新兴业务表现突出，环比增长达42.5%。用户结构持续优化，高价值用户占比提升至27.3%。总体来看，本月运营成效显著，但仍需关注部分区域的用户留存问题。</pre>';
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(4, reportName, reportId);
        }, 1000);
      }, 1500);
    }

    function saveReportOverview() {
      const name = document.getElementById('saveOverviewName').value;
      if (!name) {
        document.getElementById('overviewSaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入概述名称</div>';
        return;
      }
      document.getElementById('overviewSaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">报告概述已成功保存: ' + name + '</div>';
    }

    function generateReportConclusion() {
      document.getElementById('reportConclusionResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成报告总结...</div>';
      setTimeout(() => {
        const reportName = "报告总结 - " + (document.getElementById('conclusionReportId').value || "未命名报告总结");
        const reportId = "CONC-" + new Date().getTime();
        
        document.getElementById('reportConclusionResult').innerHTML = '<pre>本月运营总结：1. 本月运营成果显著，用户规模、营收和活跃度均实现同比、环比双增长；2. 移动端渠道表现突出，贡献了68.5%的新增用户；3. 新兴业务成为增长亮点，环比增长42.5%；4. 高价值用户占比持续提升，达到27.3%。建议措施：1. 加大对华东地区市场的投入，进一步扩大用户规模；2. 优化移动端用户体验，提升用户留存率；3. 加快新兴业务的产品迭代，巩固增长势头；4. 针对低活跃度用户开展精准运营活动，提升整体活跃度。</pre>';
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(5, reportName, reportId);
        }, 1000);
      }, 1500);
    }

    function saveReportConclusion() {
      const name = document.getElementById('saveConclusionName').value;
      if (!name) {
        document.getElementById('conclusionSaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入总结名称</div>';
        return;
      }
      document.getElementById('conclusionSaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">报告总结已成功保存: ' + name + '</div>';
    }

    function generateFullReport() {
      document.getElementById('fullReportResult').innerHTML = '<div class="loading-text"><div class="loading"></div> 正在生成报告全文...</div>';
      setTimeout(() => {
        const reportTitle = document.getElementById('fullReportTitle').value || '未命名报告';
        const reportId = 'REP-' + new Date().getTime();
        
        document.getElementById('fullReportResult').innerHTML = `<pre>【${document.getElementById('fullReportTemplate').options[document.getElementById('fullReportTemplate').selectedIndex].text}】${reportTitle}\n\n1. 执行摘要\n本月整体运营情况良好，各项核心指标均呈现增长态势。用户规模较上月增长15.3%，达到历史新高；营收同比增长23.7%，超额完成月度目标；用户活跃度提升8.2个百分点，产品粘性持续增强。\n\n2. 运营指标总览\n2.1 日/周/月关键指标\n本月日均活跃用户数(DAU)为12.5万，较上月增长12.3%；周均活跃用户数(WAU)为38.7万，环比增长10.5%；月活跃用户数(MAU)达87.3万，创历史新高。\n\n(完整报告内容过长，此处省略其余章节)...</pre>`;
        
        // 生成完成后发送通知
        setTimeout(() => {
          sendNotification(6, reportTitle, reportId);
        }, 1000);
      }, 2000);
    }

    function saveFullReport() {
      const id = document.getElementById('saveFullReportId').value;
      if (!id) {
        document.getElementById('fullReportSaveMessage').innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 请输入报告ID</div>';
        return;
      }
      document.getElementById('fullReportSaveMessage').innerHTML = '<div style="color: var(--success-color); padding: 12px; background-color: rgba(82, 196, 26, 0.1); border-radius: var(--radius);">报告全文已成功保存，报告ID: ' + id + '</div>';
    }
  </script>
</body>
</html>
