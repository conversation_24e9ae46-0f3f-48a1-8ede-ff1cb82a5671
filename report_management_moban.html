<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=0.8" />
    <title>数智化运营平台 - 运营报告生成配置管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      /* 表格美化 */
      .table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        width: 100%;
        border-collapse: collapse;
      }
      .table th {
        background-color: #f0f7ff;
        color: var(--primary-color);
        font-weight: 600;
        padding: 14px 16px;
        text-align: left;
        border-bottom: 2px solid var(--primary-color);
      }
      .table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
      }
      .table tr {
        transition: all 0.3s ease;
      }
      .table tr:hover {
        background-color: rgba(24, 144, 255, 0.08);
        transform: translateY(-1px);
      }
      .table tr:nth-child(even) {
        background-color: #f9fcff;
      }
      .table tr:last-child td {
        border-bottom: none;
      }
      .table-container {
        overflow-x: auto;
        padding: 8px;
      }
      .table .action-buttons {
        display: flex;
        gap: 8px;
      }

      /* 表单美化 */
      .form-group input,
      .form-group select {
        transition: all 0.3s ease;
        padding: 10px 14px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        background-color: white;
      }
      .form-group input:focus,
      .form-group select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
      }
      .form-group label {
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: 8px;
        display: block;
      }

      /* 按钮美化 */
      .btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
      }
      .btn-primary {
        background-color: var(--primary-color);
        border: none;
        color: white;
      }
      .btn-primary:hover {
        background-color: var(--secondary-color);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      }
      .btn-danger {
        background-color: var(--danger-color);
        border: none;
        color: white;
      }
      .btn-danger:hover {
        background-color: #e53935;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      }
      .btn:hover {
        transform: translateY(-1px);
      }
      .btn:active {
        transform: translateY(0);
      }

      /* 模态框美化 */
      .modal-container {
        position: fixed;
        top: 50%;
        left: 50%;
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        transform: translate(-50%, -50%) scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
      }
      .modal.active .modal-container {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
      }
      .modal-header {
        background-color: white;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        background-color: var(--primary-color);
        color: white;
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .modal-title {
        font-size: 18px;
        font-weight: 600;
      }
      .modal-close {
        color: var(--text-secondary);
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.2s;
      }
      .modal-close:hover {
        opacity: 1;
      }
      .modal-body {
        padding: 24px;
        max-height: 70vh;
        overflow-y: auto;
      }
      .modal-footer {
        background-color: white;
        border-top: 1px solid var(--border-color);
        padding: 16px 24px;
        background-color: #f9f9f9;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }

      /* 搜索区域美化 */
      .search-box {
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      }
      .search-box input {
        padding: 10px 12px;
      }

      /* 面包屑美化 */
      .breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        font-size: 14px;
        color: var(--text-secondary);
      }
      .breadcrumb-item {
        display: flex;
        align-items: center;
      }
      .breadcrumb-item:not(:last-child)::after {
        content: '>';
        margin-left: 8px;
        color: var(--text-tertiary);
      }
      .breadcrumb-item.active {
        color: var(--primary-color);
        font-weight: 500;
      }

      /* 卡片美化 */
      .card {
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
      }
      .card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      }

      /* 分页美化 */
      .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 16px;
        margin-top: 16px;
        border-top: 1px solid var(--border-color);
      }
      .pagination {
        display: flex;
        gap: 4px;
      }
      .pagination-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
      }
      .pagination-btn:hover:not(.active):not(:disabled) {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
      .pagination-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }
      .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); font-weight: 500; cursor: pointer;">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营通报管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
    <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;" data-href="report_management.html">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin_management.html">运营通报管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-generation.html">运营通报生成与审核</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-file-alt page-title-icon"></i>
        运营报告生成配置管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">智能洞察分析</a></div>
        <div class="breadcrumb-item active">运营报告生成配置管理</div>
      </div>

      <!-- 搜索和操作栏 -->
      <div class="card" style="padding: 16px; margin-bottom: 24px">
        <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: space-between">
          <div style="display: flex; flex-wrap: wrap; gap: 16px; width: 100%; max-width: calc(100% - 160px)">
            <div class="search-box" style="flex: 1; min-width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="搜索配置..." style="width: 100%" />
            </div>
            <div style="display: flex; flex-wrap: wrap; gap: 16px; align-items: flex-end">
              <div style="display: flex; flex-direction: column">
                <label for="templateCode" style="margin-bottom: 8px; white-space: nowrap">模板编码:</label>
                <input type="text" id="templateCode" placeholder="输入模板编码" style="width: 160px" />
              </div>
              <div style="display: flex; flex-direction: column">
                <label for="configCode" style="margin-bottom: 8px; white-space: nowrap">配置编码:</label>
                <input type="text" id="configCode" placeholder="输入配置编码" style="width: 160px" />
              </div>
              <div style="display: flex; flex-direction: column">
                <label for="templateName" style="margin-bottom: 8px; white-space: nowrap">模板名称:</label>
                <input type="text" id="templateName" placeholder="输入模板名称" style="width: 160px" />
              </div>
              <div style="display: flex; gap: 8px; padding-bottom: 8px">
                <button class="btn btn-primary">
                  <i class="fas fa-search"></i>
                  查询
                </button>
                <button class="btn" style="border: 1px solid var(--border-color)">
                  <i class="fas fa-redo"></i>
                  重置
                </button>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end; align-items: flex-end; padding-bottom: 8px">
            <button class="btn btn-primary" data-modal-target="addConfigModal">
              <i class="fas fa-plus"></i>
              新增配置
            </button>
          </div>
        </div>
      </div>

      <!-- 配置列表表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>模板名称</th>
                <th>生成周期</th>
                <th>报告文件格式</th>
                <th>地域范围</th>
                <th>报告发布对象</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>用户分析模板</td>
                <td>日报</td>
                <td>PDF</td>
                <td>全国</td>
                <td>管理员、运营人员</td>
                <td>张三</td>
                <td>2023-07-14 16:30</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editConfigModal">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn" style="color: var(--danger-color); " data-modal-target="deleteConfigModal">
                    <i class="fas fa-trash"></i>
                    删除
                  </button>
                </td>
              </tr>
              <tr>
                <td>销售分析模板</td>
                <td>周报</td>
                <td>Excel</td>
                <td>华东地区</td>
                <td>销售经理、管理员</td>
                <td>李四</td>
                <td>2023-07-16 09:15</td>
                <td>
                  <button class="btn" style="color: var(--primary-color); " data-modal-target="editConfigModal">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfigModal">
                    <i class="fas fa-trash"></i>
                    删除
                  </button>
                </td>
              </tr>
              <tr>
                <td>月度分析模板</td>
                <td>月报</td>
                <td>HTML</td>
                <td>华北地区</td>
                <td>部门主管、管理员</td>
                <td>王五</td>
                <td>2023-07-01 14:45</td>
                <td>
                  <button class="btn" style="color: var(--primary-color)" data-modal-target="editConfigModal">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn" style="color: var(--danger-color)" data-modal-target="deleteConfigModal">
                    <i class="fas fa-trash"></i>
                    删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination-container">
          <div class="pagination-info">显示 1 至 3 条，共 3 条</div>
          <div class="pagination">
            <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
            <button class="pagination-btn active">1</button>
            <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增配置模态框 -->
    <div class="modal" id="addConfigModal">
      <div class="modal-overlay"></div>
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">新增运营报告生成配置</h3>
          <button class="modal-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body">
          <form id="addConfigForm">
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px">
              <div class="form-group">
                <label for="addTemplateCode">
                  运营报告模板编码
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="addTemplateCode" required placeholder="请输入模板编码" />
              </div>
              <div class="form-group">
                <label for="addConfigCode">
                  运营报告配置编码
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="addConfigCode" required placeholder="请输入配置编码" />
              </div>
              <div class="form-group">
                <label for="addGenerateCycle">
                  生成周期
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <select id="addGenerateCycle" required>
                  <option value="">请选择</option>
                  <option value="daily">日报</option>
                  <option value="weekly">周报</option>
                  <option value="monthly">月报</option>
                  <option value="quarterly">季报</option>
                  <option value="yearly">年报</option>
                </select>
              </div>
              <div class="form-group">
                <label for="addReportFormat">
                  报告文件格式
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <select id="addReportFormat" required>
                  <option value="">请选择</option>
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="html">HTML</option>
                  <option value="word">Word</option>
                </select>
              </div>
              <div class="form-group">
                <label for="addRegionScope">
                  地域范围
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="addRegionScope" required placeholder="请输入地域范围，如：全国、华东地区" />
              </div>
              <div class="form-group">
                <label for="addPublishTarget">
                  报告发布对象
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="addPublishTarget" required placeholder="请输入发布对象，如：管理员、运营人员" />
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" data-modal-close>取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('addConfigForm').submit()">
            <i class="fas fa-save"></i>
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑配置模态框 -->
    <div class="modal" id="editConfigModal">
      <div class="modal-overlay"></div>
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">修改运营报告生成配置</h3>
          <button class="modal-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body">
          <form id="editConfigForm">
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px">
              <div class="form-group">
                <label for="editConfigCode">运营报告生成配置编码</label>
                <input type="text" id="editConfigCode" disabled value="REP-CFG-20230714001" class="disabled-input" />
              </div>
              <div class="form-group">
                <label for="editTemplateCode">运营报告模板编码</label>
                <input type="text" id="editTemplateCode" disabled value="REP-TPL-001" class="disabled-input" />
              </div>
              <div class="form-group">
                <label for="editGenerateCycle">
                  生成周期
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <select id="editGenerateCycle" required>
                  <option value="daily">日报</option>
                  <option value="weekly" selected>周报</option>
                  <option value="monthly">月报</option>
                  <option value="quarterly">季报</option>
                  <option value="yearly">年报</option>
                </select>
              </div>
              <div class="form-group">
                <label for="editReportFormat">
                  报告文件格式
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <select id="editReportFormat" required>
                  <option value="pdf">PDF</option>
                  <option value="excel" selected>Excel</option>
                  <option value="html">HTML</option>
                  <option value="word">Word</option>
                </select>
              </div>
              <div class="form-group">
                <label for="editRegionScope">
                  地域范围
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="editRegionScope" required value="华东地区" />
              </div>
              <div class="form-group">
                <label for="editPublishTarget">
                  报告发布对象
                  <span style="color: var(--danger-color)">*</span>
                </label>
                <input type="text" id="editPublishTarget" required value="销售经理、管理员" />
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" data-modal-close>取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('editConfigForm').submit()">
            <i class="fas fa-edit"></i>
            修改
          </button>
        </div>
      </div>
    </div>

    <!-- 删除配置模态框 -->
    <div class="modal" id="deleteConfigModal">
      <div class="modal-overlay"></div>
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">删除运营报告生成配置</h3>
          <button class="modal-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body">
          <div style="display: flex; align-items: flex-start; gap: 16px">
            <div style="color: var(--danger-color); font-size: 24px"><i class="fas fa-exclamation-circle"></i></div>
            <div>
              <p style="font-weight: 500; margin-bottom: 8px">确定要删除此运营报告生成配置吗？</p>
              <p style="color: var(--text-secondary); margin-bottom: 16px">删除后将无法恢复，请谨慎操作。</p>
              <div style="padding: 16px; background-color: #fff8f8; border-radius: 6px; border-left: 4px solid var(--danger-color); margin-top: 16px">
                <div style="margin-bottom: 4px">
                  <strong>配置编码:</strong>
                  REP-CFG-20230714001
                </div>
                <div>
                  <strong>模板名称:</strong>
                  用户分析模板
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" data-modal-close>取消</button>
          <button class="btn btn-danger">
            <i class="fas fa-trash"></i>
            删除
          </button>
        </div>
      </div>
    </div>

    <style>
      .disabled-input {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
      }
    </style>

    <script src="js/common.js"></script>
    <script>
      // 模态框功能
      function openModal(modal) {
        if (modal == null) return;
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
      }

      function closeModal(modal) {
        if (modal == null) return;
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }

      document.addEventListener('DOMContentLoaded', function () {
        // 打开模态框
        document.querySelectorAll('[data-modal-target]').forEach(button => {
          button.addEventListener('click', () => {
            const modal = document.getElementById(button.getAttribute('data-modal-target'));
            openModal(modal);
          });
        });

        // 关闭模态框
        document.querySelectorAll('[data-modal-close]').forEach(button => {
          button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            closeModal(modal);
          });
        });

        // 点击模态框外部关闭
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
          overlay.addEventListener('click', () => {
            const modal = overlay.closest('.modal');
            closeModal(modal);
          });
        });

        // 关闭按钮
        document.querySelectorAll('.modal-close').forEach(button => {
          button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            closeModal(modal);
          });
        });

        // 表单提交
        document.getElementById('addConfigForm')?.addEventListener('submit', function (e) {
          e.preventDefault();
          // 调用新增配置接口
          fetch('http://localhost:8000/api/report/config/add', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'add_config',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('新增配置接口调用完成');
          });
          alert('新增配置成功！');
          closeModal(document.getElementById('addConfigModal'));
          // 这里可以添加表单提交逻辑
        });

        document.getElementById('editConfigForm')?.addEventListener('submit', function (e) {
          e.preventDefault();
          // 调用修改配置接口
          fetch('http://localhost:8000/api/report/config/edit', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'edit_config',
              timestamp: new Date().toISOString()
            })
          }).catch(error => {
            console.log('修改配置接口调用完成');
          });
          alert('修改配置成功！');
          closeModal(document.getElementById('editConfigModal'));
          // 这里可以添加表单提交逻辑
        });

        // 删除按钮
        document.querySelectorAll('.btn-danger').forEach(button => {
          button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            // 调用删除配置接口
            fetch('http://localhost:8000/api/report/config/delete', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'delete_config',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('删除配置接口调用完成');
            });
            alert('删除配置成功！');
            closeModal(modal);
            // 这里可以添加删除逻辑
          });
        });

        // 为其他按钮添加API调用
        addApiCallToButtons();
      });

      // 为各个按钮添加API调用功能
      function addApiCallToButtons() {
        // 搜索按钮
        const searchBtn = document.querySelector('button[onclick*="search"]');
        if (searchBtn) {
          searchBtn.addEventListener('click', function() {
            // 调用搜索配置接口
            fetch('http://localhost:8000/api/report/config/search', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'search_config',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('搜索配置接口调用完成');
            });
          });
        }

        // 重置按钮
        const resetBtn = document.querySelector('button[onclick*="reset"]');
        if (resetBtn) {
          resetBtn.addEventListener('click', function() {
            // 调用重置接口
            fetch('http://localhost:8000/api/report/config/reset', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'reset_config',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('重置配置接口调用完成');
            });
          });
        }

        // 编辑按钮
        const editBtns = document.querySelectorAll('button[data-modal-target="editConfigModal"]');
        editBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 调用编辑配置接口
            fetch('http://localhost:8000/api/report/config/edit_modal', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'edit_config_modal',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('编辑配置模态框接口调用完成');
            });
          });
        });

        // 删除按钮
        const deleteBtns = document.querySelectorAll('button[data-modal-target="deleteConfigModal"]');
        deleteBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 调用删除配置接口
            fetch('http://localhost:8000/api/report/config/delete_modal', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'delete_config_modal',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('删除配置模态框接口调用完成');
            });
          });
        });
      }
    </script>
  </body>
</html>
