<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=0.8">
  <title>数智化运营平台 - 运营指标管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #1890ff;
      --primary-dark: #096dd9;
      --primary-light: #e6f7ff;
      --secondary-color: #6c757d;
      --text-primary: #333333;
      --text-secondary: #6c757d;
      --bg-color: #f5f7fa;
      --card-bg: #ffffff;
      --border-color: #e8e8e8;
      --success-color: #52c41a;
      --danger-color: #ff4d4f;
      --warning-color: #faad14;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
      --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
      --radius: 6px;
      --radius-lg: 8px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    }

    body {
      background-color: var(--bg-color);
      color: var(--text-primary);
      line-height: 1.6;
    }

    .container {
      display: flex;
      min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
      width: 240px;
      background-color: white;
      padding: 20px 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      transition: var(--transition);
      box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
      border-right: 1px solid var(--border-color);
    }

    .sidebar-header {
      padding: 0 20px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-header h2 {
      font-size: 1.5rem;
      margin: 0;
      color: black;
      font-weight: 500;
    }

    .menu {
      list-style: none;
      padding: 20px 0;
    }

    .menu-item {
      margin-bottom: 5px;
    }

    .menu-item a {
      display: block;
      padding: 10px 20px;
      color: rgba(0, 0, 0, 0.7);
      text-decoration: none;
      transition: var(--transition);
      border-left: 3px solid transparent;
    }

    .menu-item a:hover,
    .menu-item a.active {
      background-color: rgba(0, 0, 0, 0.05);
      color: black;
      border-left-color: var(--primary-color);
    }

    /* 主内容区域样式 */
    .main-content {
      flex: 1;
      margin-left: 240px;
      padding: 24px;
      transition: var(--transition);
    }

    .header {
      background-color: #fff;
      padding: 16px 24px;
      box-shadow: var(--shadow-sm);
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: var(--radius-lg);
    }

    .header h1 {
      font-size: 1.8rem;
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    .user-info {
      display: flex;
      align-items: center;
    }

    .user-info span {
      margin-right: 12px;
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
    }

    /* 表格美化 */
    .table {
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
      width: 100%;
      border-collapse: collapse;
    }
    .table th {
      background-color: var(--primary-light);
      color: var(--primary-color);
      font-weight: 600;
      padding: 14px 16px;
      text-align: left;
      border-bottom: 2px solid var(--primary-color);
    }
    .table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color);
      vertical-align: middle;
    }
    .table tr {
      transition: var(--transition);
    }
    .table tr:hover {
      background-color: rgba(24, 144, 255, 0.08);
      transform: translateY(-1px);
    }
    .table tr:nth-child(even) {
      background-color: #f9fcff;
    }
    .table tr:last-child td {
      border-bottom: none;
    }
    .table-container {
      overflow-x: auto;
      padding: 8px;
    }
    .table .action-buttons {
      display: flex;
      gap: 8px;
    }

    /* 表单美化 */
    .form-group input,
    .form-group select,
    .form-group textarea {
      transition: var(--transition);
      padding: 10px 14px;
      border-radius: var(--radius);
      border: 1px solid var(--border-color);
      background-color: white;
      width: 100%;
      font-size: 14px;
    }
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
      outline: none;
    }
    .form-group label {
      color: var(--text-primary);
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 24px;
    }

    /* 按钮美化 */
    .btn {
      padding: 8px 16px;
      border-radius: var(--radius);
      font-weight: 500;
      transition: var(--transition);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      border: none;
      cursor: pointer;
      outline: none;
      font-size: 14px;
    }
    .btn-primary {
      background-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    }
    .btn-primary:hover {
      background-color: var(--primary-dark);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
    }
    .btn-primary:active {
      transform: translateY(0);
    }
    .btn-danger {
      background-color: var(--danger-color);
      color: white;
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
    }
    .btn-danger:hover {
      background-color: #e53935;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    }
    .btn-danger:active {
      transform: translateY(0);
    }
    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--border-color);
      color: var(--text-primary);
    }
    .btn-outline:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
      background-color: var(--primary-light);
      transform: translateY(-1px);
    }
    .btn + .btn {
      margin-left: 8px;
    }

    /* 模态框美化 */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
    }
    .modal.active {
      opacity: 1;
      visibility: visible;
    }
    .modal-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: -1;
      backdrop-filter: blur(2px);
    }
    .modal-container {
      background-color: white;
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      transform: translateY(-20px);
      transition: transform 0.3s ease;
      max-width: 90%;
      width: 800px;
      max-height: 90vh;
      overflow-y: auto;
    }
    .modal.active .modal-container {
      transform: translateY(0);
    }
    .modal-header {
      background-color: var(--primary-color);
      color: white;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .modal-title {
      font-size: 18px;
      font-weight: 600;
    }
    .modal-close {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      opacity: 0.8;
      transition: opacity 0.2s;
    }
    .modal-close:hover {
      opacity: 1;
    }
    .modal-body {
      padding: 24px;
    }
    .modal-footer {
      padding: 16px 24px;
      background-color: #f9f9f9;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      border-top: 1px solid var(--border-color);
    }

    /* 搜索区域美化 */
    .search-container {
      display: flex;
      max-width: 500px;
      width: 100%;
      box-shadow: var(--shadow-sm);
      border-radius: var(--radius);
      overflow: hidden;
    }
    .search-box {
      position: relative;
      display: flex;
      align-items: center;
      background-color: #fff;
      border: 1px solid var(--border-color);
      border-right: none;
      padding: 10px 16px;
      flex: 1;
    }
    .search-box-icon {
      color: var(--text-secondary);
      margin-right: 12px;
      font-size: 16px;
    }
    .search-box input {
      border: none;
      outline: none;
      flex: 1;
      padding: 0;
      font-size: 14px;
    }

    /* 面包屑美化 */
    .breadcrumb {
      display: flex;
      padding: 12px 16px;
      margin-bottom: 24px;
      list-style: none;
      background-color: var(--card-bg);
      border-radius: var(--radius);
      box-shadow: var(--shadow-sm);
      font-size: 14px;
    }
    .breadcrumb-item {
      margin-right: 10px;
      color: var(--text-secondary);
      position: relative;
    }
    .breadcrumb-item:not(:last-child)::after {
      content: '/';
      margin-left: 10px;
      color: var(--text-secondary);
    }
    .breadcrumb-item.active {
      color: var(--primary-color);
      font-weight: 500;
    }

    /* 功能模块样式 */
    .function-module {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px dashed var(--border-color);
      transition: var(--transition);
      background-color: var(--card-bg);
      border-radius: var(--radius-lg);
      padding: 24px;
      box-shadow: var(--shadow-sm);
      display: none;
      animation: fadeIn 0.3s ease;
    }
    .function-module.active {
      display: block;
    }
    .function-module:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .function-title {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 16px;
      color: var(--primary-color);
      padding-bottom: 8px;
      border-bottom: 2px solid var(--primary-color);
      display: inline-block;
    }
    .function-description {
      margin-bottom: 24px;
      padding: 16px;
      background-color: var(--primary-light);
      border-radius: var(--radius);
      line-height: 1.7;
    }
    .input-section,
    .result-section,
    .save-section {
      margin-bottom: 28px;
    }
    .section-title {
      font-size: 1rem;
      font-weight: 500;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      color: var(--text-primary);
      padding-left: 12px;
      border-left: 3px solid var(--primary-color);
    }
    .section-title i {
      margin-right: 12px;
      color: var(--primary-color);
      font-size: 16px;
    }
    .result-display {
      padding: 16px;
      background-color: #fafafa;
      border-radius: var(--radius);
      min-height: 120px;
      overflow-x: auto;
      border: 1px solid var(--border-color);
    }
    pre {
      white-space: pre-wrap;
      word-break: break-all;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 13px;
      line-height: 1.6;
      color: #333;
    }

    /* 加载状态样式 */
    .loading {
      width: 36px;
      height: 36px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-left-color: var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    .loading-text {
      text-align: center;
      padding: 24px 0;
      color: var(--text-secondary);
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* 错误信息样式 */
    .error-message {
      color: var(--danger-color);
      padding: 12px 16px;
      background-color: rgba(255, 77, 79, 0.1);
      border-radius: var(--radius);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      border-left: 4px solid var(--danger-color);
    }
    .error-message i {
      margin-right: 12px;
      font-size: 18px;
    }

    /* 选项卡样式 */
    .tabs {
      display: flex;
      flex-wrap: wrap;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 24px;
      overflow-x: auto;
      scrollbar-width: none;
    }
    .tabs::-webkit-scrollbar {
      display: none;
    }
    .tab {
      padding: 12px 24px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      background-color: transparent;
      transition: var(--transition);
      font-weight: 500;
      color: var(--text-secondary);
      white-space: nowrap;
    }
    .tab:hover {
      background-color: var(--primary-light);
      color: var(--primary-color);
    }
    .tab.active {
      border-color: var(--border-color);
      border-bottom: 3px solid var(--primary-color);
      background-color: white;
      color: var(--primary-color);
      border-top-left-radius: var(--radius);
      border-top-right-radius: var(--radius);
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
      .sidebar {
        width: 200px;
      }
      .main-content {
        margin-left: 200px;
      }
      .form-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }
    }
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        z-index: 999;
      }
      .sidebar.active {
        transform: translateX(0);
      }
      .main-content {
        margin-left: 0;
      }
      .menu-toggle {
        display: block;
      }
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      background-color: var(--primary-color);
      color: white;
      border-radius: var(--radius);
      box-shadow: var(--shadow-md);
      z-index: 1001;
      transition: all 0.3s ease;
    }
    .notification.fade {
      opacity: 0;
      transform: translateY(-20px);
    }

    /* 高亮样式示例 */
    .highlight-red { color: var(--danger-color); font-weight: bold; }
    .highlight-yellow { background-color: rgba(250, 173, 20, 0.2); padding: 2px 4px; }
    .highlight-bold { font-weight: bold; }
    .highlight-underline { text-decoration: underline; }
    .highlight-large { font-size: 1.2em; }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-secondary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-secondary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-secondary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer; font-weight: 500;">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营通报管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      运营指标管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营指标管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="card" style="padding: 16px; margin-bottom: 24px;">
      <div style="display: flex; flex-wrap: wrap; gap: 16px; justify-content: space-between;">
        <div style="display: flex; flex-wrap: wrap; gap: 16px; width: 100%;">
          <div class="search-container">
            <div class="search-box">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="搜索指标..." style="width: 100%;">
            </div>
            <button class="btn btn-primary"><i class="fas fa-search"></i> 搜索</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 选项卡 -->
    <div class="tabs">
      <button class="tab active" data-tab="module1"><i class="fas fa-search"></i> 指标查询</button>
      <button class="tab" data-tab="module2"><i class="fas fa-fill"></i> 指标填充</button>
      <button class="tab" data-tab="module3"><i class="fas fa-sync-alt"></i> 环比变化</button>
      <button class="tab" data-tab="module4"><i class="fas fa-exclamation-triangle"></i> 异常高亮</button>
      <button class="tab" data-tab="module5"><i class="fas fa-star"></i> 重点标注</button>
      <button class="tab" data-tab="module6"><i class="fas fa-project-diagram"></i> 构成因子</button>
      <button class="tab" data-tab="module7"><i class="fas fa-search-plus"></i> 根因归纳</button>
      <button class="tab" data-tab="module8"><i class="fas fa-chart-line"></i> 市场建议</button>
      <button class="tab" data-tab="module9"><i class="fas fa-users"></i> 产能建议</button>
      <button class="tab" data-tab="module10"><i class="fas fa-handshake"></i> 商机建议</button>
      <button class="tab" data-tab="module11"><i class="fas fa-clock"></i> 订单建议</button>
      <button class="tab" data-tab="module12"><i class="fas fa-tools"></i> 工具建议</button>
    </div>

    <!-- 功能模块区域 -->
    <div class="card">
      <div class="card-header">
        <div class="card-title">运营指标管理功能</div>
      </div>
      <div class="card-body">
        <!-- 功能模块1：运营指标自动查询 -->
        <div class="function-module active" id="module1">
          <div class="function-title">运营指标自动查询</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="templateCodeQuery">运营报告模板编码</label>
                <input type="text" id="templateCodeQuery" placeholder="输入运营报告模板编码">
              </div>
              <div class="form-group">
                <label for="indexNameQuery">指标名称</label>
                <input type="text" id="indexNameQuery" placeholder="输入指标名称">
              </div>
              <div class="form-group">
                <label for="indexCodeQuery">指标编码</label>
                <input type="text" id="indexCodeQuery" placeholder="输入指标编码">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="queryIndex()"><i class="fas fa-magic"></i> 查询指标</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="indexQueryResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"查询指标"按钮获取结果</div>
            </div>
          </div>
        </div>

        <!-- 功能模块2：运营报告指标自动填充 -->
        <div class="function-module" id="module2">
          <div class="function-title">运营报告指标自动填充</div>
         

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="indexNameFill">运营指标名称</label>
                <input type="text" id="indexNameFill" placeholder="输入运营指标名称">
              </div>
              <div class="form-group">
                <label for="indexValueFill">指标值</label>
                <input type="text" id="indexValueFill" placeholder="输入指标值">
              </div>
              <div class="form-group">
                <label for="reportFormatFill">运营报告内容格式</label>
                <select id="reportFormatFill">
                  <option value="">请选择</option>
                  <option value="text">文本格式</option>
                  <option value="html">HTML格式</option>
                  <option value="markdown">Markdown格式</option>
                </select>
              </div>
              <div class="form-group">
                <label for="paramsFill">参数</label>
                <textarea id="paramsFill" rows="3" placeholder="输入参数，JSON格式"></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="fillIndex()"><i class="fas fa-magic"></i> 填充指标</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="indexFillResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"填充指标"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveIndexName">运营指标名称</label>
                <input type="text" id="saveIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveReportFormat">报告格式</label>
                <input type="text" id="saveReportFormat" placeholder="自动填充，无需修改" readonly>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveFillResult()"><i class="fas fa-save"></i> 保存填充结果</button>
            </div>
            <div id="fillSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块3：运营报告指标环比变化自动填充 -->
        <div class="function-module" id="module3">
          <div class="function-title">运营报告指标环比变化自动填充</div>
        

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="环比IndexName">运营指标名称</label>
                <input type="text" id="环比IndexName" placeholder="输入运营指标名称">
              </div>
              <div class="form-group">
                <label for="环比IndexValue">指标值</label>
                <input type="text" id="环比IndexValue" placeholder="输入指标值">
              </div>
              <div class="form-group">
                <label for="generate环比">是否生成环比变化</label>
                <select id="generate环比">
                  <option value="">请选择</option>
                  <option value="yes">是</option>
                  <option value="no">否</option>
                </select>
              </div>
              <div class="form-group">
                <label for="环比ReportFormat">运营报告内容格式</label>
                <select id="环比ReportFormat">
                  <option value="">请选择</option>
                  <option value="text">文本格式</option>
                  <option value="html">HTML格式</option>
                  <option value="markdown">Markdown格式</option>
                </select>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="fill环比Change()"><i class="fas fa-magic"></i> 获取环比变化</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="环比ChangeResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"获取环比变化"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="save环比IndexName">运营指标名称</label>
                <input type="text" id="save环比IndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="save环比ReportFormat">报告格式</label>
                <input type="text" id="save环比ReportFormat" placeholder="自动填充，无需修改" readonly>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="save环比ChangeResult()"><i class="fas fa-save"></i> 保存环比变化结果</button>
            </div>
            <div id="环比SaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块4：异常高亮 -->
        <div class="function-module" id="module4">
          <div class="function-title">异常指标高亮处理</div>
         

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="anomalyIndexName">异常指标名称</label>
                <input type="text" id="anomalyIndexName" placeholder="输入异常指标名称">
              </div>
              <div class="form-group">
                <label for="anomalyIndexValue">指标值</label>
                <input type="text" id="anomalyIndexValue" placeholder="输入异常指标值">
              </div>
              <div class="form-group">
                <label for="highlightType">处理类型</label>
                <select id="highlightType">
                  <option value="">请选择</option>
                  <option value="red">转红字</option>
                  <option value="yellow">转黄底</option>
                  <option value="bold">加粗显示</option>
                  <option value="underline">下划线显示</option>
                </select>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="processHighlight()"><i class="fas fa-magic"></i> 执行高亮处理</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 高亮处理结果</div>
            <div id="highlightResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"执行高亮处理"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveAnomalyIndexName">异常指标名称</label>
                <input type="text" id="saveAnomalyIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveAnomalyIndexValue">指标值</label>
                <input type="text" id="saveAnomalyIndexValue" placeholder="自动填充，无需修改" readonly>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveHighlightResult()"><i class="fas fa-save"></i> 保存高亮结果</button>
            </div>
            <div id="highlightSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块5：重点标注 -->
        <div class="function-module" id="module5">
          <div class="function-title">重点指标标注处理</div>
         

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="keyIndexName">重点指标名称</label>
                <input type="text" id="keyIndexName" placeholder="输入重点指标名称">
              </div>
              <div class="form-group">
                <label for="keyIndexValue">指标值</label>
                <input type="text" id="keyIndexValue" placeholder="输入重点指标值">
              </div>
              <div class="form-group">
                <label for="markType">标注类型</label>
                <select id="markType">
                  <option value="">请选择</option>
                  <option value="bold">加粗显示</option>
                  <option value="large">加大字号</option>
                  <option value="underline">下划线显示</option>
                  <option value="highlight">高亮显示</option>
                </select>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="processMark()"><i class="fas fa-magic"></i> 执行标注处理</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="markResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"执行标注处理"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveKeyIndexName">重点指标名称</label>
                <input type="text" id="saveKeyIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveKeyIndexValue">指标值</label>
                <input type="text" id="saveKeyIndexValue" placeholder="自动填充，无需修改" readonly>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveMarkResult()"><i class="fas fa-save"></i> 保存标注结果</button>
            </div>
            <div id="markSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块6：构成因子 -->
        <div class="function-module" id="module6">
          <div class="function-title">异常指标构成因子分析</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="factorIndexName">异常指标名称</label>
                <input type="text" id="factorIndexName" placeholder="输入异常指标名称">
              </div>
              <div class="form-group">
                <label for="factorIndexCode">异常指标编码</label>
                <input type="text" id="factorIndexCode" placeholder="输入异常指标编码">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="queryFactors()"><i class="fas fa-magic"></i> 查询构成因子</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="factorResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"查询构成因子"按钮获取结果</div>
            </div>
          </div>
        </div>

        <!-- 功能模块7：根因归纳 -->
        <div class="function-module" id="module7">
          <div class="function-title">异常指标根因归纳分析</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="rootCauseIndexName">异常指标名称</label>
                <input type="text" id="rootCauseIndexName" placeholder="输入异常指标名称">
              </div>
              <div class="form-group">
                <label for="rootCauseIndexValue">异常指标值</label>
                <input type="text" id="rootCauseIndexValue" placeholder="输入异常指标值">
              </div>
              <div class="form-group">
                <label for="rootCauseFactors">指标构成因子</label>
                <input type="text" id="rootCauseFactors" placeholder="输入构成因子，用逗号分隔">
              </div>
              <div class="form-group">
                <label for="rootCauseFactorValues">构成因子指标值</label>
                <input type="text" id="rootCauseFactorValues" placeholder="输入对应因子值，用逗号分隔">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="analyzeRootCause()"><i class="fas fa-magic"></i> 归纳根因分析</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="rootCauseResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"归纳根因分析"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveRootCauseIndexName">异常指标名称</label>
                <input type="text" id="saveRootCauseIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveRootCauseContent">根因归纳内容</label>
                <textarea id="saveRootCauseContent" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveRootCauseResult()"><i class="fas fa-save"></i> 保存根因归纳结果</button>
            </div>
            <div id="rootCauseSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块8：市场建议 -->
        <div class="function-module" id="module8">
          <div class="function-title">市场占有指标提升建议</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="marketIndexName">市场占有类指标名称</label>
                <input type="text" id="marketIndexName" placeholder="输入市场占有类指标名称">
              </div>
              <div class="form-group">
                <label for="marketCurrentValue">当前指标值</label>
                <input type="text" id="marketCurrentValue" placeholder="输入当前市场占有率">
              </div>
              <div class="form-group">
                <label for="marketCompetitorValue">竞对占有值</label>
                <input type="text" id="marketCompetitorValue" placeholder="输入竞争对手市场占有率">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="generateMarketSuggestion()"><i class="fas fa-magic"></i> 生成提升建议</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="marketSuggestionResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成提升建议"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveMarketIndexName">市场占有类指标名称</label>
                <input type="text" id="saveMarketIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveMarketSuggestion">提升建议内容</label>
                <textarea id="saveMarketSuggestion" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveMarketSuggestion()"><i class="fas fa-magic"></i> 保存提升建议</button>
            </div>
            <div id="marketSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块9：产能建议 -->
        <div class="function-module" id="module9">
          <div class="function-title">人员产能指标提升建议</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="capacityIndexName">人员产能类指标名称</label>
                <input type="text" id="capacityIndexName" placeholder="输入人员产能类指标名称">
              </div>
              <div class="form-group">
                <label for="workItemType">工作事项类型</label>
                <input type="text" id="workItemType" placeholder="输入工作事项类型">
              </div>
              <div class="form-group">
                <label for="currentCapacity">当前产能</label>
                <input type="text" id="currentCapacity" placeholder="输入当前产能值">
              </div>
              <div class="form-group">
                <label for="historicalCapacity">历史产能</label>
                <input type="text" id="historicalCapacity" placeholder="输入历史产能值">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="generateCapacitySuggestion()"><i class="fas fa-magic"></i> 生成提升建议</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 生成结果</div>
            <div id="capacitySuggestionResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成提升建议"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveCapacityIndexName">人员产能类指标名称</label>
                <input type="text" id="saveCapacityIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveCapacitySuggestion">提升建议内容</label>
                <textarea id="saveCapacitySuggestion" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveCapacitySuggestion()"><i class="fas fa-magic"></i> 保存提升建议</button>
            </div>
            <div id="capacitySaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块10：商机建议 -->
        <div class="function-module" id="module10">
          <div class="function-title">商机转化指标提升建议</div>
          
          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="businessIndexName">商机转化类指标名称</label>
                <input type="text" id="businessIndexName" placeholder="输入商机转化类指标名称">
              </div>
              <div class="form-group">
                <label for="conversionRate">商机转化率</label>
                <input type="text" id="conversionRate" placeholder="输入商机转化率">
              </div>
              <div class="form-group">
                <label for="businessVolume">商机单量</label>
                <input type="text" id="businessVolume" placeholder="输入商机单量">
              </div>
              <div class="form-group">
                <label for="businessDuration">商机推进时长</label>
                <input type="text" id="businessDuration" placeholder="输入商机推进时长（天）">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="generateBusinessSuggestion()"><i class="fas fa-lightbulb"></i> 生成提升建议</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 商机转化提升建议</div>
            <div id="businessSuggestionResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成提升建议"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveBusinessIndexName">商机转化类指标名称</label>
                <input type="text" id="saveBusinessIndexName" placeholder="自动填充，无需修改" readonly>
              
              </div>
              <div class="form-group">
                <label for="saveBusinessSuggestion">提升建议内容</label>
                <textarea id="saveBusinessSuggestion" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveBusinessSuggestion()"><i class="fas fa-save"></i> 保存提升建议</button>
            </div>
            <div id="businessSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块11：订单建议 -->
        <div class="function-module" id="module11">
          <div class="function-title">订单时效指标提升建议</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="orderIndexName">订单时效类指标名称</label>
                <input type="text" id="orderIndexName" placeholder="输入订单时效类指标名称">
              </div>
              <div class="form-group">
                <label for="avgOrderTime">订单环节平均时效</label>
                <input type="text" id="avgOrderTime" placeholder="输入平均时效（小时）">
              </div>
              <div class="form-group">
                <label for="orderSLA">订单环节SLA值</label>
                <input type="text" id="orderSLA" placeholder="输入SLA标准值（小时）">
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="generateOrderSuggestion()"><i class="fas fa-lightbulb"></i> 生成提升建议</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 订单时效提升建议</div>
            <div id="orderSuggestionResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成提升建议"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveOrderIndexName">订单时效类指标名称</label>
                <input type="text" id="saveOrderIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveOrderSuggestion">提升建议内容</label>
                <textarea id="saveOrderSuggestion" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveOrderSuggestion()"><i class="fas fa-save"></i> 保存提升建议</button>
            </div>
            <div id="orderSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>

        <!-- 功能模块12：工具建议 -->
        <div class="function-module" id="module12">
          <div class="function-title">工具使用指标提升建议</div>
          

          <div class="input-section">
            <div class="section-title"><i class="fas fa-input"></i> 输入信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="toolIndexName">工具使用类指标名称</label>
                <input type="text" id="toolIndexName" placeholder="输入工具使用类指标名称">
              </div>
              <div class="form-group">
                <label for="toolName">工具名称</label>
                <input type="text" id="toolName" placeholder="输入工具名称">
              </div>
              <div class="form-group">
                <label for="toolUsageData">工具使用数据</label>
                <textarea id="toolUsageData" rows="3" placeholder="输入工具使用数据，如登录次数、活跃时长、驻留时间等"></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="generateToolSuggestion()"><i class="fas fa-lightbulb"></i> 生成提升建议</button>
            </div>
          </div>

          <div class="result-section">
            <div class="section-title"><i class="fas fa-clipboard-check"></i> 工具使用提升建议</div>
            <div id="toolSuggestionResult" class="result-display">
              <div style="text-align: center; color: var(--text-secondary); padding: 24px 0;">请点击"生成提升建议"按钮获取结果</div>
            </div>
          </div>

          <div class="save-section">
            <div class="section-title"><i class="fas fa-save"></i> 保存信息</div>
            <div class="form-grid">
              <div class="form-group">
                <label for="saveToolIndexName">工具使用类指标名称</label>
                <input type="text" id="saveToolIndexName" placeholder="自动填充，无需修改" readonly>
              </div>
              <div class="form-group">
                <label for="saveToolSuggestion">提升建议内容</label>
                <textarea id="saveToolSuggestion" rows="3" placeholder="自动填充，无需修改" readonly></textarea>
              </div>
            </div>
            <div style="margin-top: 16px;">
              <button class="btn btn-primary" onclick="saveToolSuggestion()"><i class="fas fa-save"></i> 保存提升建议</button>
            </div>
            <div id="toolSaveMessage" style="margin-top: 16px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 配置模态框 -->
  <div class="modal" id="configModal">
    <div class="modal-overlay"></div>
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">配置参数</h2>
        <button class="modal-close" onclick="closeModal('configModal')">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-grid">
          <div class="form-group">
            <label for="apiEndpoint">API 端点</label>
            <input type="text" id="apiEndpoint" value="https://api.example.com/v1/">
          </div>
          <div class="form-group">
            <label for="modelName">模型名称</label>
            <input type="text" id="modelName" value="gpt-4">
          </div>
          <div class="form-group">
            <label for="temperature">温度参数</label>
            <input type="number" id="temperature" step="0.1" min="0" max="1" value="0.7">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
        <button class="btn btn-outline" onclick="closeModal('configModal')">取消</button>
      </div>
    </div>
  </div>

  <script>
    // 统一模块管理
    const ModuleManager = {
      init() {
        this.loadConfig();
        this.initTabs();
        this.initConfigButton();
        this.addApiCallToButtons();
      },

      // 为各个按钮添加API调用功能
      addApiCallToButtons() {
        // 搜索按钮
        const searchBtn = document.querySelector('button[onclick="queryIndex()"]');
        if (searchBtn) {
          searchBtn.addEventListener('click', function() {
            // 调用指标查询接口
            fetch('http://localhost:8000/api/report/content/query', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'query_index',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('指标查询接口调用完成');
            });
          });
        }

        // 填充指标按钮
        const fillIndexBtn = document.querySelector('button[onclick="fillIndex()"]');
        if (fillIndexBtn) {
          fillIndexBtn.addEventListener('click', function() {
            // 调用指标填充接口
            fetch('http://localhost:8000/api/report/content/fill', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'fill_index',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('指标填充接口调用完成');
            });
          });
        }

        // 保存填充结果按钮
        const saveFillBtn = document.querySelector('button[onclick="saveFillResult()"]');
        if (saveFillBtn) {
          saveFillBtn.addEventListener('click', function() {
            // 调用保存填充结果接口
            fetch('http://localhost:8000/api/report/content/save_fill', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_fill_result',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存填充结果接口调用完成');
            });
          });
        }

        // 获取环比变化按钮
        const fill环比Btn = document.querySelector('button[onclick="fill环比Change()"]');
        if (fill环比Btn) {
          fill环比Btn.addEventListener('click', function() {
            // 调用环比变化接口
            fetch('http://localhost:8000/api/report/content/环比_change', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: '环比_change',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('环比变化接口调用完成');
            });
          });
        }

        // 保存环比变化结果按钮
        const save环比Btn = document.querySelector('button[onclick="save环比ChangeResult()"]');
        if (save环比Btn) {
          save环比Btn.addEventListener('click', function() {
            // 调用保存环比变化结果接口
            fetch('http://localhost:8000/api/report/content/save_环比', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_环比_result',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存环比变化结果接口调用完成');
            });
          });
        }

        // 执行高亮处理按钮
        const highlightBtn = document.querySelector('button[onclick="processHighlight()"]');
        if (highlightBtn) {
          highlightBtn.addEventListener('click', function() {
            // 调用高亮处理接口
            fetch('http://localhost:8000/api/report/content/highlight', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'process_highlight',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('高亮处理接口调用完成');
            });
          });
        }

        // 保存高亮结果按钮
        const saveHighlightBtn = document.querySelector('button[onclick="saveHighlightResult()"]');
        if (saveHighlightBtn) {
          saveHighlightBtn.addEventListener('click', function() {
            // 调用保存高亮结果接口
            fetch('http://localhost:8000/api/report/content/save_highlight', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_highlight_result',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存高亮结果接口调用完成');
            });
          });
        }

        // 执行标注处理按钮
        const markBtn = document.querySelector('button[onclick="processMark()"]');
        if (markBtn) {
          markBtn.addEventListener('click', function() {
            // 调用标注处理接口
            fetch('http://localhost:8000/api/report/content/mark', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'process_mark',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('标注处理接口调用完成');
            });
          });
        }

        // 保存标注结果按钮
        const saveMarkBtn = document.querySelector('button[onclick="saveMarkResult()"]');
        if (saveMarkBtn) {
          saveMarkBtn.addEventListener('click', function() {
            // 调用保存标注结果接口
            fetch('http://localhost:8000/api/report/content/save_mark', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_mark_result',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存标注结果接口调用完成');
            });
          });
        }

        // 查询构成因子按钮
        const factorsBtn = document.querySelector('button[onclick="queryFactors()"]');
        if (factorsBtn) {
          factorsBtn.addEventListener('click', function() {
            // 调用查询构成因子接口
            fetch('http://localhost:8000/api/report/content/factors', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'query_factors',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('查询构成因子接口调用完成');
            });
          });
        }

        // 归纳根因分析按钮
        const rootCauseBtn = document.querySelector('button[onclick="analyzeRootCause()"]');
        if (rootCauseBtn) {
          rootCauseBtn.addEventListener('click', function() {
            // 调用根因分析接口
            fetch('http://localhost:8000/api/report/content/root_cause', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'analyze_root_cause',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('根因分析接口调用完成');
            });
          });
        }

        // 保存根因归纳结果按钮
        const saveRootCauseBtn = document.querySelector('button[onclick="saveRootCauseResult()"]');
        if (saveRootCauseBtn) {
          saveRootCauseBtn.addEventListener('click', function() {
            // 调用保存根因归纳结果接口
            fetch('http://localhost:8000/api/report/content/save_root_cause', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_root_cause_result',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存根因归纳结果接口调用完成');
            });
          });
        }

        // 生成市场建议按钮
        const marketBtn = document.querySelector('button[onclick="generateMarketSuggestion()"]');
        if (marketBtn) {
          marketBtn.addEventListener('click', function() {
            // 调用生成市场建议接口
            fetch('http://localhost:8000/api/report/content/market_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'generate_market_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('生成市场建议接口调用完成');
            });
          });
        }

        // 保存市场建议按钮
        const saveMarketBtn = document.querySelector('button[onclick="saveMarketSuggestion()"]');
        if (saveMarketBtn) {
          saveMarketBtn.addEventListener('click', function() {
            // 调用保存市场建议接口
            fetch('http://localhost:8000/api/report/content/save_market_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_market_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存市场建议接口调用完成');
            });
          });
        }

        // 生成产能建议按钮
        const capacityBtn = document.querySelector('button[onclick="generateCapacitySuggestion()"]');
        if (capacityBtn) {
          capacityBtn.addEventListener('click', function() {
            // 调用生成产能建议接口
            fetch('http://localhost:8000/api/report/content/capacity_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'generate_capacity_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('生成产能建议接口调用完成');
            });
          });
        }

        // 保存产能建议按钮
        const saveCapacityBtn = document.querySelector('button[onclick="saveCapacitySuggestion()"]');
        if (saveCapacityBtn) {
          saveCapacityBtn.addEventListener('click', function() {
            // 调用保存产能建议接口
            fetch('http://localhost:8000/api/report/content/save_capacity_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_capacity_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存产能建议接口调用完成');
            });
          });
        }

        // 生成商机建议按钮
        const businessBtn = document.querySelector('button[onclick="generateBusinessSuggestion()"]');
        if (businessBtn) {
          businessBtn.addEventListener('click', function() {
            // 调用生成商机建议接口
            fetch('http://localhost:8000/api/report/content/business_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'generate_business_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('生成商机建议接口调用完成');
            });
          });
        }

        // 保存商机建议按钮
        const saveBusinessBtn = document.querySelector('button[onclick="saveBusinessSuggestion()"]');
        if (saveBusinessBtn) {
          saveBusinessBtn.addEventListener('click', function() {
            // 调用保存商机建议接口
            fetch('http://localhost:8000/api/report/content/save_business_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_business_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存商机建议接口调用完成');
            });
          });
        }

        // 生成订单建议按钮
        const orderBtn = document.querySelector('button[onclick="generateOrderSuggestion()"]');
        if (orderBtn) {
          orderBtn.addEventListener('click', function() {
            // 调用生成订单建议接口
            fetch('http://localhost:8000/api/report/content/order_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'generate_order_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('生成订单建议接口调用完成');
            });
          });
        }

        // 保存订单建议按钮
        const saveOrderBtn = document.querySelector('button[onclick="saveOrderSuggestion()"]');
        if (saveOrderBtn) {
          saveOrderBtn.addEventListener('click', function() {
            // 调用保存订单建议接口
            fetch('http://localhost:8000/api/report/content/save_order_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_order_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存订单建议接口调用完成');
            });
          });
        }

        // 生成工具建议按钮
        const toolBtn = document.querySelector('button[onclick="generateToolSuggestion()"]');
        if (toolBtn) {
          toolBtn.addEventListener('click', function() {
            // 调用生成工具建议接口
            fetch('http://localhost:8000/api/report/content/tool_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'generate_tool_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('生成工具建议接口调用完成');
            });
          });
        }

        // 保存工具建议按钮
        const saveToolBtn = document.querySelector('button[onclick="saveToolSuggestion()"]');
        if (saveToolBtn) {
          saveToolBtn.addEventListener('click', function() {
            // 调用保存工具建议接口
            fetch('http://localhost:8000/api/report/content/save_tool_suggestion', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_tool_suggestion',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存工具建议接口调用完成');
            });
          });
        }

        // 保存配置按钮
        const saveConfigBtn = document.querySelector('button[onclick="saveConfig()"]');
        if (saveConfigBtn) {
          saveConfigBtn.addEventListener('click', function() {
            // 调用保存配置接口
            fetch('http://localhost:8000/api/report/content/save_config', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'save_config',
                timestamp: new Date().toISOString()
              })
            }).catch(error => {
              console.log('保存配置接口调用完成');
            });
          });
        }
      },

      // 初始化选项卡切换
      initTabs() {
        const tabsContainer = document.querySelector('.tabs');
        tabsContainer.addEventListener('click', (e) => {
          const tab = e.target.closest('.tab');
          if (!tab) return;

          // 切换选项卡激活状态
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
          tab.classList.add('active');

          // 切换模块显示状态
          const tabId = tab.getAttribute('data-tab');
          document.querySelectorAll('.function-module').forEach(module => {
            module.classList.remove('active');
          });
          document.getElementById(tabId).classList.add('active');
        });

        // 初始化第一个模块
        document.querySelector('.tab.active')?.click();
      },

      // 添加配置按钮
      initConfigButton() {
        const configBtn = document.createElement('button');
        configBtn.id = 'configBtn';
        configBtn.className = 'btn btn-outline';
        configBtn.innerHTML = '<i class="fas fa-cog"></i> 配置';
        configBtn.onclick = () => this.openModal('configModal');
        
        const header = document.querySelector('.header');
        header?.appendChild(configBtn);
      },

      // 模态框控制
      openModal(modalId) {
        document.getElementById(modalId).classList.add('active');
      },

      closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
      },

      // 配置管理
      loadConfig() {
        const defaultConfig = {
          apiEndpoint: "https://api.example.com/v1/",
          modelName: "gpt-4",
          temperature: "0.7"
        };

        const savedConfig = {
          apiEndpoint: localStorage.getItem("apiEndpoint") || defaultConfig.apiEndpoint,
          modelName: localStorage.getItem("modelName") || defaultConfig.modelName,
          temperature: localStorage.getItem("temperature") || defaultConfig.temperature
        };

        Object.keys(savedConfig).forEach(key => {
          const el = document.getElementById(key);
          if (el) el.value = savedConfig[key];
        });

        return savedConfig;
      },

      saveConfig() {
        const config = {
          apiEndpoint: document.getElementById("apiEndpoint").value,
          modelName: document.getElementById("modelName").value,
          temperature: document.getElementById("temperature").value
        };

        Object.keys(config).forEach(key => {
          localStorage.setItem(key, config[key]);
        });

        this.showNotification("配置保存成功！");
        this.closeModal('configModal');
      },

      // 通知提示
      showNotification(message) {
        const notification = document.createElement("div");
        notification.className = "notification";
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.classList.add("fade");
          setTimeout(() => notification.remove(), 1000);
        }, 3000);
      },

      // 加载状态
      showLoading(elementId) {
        const element = document.getElementById(elementId);
        element.innerHTML = '<div class="loading"></div><div class="loading-text">加载中...</div>';
      },

      // 错误信息
      showError(elementId, errorMessage) {
        const element = document.getElementById(elementId);
        element.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-circle"></i> ${errorMessage}</div>`;
      }
    };

    // 报告引擎API
    const ReportEngine = {
      config: {},

      init() {
        this.config = ModuleManager.loadConfig();
      },

      updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
      },

      // 指标查询
      queryIndex(indexName) {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              indexName,
              value: (Math.random() * 100).toFixed(2),
              unit: "%",
              timestamp: new Date().toISOString()
            });
          }, 1500);
        });
      },

      // 指标填充
      fillIndex(indexName, indexValue, reportFormat, params) {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              let filledReportText = '';
              if (reportFormat === 'html') {
                filledReportText = `<div class="filled-report"><h3>${indexName}</h3><p>当前值: <strong>${indexValue}</strong></p></div>`;
              } else if (reportFormat === 'markdown') {
                filledReportText = `# ${indexName}\n\n当前值: **${indexValue}**`;
              } else {
                filledReportText = `${indexName}: ${indexValue}`;
              }

              resolve({
                success: true,
                data: { filledReportText },
                message: "指标填充成功"
              });
            } catch (error) {
              reject({ success: false, message: error.message || "指标填充失败" });
            }
          }, 1500);
        });
      },

      // 环比变化
      fill环比Change(indexName, indexValue, generate环比, reportFormat) {
        return new Promise((resolve) => {
          setTimeout(() => {
            if (generate环比 === 'no') {
              resolve({
                indexName,
                currentValue: parseFloat(indexValue),
                generate环比,
                reportFormat
              });
              return;
            }

            const lastPeriodValue = parseFloat(indexValue) * (1 - (Math.random() * 0.2 - 0.1));
            const changeValue = parseFloat(indexValue) - lastPeriodValue;
            const changePercentage = (changeValue / lastPeriodValue * 100).toFixed(2);

            resolve({
              indexName,
              currentValue: parseFloat(indexValue),
              lastPeriodValue: lastPeriodValue.toFixed(2),
              changePercentage,
              changeType: changeValue >= 0 ? 'increase' : 'decrease',
              reportFormat
            });
          }, 1500);
        });
      },

      // 异常高亮处理
      processHighlight(indexName, indexValue, highlightType) {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              // 根据选择的高亮类型，返回不同样式的HTML
              let highlightedHtml = '';
              let styleName = '';
              
              switch(highlightType) {
                case 'red':
                  highlightedHtml = `<span class="highlight-red">${indexValue}</span>`;
                  styleName = '转红字';
                  break;
                case 'yellow':
                  highlightedHtml = `<span class="highlight-yellow">${indexValue}</span>`;
                  styleName = '转黄底';
                  break;
                case 'bold':
                  highlightedHtml = `<span class="highlight-bold">${indexValue}</span>`;
                  styleName = '加粗显示';
                  break;
                case 'underline':
                  highlightedHtml = `<span class="highlight-underline">${indexValue}</span>`;
                  styleName = '下划线显示';
                  break;
                default:
                  throw new Error('无效的高亮类型');
              }
              
              resolve({
                success: true,
                data: {
                  indexName,
                  indexValue,
                  highlightedHtml,
                  styleName
                },
                message: "异常指标高亮处理成功"
              });
            } catch (error) {
              reject({ success: false, message: error.message || "异常指标高亮处理失败" });
            }
          }, 1000);
        });
      },

      // 重点标注处理
      processMark(indexName, indexValue, markType) {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              let markedHtml = '';
              let styleName = '';
              
              switch(markType) {
                case 'bold':
                  markedHtml = `<span class="highlight-bold">${indexValue}</span>`;
                  styleName = '加粗显示';
                  break;
                case 'large':
                  markedHtml = `<span class="highlight-large">${indexValue}</span>`;
                  styleName = '加大字号';
                  break;
                case 'underline':
                  markedHtml = `<span class="highlight-underline">${indexValue}</span>`;
                  styleName = '下划线显示';
                  break;
                case 'highlight':
                  markedHtml = `<span class="highlight-yellow">${indexValue}</span>`;
                  styleName = '高亮显示';
                  break;
                default:
                  throw new Error('无效的标注类型');
              }
              
              resolve({
                success: true,
                data: {
                  indexName,
                  indexValue,
                  markedHtml,
                  styleName
                },
                message: "重点指标标注处理成功"
              });
            } catch (error) {
              reject({ success: false, message: error.message || "重点指标标注处理失败" });
            }
          }, 1000);
        });
      },

      // 查询构成因子
      queryFactors(indexName, indexCode) {
        return new Promise((resolve) => {
          setTimeout(() => {
            // 模拟生成构成因子数据
            const factors = [
              { name: "因子A", code: "FA001", weight: (Math.random() * 40 + 10).toFixed(1) },
              { name: "因子B", code: "FA002", weight: (Math.random() * 30 + 5).toFixed(1) },
              { name: "因子C", code: "FA003", weight: (Math.random() * 20 + 5).toFixed(1) },
              { name: "因子D", code: "FA004", weight: (Math.random() * 10 + 1).toFixed(1) }
            ];
            
            // 确保权重总和为100%
            const total = factors.reduce((sum, f) => sum + parseFloat(f.weight), 0);
            factors.forEach(f => {
              f.weight = ((parseFloat(f.weight) / total) * 100).toFixed(1);
            });

            resolve({
              indexName,
              indexCode,
              factors,
              timestamp: new Date().toISOString()
            });
          }, 1500);
        });
      },

      // 根因归纳分析
      analyzeRootCause(indexName, indexValue, factors, factorValues) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              indexValue,
              factors: factors.split(','),
              factorValues: factorValues.split(','),
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的根因分析结果
            const rootCauseText = `根据分析，${indexName}异常（当前值：${indexValue}）的主要原因包括：
1. 关键因子${factors.split(',')[0]}表现不佳，可能影响整体指标
2. 外部环境变化导致${factors.split(',')[1]}波动较大
3. 内部流程优化不足，造成${factors.split(',')[2]}效率低下
4. 资源配置不合理，影响了${factors.split(',')[3]}的表现

建议优先针对${factors.split(',')[0]}和${factors.split(',')[1]}进行优化调整。`;
            
            const responseData = {
              indexName,
              rootCause: rootCauseText,
              confidence: (Math.random() * 20 + 80).toFixed(1),
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              rootCauseText
            });
          }, 2000);
        });
      },

      // 生成市场占有提升建议
      generateMarketSuggestion(indexName, currentValue, competitorValue) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              currentValue,
              competitorValue,
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的建议
            const suggestionText = `针对${indexName}（当前值：${currentValue}%，竞对值：${competitorValue}%）的提升建议：
1. 加强市场推广力度，增加品牌曝光度
2. 优化产品定价策略，提高性价比竞争力
3. 拓展销售渠道，覆盖更多潜在客户群体
4. 提升客户服务质量，增强客户粘性
5. 针对竞争对手弱点制定差异化竞争策略

预计实施后可提升${(Math.random() * 5 + 2).toFixed(1)}%的市场占有率。`;
            
            const responseData = {
              indexName,
              suggestion: suggestionText,
              expectedImprovement: `${(Math.random() * 5 + 2).toFixed(1)}%`,
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              suggestionText
            });
          }, 2000);
        });
      },

      // 生成产能提升建议
      generateCapacitySuggestion(indexName, workType, current, historical) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              workType,
              currentCapacity: current,
              historicalCapacity: historical,
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的建议
            const suggestionText = `针对${workType}的${indexName}（当前：${current}，历史：${historical}）的提升建议：
1. 优化工作流程，减少不必要的环节
2. 引入自动化工具，提高工作效率
3. 加强员工培训，提升专业技能水平
4. 合理安排工作负荷，避免资源浪费
5. 建立绩效激励机制，提高工作积极性

预计通过上述措施可提升${(Math.random() * 15 + 5).toFixed(1)}%的产能。`;
            
            const responseData = {
              indexName,
              workType,
              suggestion: suggestionText,
              expectedImprovement: `${(Math.random() * 15 + 5).toFixed(1)}%`,
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              suggestionText
            });
          }, 2000);
        });
      },

      // 生成商机转化提升建议
      generateBusinessSuggestion(indexName, conversionRate, volume, duration) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              conversionRate,
              businessVolume: volume,
              duration,
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的建议
            const suggestionText = `针对${indexName}（转化率：${conversionRate}%，单量：${volume}，时长：${duration}天）的提升建议：
1. 优化商机筛选机制，聚焦高质量潜在客户
2. 加强销售人员培训，提升谈判技巧
3. 建立标准化跟进流程，缩短转化周期
4. 提供个性化解决方案，提高客户满意度
5. 完善激励机制，提高销售人员积极性

预计实施后可将转化率提升${(Math.random() * 8 + 2).toFixed(1)}%，缩短转化周期${(Math.random() * 3 + 1).toFixed(1)}天。`;
            
            const responseData = {
              indexName,
              suggestion: suggestionText,
              expectedConversionImprovement: `${(Math.random() * 8 + 2).toFixed(1)}%`,
              expectedDurationReduction: `${(Math.random() * 3 + 1).toFixed(1)}天`,
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              suggestionText
            });
          }, 2000);
        });
      },

      // 生成订单时效提升建议
      generateOrderSuggestion(indexName, avgTime, sla) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              avgOrderTime: avgTime,
              orderSLA: sla,
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的建议
            const suggestionText = `针对${indexName}（平均时效：${avgTime}小时，SLA：${sla}小时）的提升建议：
1. 优化订单处理流程，消除瓶颈环节
2. 引入自动化处理系统，提高处理效率
3. 合理分配人力资源，确保高峰期处理能力
4. 建立预警机制，及时发现并处理超时订单
5. 加强各环节协同，减少沟通成本

预计实施后可将平均时效缩短至${(Math.random() * (parseFloat(avgTime) - 1) + 1).toFixed(1)}小时，达到SLA标准。`;
            
            const responseData = {
              indexName,
              suggestion: suggestionText,
              expectedTimeReduction: `${(Math.random() * 5 + 1).toFixed(1)}小时`,
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              suggestionText
            });
          }, 2000);
        });
      },

      // 生成工具使用提升建议
      generateToolSuggestion(indexName, toolName, usageData) {
        return new Promise((resolve) => {
          setTimeout(() => {
            const requestData = {
              indexName,
              toolName,
              usageData,
              timestamp: new Date().toISOString()
            };
            
            // 模拟大模型返回的建议
            const suggestionText = `针对${toolName}的${indexName}的提升建议：
1. 开展工具使用培训，提高员工操作熟练度
2. 优化工具界面设计，提升用户体验
3. 增加常用功能快捷键，提高操作效率
4. 建立工具使用激励机制，鼓励员工积极使用
5. 根据反馈持续优化工具功能，满足实际需求

预计实施后可提升${(Math.random() * 20 + 10).toFixed(1)}%的工具使用效率和满意度。`;
            
            const responseData = {
              indexName,
              toolName,
              suggestion: suggestionText,
              expectedImprovement: `${(Math.random() * 20 + 10).toFixed(1)}%`,
              timestamp: new Date().toISOString()
            };

            resolve({
              request: JSON.stringify(requestData, null, 2),
              response: JSON.stringify(responseData, null, 2),
              suggestionText
            });
          }, 2000);
        });
      }
    };

    // 页面功能函数
    async function queryIndex() {
      const indexName = document.getElementById('indexNameQuery').value;
      const resultEl = document.getElementById('indexQueryResult');
      
      if (!indexName) {
        ModuleManager.showError('indexQueryResult', '请输入指标名称');
        return;
      }

      ModuleManager.showLoading('indexQueryResult');
      try {
        const result = await ReportEngine.queryIndex(indexName);
        resultEl.innerHTML = `
          <pre>
指标名称: ${result.indexName}
指标值: ${result.value}${result.unit}
查询时间: ${new Date(result.timestamp).toLocaleString()}
          </pre>
        `;
      } catch (error) {
        ModuleManager.showError('indexQueryResult', error.message || '查询失败');
      }
    }

    async function fillIndex() {
      const indexName = document.getElementById('indexNameFill').value;
      const indexValue = document.getElementById('indexValueFill').value;
      const reportFormat = document.getElementById('reportFormatFill').value;
      const resultEl = document.getElementById('indexFillResult');
      
      if (!indexName || !indexValue || !reportFormat) {
        ModuleManager.showError('indexFillResult', '请完善输入信息');
        return;
      }

      ModuleManager.showLoading('indexFillResult');
      try {
        const result = await ReportEngine.fillIndex(indexName, indexValue, reportFormat);
        resultEl.innerHTML = `<pre>${result.data.filledReportText}</pre>`;
        
        // 填充保存表单
        document.getElementById('saveIndexName').value = indexName;
        document.getElementById('saveReportFormat').value = reportFormat;
      } catch (error) {
        ModuleManager.showError('indexFillResult', error.message || '填充失败');
      }
    }

    function saveFillResult() {
      const indexName = document.getElementById('saveIndexName').value;
      const messageEl = document.getElementById('fillSaveMessage');
      
      if (!indexName) {
        messageEl.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 没有可保存的内容</div>';
        return;
      }

      messageEl.innerHTML = '<div style="color: var(--success-color);"><i class="fas fa-check-circle"></i> 填充结果已保存</div>';
    }

    async function fill环比Change() {
      const indexName = document.getElementById('环比IndexName').value;
      const indexValue = document.getElementById('环比IndexValue').value;
      const generate环比 = document.getElementById('generate环比').value;
      const reportFormat = document.getElementById('环比ReportFormat').value;
      const resultEl = document.getElementById('环比ChangeResult');
      
      if (!indexName || !indexValue || !generate环比 || !reportFormat) {
        ModuleManager.showError('环比ChangeResult', '请完善输入信息');
        return;
      }

      ModuleManager.showLoading('环比ChangeResult');
      try {
        const result = await ReportEngine.fill环比Change(indexName, indexValue, generate环比, reportFormat);
        
        let resultHtml = `<pre>指标名称: ${result.indexName}\n当期值: ${result.currentValue}`;
        if (generate环比 === 'yes') {
          resultHtml += `\n上期值: ${result.lastPeriodValue}
变化率: ${result.changePercentage}% (${result.changePercentage >= 0 ? '上升' : '下降'})`;
        }
        resultHtml += `\n格式: ${result.reportFormat}</pre>`;
        
        resultEl.innerHTML = resultHtml;
        
        // 填充保存表单
        document.getElementById('save环比IndexName').value = indexName;
        document.getElementById('save环比ReportFormat').value = reportFormat;
      } catch (error) {
        ModuleManager.showError('环比ChangeResult', error.message || '获取失败');
      }
    }

    function save环比ChangeResult() {
      const indexName = document.getElementById('save环比IndexName').value;
      const messageEl = document.getElementById('环比SaveMessage');
      
      if (!indexName) {
        messageEl.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> 没有可保存的内容</div>';
        return;
      }

      messageEl.innerHTML = '<div style="color: var(--success-color);"><i class="fas fa-check-circle"></i> 环比变化结果已保存</div>';
    }

    // 异常高亮处理函数
    async function processHighlight() {
      const indexName = document.getElementById('anomalyIndexName').value;
      const indexValue = document.getElementById('anomalyIndexValue').value;
      const highlightType = document.getElementById('highlightType').value;
      const resultEl = document.getElementById('highlightResult');
      
      if (!indexName || !indexValue || !highlightType) {
        ModuleManager.showError('highlightResult', '请完善输入信息');
        return;
      }

      ModuleManager.showLoading('highlightResult');
      try {
        const result = await ReportEngine.processHighlight(indexName, indexValue, highlightType);
        resultEl.innerHTML = `
          <div>
            <p><strong>指标名称:</strong> ${result.data.indexName}</p>
            <p><strong>原始值:</strong> ${result.data.indexValue}</p>
            <p><strong>高亮处理后:</strong> ${result.data.highlightedHtml}</p>
            <p><strong>处理类型:</strong> ${result.data.styleName}</p>
          </div>
        `;
        
        // 填充保存表单
        document.getElementById('saveAnomalyIndexName').value = indexName;
        document.getElementById('saveAnomalyIndexValue').value = indexValue;
      } catch (error) {
        ModuleManager.showError('highlightResult', error.message || '高亮处理失败');
      }
    }

    // 保存高亮处理结果
    function saveHighlightResult() {
      const indexName = document.getElementById('saveAnomalyIndexName').value;
      const messageEl = document.getElementById('highlightSaveMessage');
      
      if (!indexName) {
        ModuleManager.showError('highlightSaveMessage', '没有可保存的内容');
        return;
      }

      // 模拟保存到本地存储
      const savedData = {
        indexName: indexName,
        indexValue: document.getElementById('saveAnomalyIndexValue').value,
        timestamp: new Date().toISOString()
      };
      
      localStorage.setItem('highlightedAnomaly_' + Date.now(), JSON.stringify(savedData));
      
      ModuleManager.showNotification('高亮处理结果已保存');
      messageEl.innerHTML = '<div style="color: var(--success-color);"><i class="fas fa-check-circle"></i> 高亮处理结果已保存</div>';
    }

  // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
      ModuleManager.init();
      ReportEngine.init();
    });
  </script>
</body>
</html>