<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 任务调度工作台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">任务调度</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">五级穿透</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-calendar-alt page-title-icon"></i>
      任务调度工作台
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item active">任务调度</div>
    </div>

    <!-- 数据概览卡片 -->
    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
      <div class="stat-card">
        <div class="stat-card-title">待处理任务</div>
        <div class="stat-card-value">24</div>
        <div class="stat-card-extra"><span class="tag tag-warning">+8 较昨日</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title">处理中任务</div>
        <div class="stat-card-value">16</div>
        <div class="stat-card-extra"><span class="tag tag-info">-2 较昨日</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title">已完成任务</div>
        <div class="stat-card-value">138</div>
        <div class="stat-card-extra"><span class="tag tag-success">+24 较昨日</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title">失败任务</div>
        <div class="stat-card-value">5</div>
        <div class="stat-card-extra"><span class="tag tag-danger">+3 较昨日</span></div>
      </div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex; width: 60%;">
        <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" placeholder="搜索任务...">
        </div>
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
          </select>
        </div>
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部优先级</option>
            <option value="high">高</option>
            <option value="medium">中</option>
            <option value="low">低</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 高级筛选</button>
        <button class="btn btn-primary" data-modal-target="addTaskModal"><i class="fas fa-plus"></i> 新增任务</button>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>任务ID</th>
              <th>任务名称</th>
              <th>优先级</th>
              <th>状态</th>
              <th>创建人</th>
              <th>创建时间</th>
              <th>截止时间</th>
              <th>进度</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>#10086</td>
              <td>数据仓库定期维护</td>
              <td><span class="tag tag-warning">中</span></td>
              <td><span class="tag tag-success">已完成</span></td>
              <td>张三</td>
              <td>2023-07-15 09:30</td>
              <td>2023-07-16 18:00</td>
              <td><div class="progress-bar"><div class="progress" style="width: 100%; background-color: var(--success-color);"></div></div>100%</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>#10087</td>
              <td>用户行为数据分析</td>
              <td><span class="tag tag-danger">高</span></td>
              <td><span class="tag tag-danger">失败</span></td>
              <td>李四</td>
              <td>2023-07-15 14:15</td>
              <td>2023-07-16 12:00</td>
              <td><div class="progress-bar"><div class="progress" style="width: 65%; background-color: var(--danger-color);"></div></div>65%</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-redo"></i></button>
              </td>
            </tr>
            <tr>
              <td>#10088</td>
              <td>系统性能优化</td>
              <td><span class="tag tag-danger">高</span></td>
              <td><span class="tag tag-info">处理中</span></td>
              <td>王五</td>
              <td>2023-07-16 09:00</td>
              <td>2023-07-17 18:00</td>
              <td><div class="progress-bar"><div class="progress" style="width: 40%; background-color: var(--primary-color);"></div></div>40%</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-pause"></i></button>
              </td>
            </tr>
            <tr>
              <td>#10089</td>
              <td>数据备份与恢复测试</td>
              <td><span class="tag tag-warning">中</span></td>
              <td><span class="tag tag-warning">待处理</span></td>
              <td>赵六</td>
              <td>2023-07-16 10:30</td>
              <td>2023-07-18 18:00</td>
              <td><div class="progress-bar"><div class="progress" style="width: 0%; background-color: var(--warning-color);"></div></div>0%</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-play"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增任务模态框 -->
  <div class="modal" id="addTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" name="taskDescription" rows="3" required placeholder="请输入任务描述"></textarea>
          </div>
          <div class="form-group">
            <label for="taskPriority">任务优先级</label>
            <select id="taskPriority" name="taskPriority" required>
              <option value="">请选择优先级</option>
              <option value="high">高</option>
              <option value="medium" selected>中</option>
              <option value="low">低</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskAssignee">任务执行人</label>
            <select id="taskAssignee" name="taskAssignee" required>
              <option value="">请选择执行人</option>
              <option value="user1">张三</option>
              <option value="user2">李四</option>
              <option value="user3">王五</option>
              <option value="user4">赵六</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskStartTime">开始时间</label>
            <input type="datetime-local" id="taskStartTime" name="taskStartTime" required>
          </div>
          <div class="form-group">
            <label for="taskEndTime">截止时间</label>
            <input type="datetime-local" id="taskEndTime" name="taskEndTime" required>
          </div>
          <div class="form-group">
            <label for="taskType">任务类型</label>
            <select id="taskType" name="taskType" required>
              <option value="">请选择任务类型</option>
              <option value="data_analysis">数据分析</option>
              <option value="system_maintenance">系统维护</option>
              <option value="performance_optimization">性能优化</option>
              <option value="data_backup">数据备份</option>
              <option value="other">其他</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addTaskForm').submit()">创建任务</button>
      </div>
    </div>
  </div>

  <!-- 编辑任务模态框 -->
  <div class="modal" id="editTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑任务表单内容与新增表单类似，实际应用中会加载任务的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 新增任务表单提交
    document.getElementById('addTaskForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addTaskForm')) {
        // 模拟提交成功
        alert('任务创建成功！');
        document.getElementById('addTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>