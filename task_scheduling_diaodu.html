<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 任务调度看板</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      /* 基础变量定义 */
      :root {
        --primary-color: #4096ff;
        --primary-light: #e6f7ff;
        --primary-dark: #3385ff;
        --success-color: #52c41a;
        --success-light: #f6ffed;
        --warning-color: #faad14;
        --warning-light: #fffbe6;
        --danger-color: #ff4d4f;
        --danger-light: #fff2f0;
        --info-color: #1890ff;
        --info-light: #e6f7ff;
        --border-color: #e5e6eb;
        --text-primary: #1f2329;
        --text-secondary: #6b7785;
        --text-tertiary: #86909c;
        --bg-light: #f2f3f5;
        --bg-white: #ffffff;
        --shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
        --shadow-hover: 0 6px 16px 0 rgba(0, 0, 0, 0.12);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --radius: 6px;
      }

      /* 全局样式重置 */
  
      /* 自定义滚动条 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
      .page-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 10px;
      }

      .page-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 40px;
        height: 3px;
        background-color: var(--primary-color);
        border-radius: 3px;
      }

      .page-title-icon {
        margin-right: 10px;
        color: var(--primary-color);
        animation: bounce 2s infinite;
      }

      @keyframes bounce {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-5px);
        }
      }

      .breadcrumb {
        display: flex;
        margin-bottom: 20px;
        font-size: 14px;
        color: var(--text-secondary);
      }

      .breadcrumb-item {
        display: flex;
        align-items: center;
        transition: var(--transition);
      }

      .breadcrumb-item:not(:last-child)::after {
        content: '/';
        margin: 0 8px;
        color: var(--text-tertiary);
      }

      .breadcrumb-item.active {
        color: var(--text-primary);
        font-weight: 500;
      }

      .breadcrumb-item a:hover {
        color: var(--primary-color);
        text-decoration: underline;
      }

      /* 搜索框样式 */
      .search-box {
        position: relative;
        margin-bottom: 20px;
      }

      .search-input {
        width: 100%;
        padding: 10px 15px 10px 40px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        font-size: 14px;
        transition: var(--transition);
      }

      .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-tertiary);
        transition: var(--transition);
      }

      .search-input:focus + .search-icon {
        color: var(--primary-color);
      }

      /* 标题样式 */
      .page-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 20px;
        color: var(--text-primary);
        display: flex;
        align-items: center;
      }

      .page-title i {
        margin-right: 10px;
        color: var(--primary-color);
      }

      /* 选项卡样式 */
      .tabs-container {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 20px;
        background-color: var(--bg-white);
        padding-left: 15px;
        border-radius: var(--radius) var(--radius) 0 0;
        overflow-x: auto;
        scrollbar-width: thin;
      }

      .tab {
        padding: 14px 24px;
        cursor: pointer;
        border: none;
        border-bottom: 3px solid transparent;
        margin-right: 5px;
        display: flex;
        align-items: center;
        font-size: 14px;
        transition: var(--transition);
        color: var(--text-secondary);
        background-color: transparent;
        white-space: nowrap;
        position: relative;
      }

      .tab i {
        margin-right: 8px;
        font-size: 16px;
        transition: var(--transition);
      }

      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
        font-weight: 500;
      }

      .tab.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background-color: var(--bg-white);
      }

      .tab:hover:not(.active) {
        color: var(--primary-color);
        background-color: var(--primary-light);
      }

      .tab-content {
        display: none;
        background-color: var(--bg-white);
        padding: 20px;
        border: 1px solid var(--border-color);
        border-top: none;
        min-height: 500px;
        border-radius: 0 0 var(--radius) var(--radius);
        box-shadow: var(--shadow);
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(5px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .tab-content.active {
        display: block;
      }

      /* 按钮样式 */
      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 6px 16px;
        border-radius: var(--radius);
        font-size: 14px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: var(--transition);
        background-color: transparent;
        color: var(--text-secondary);
      }

      .btn:hover {
        background-color: var(--bg-light);
        color: var(--primary-color);
      }

      .btn-primary {
        background-color: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background-color: var(--primary-dark);
        color: white;
      }

      .btn-text {
        color: var(--text-secondary);
      }

      .btn-text:hover {
        color: var(--primary-color);
        background-color: transparent;
      }

      /* 卡片样式 */
      .card {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        border: 1px solid var(--border-color);
        margin-bottom: 20px;
        box-shadow: var(--shadow);
        transition: var(--transition);
      }

      .card:hover {
        box-shadow: var(--shadow-hover);
      }

      .card-header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
        display: flex;
        align-items: center;
      }

      .card-title i {
        margin-right: 8px;
        color: var(--primary-color);
      }

      .card-body {
        padding: 20px;
      }

      .card-footer {
        padding: 16px 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* 统计卡片样式 */
      .stat-card {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        border-left: 4px solid var(--primary-color);
        padding: 16px;
        box-shadow: var(--shadow);
        transition: var(--transition);
      }

      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
      }

      .stat-card-title {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }

      .stat-card-title i {
        margin-right: 8px;
      }

      .stat-card-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .stat-card-extra {
        font-size: 12px;
        color: var(--text-tertiary);
        display: flex;
        align-items: center;
      }

      .stat-card-extra i {
        margin-right: 4px;
      }

      /* 标签样式 */
      .tag {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-right: 4px;
      }

      .tag-success {
        background-color: var(--success-light);
        color: var(--success-color);
      }

      .tag-warning {
        background-color: var(--warning-light);
        color: var(--warning-color);
      }

      .tag-danger {
        background-color: var(--danger-light);
        color: var(--danger-color);
      }

      .tag-info {
        background-color: var(--info-light);
        color: var(--info-color);
      }

      .tag-primary {
        background-color: var(--primary-light);
        color: var(--primary-color);
      }

      /* 表格样式 */
      .table {
        width: 100%;
        border-collapse: collapse;
      }

      .table th,
      .table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }

      .table th {
        font-weight: 500;
        color: var(--text-primary);
        background-color: #f7f8fa;
      }

      .table tr:hover {
        background-color: #f7f8fa;
      }

      /* 表单样式 */
      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
      }

      input[type='text'],
      select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        font-size: 14px;
        transition: var(--transition);
      }

      input[type='text']:focus,
      select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
      }

      /* 配置项样式 */
      .config-item {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px dashed var(--border-color);
        transition: var(--transition);
      }

      .config-item:hover {
        background-color: rgba(64, 150, 255, 0.02);
      }

      .config-item:last-child {
        border-bottom: none;
      }

      .config-label {
        font-weight: 500;
        margin-bottom: 8px;
        display: block;
        color: var(--text-primary);
        font-size: 14px;
        position: relative;
        padding-left: 20px;
      }

      .config-label::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: var(--primary-color);
      }

      /* 通知项样式 */
      .notification-item {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: var(--radius);
        background-color: var(--bg-white);
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .notification-item::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(64, 150, 255, 0.05) 0%, rgba(64, 150, 255, 0) 100%);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }

      .notification-item:hover::after {
        transform: translateX(0);
      }

      .notification-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }

      .notification-time {
        font-size: 12px;
        color: var(--text-tertiary);
        display: flex;
        align-items: center;
      }

      .notification-time i {
        margin-right: 4px;
      }

      /* 分页样式 */
      .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }

      .pagination-item {
        width: 32px;
        height: 32px;
        border-radius: var(--radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 4px;
        cursor: pointer;
        transition: var(--transition);
        color: var(--text-secondary);
      }

      .pagination-item:hover:not(.active) {
        background-color: var(--bg-light);
        color: var(--primary-color);
      }

      .pagination-item.active {
        background-color: var(--primary-color);
        color: white;
      }

      /* 任务类型项样式 */
      .task-type-item {
        padding: 10px 16px;
        margin-bottom: 8px;
        border-radius: var(--radius);
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        color: var(--text-secondary);
      }

      .task-type-item.active {
        background-color: var(--primary-light);
        color: var(--primary-color);
        font-weight: 500;
      }

      .task-type-item:hover:not(.active) {
        background-color: var(--bg-light);
      }

      /* 工具提示样式 */
      .tooltip {
        position: relative;
      }

      .tooltip-text {
        visibility: hidden;
        width: 120px;
        background-color: var(--text-primary);
        color: white;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 12px;
        font-weight: normal;
      }

      .tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
      }

      /* 动画效果 */
      .fade-in {
        animation: fadeIn 0.5s ease forwards;
        opacity: 0;
      }

      /* 弹窗样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .modal-overlay.active {
        opacity: 1;
        visibility: visible;
      }

      .modal {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        width: 90%;
        max-width: 500px;
        box-shadow: var(--shadow-hover);
        transform: translateY(-20px);
        transition: transform 0.3s ease;
      }

      .modal-overlay.active .modal {
        transform: translateY(0);
        margin: 20px auto;
        display: block;
        overflow: auto;
      }

      .modal-header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .modal-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
      }

      .modal-close {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: var(--text-tertiary);
        transition: var(--transition);
      }

      .modal-close:hover {
        color: var(--danger-color);
      }

      .modal-body {
        /* padding: 20px;
      max-height: 70vh;
      overflow-y: auto; */
      }

      .modal-footer {
        padding: 16px 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }

      /* 任务详情表单样式 */
      .task-detail-form .form-group {
        margin-bottom: 15px;
      }

      .task-detail-form label {
        font-weight: 500;
        margin-bottom: 5px;
        display: block;
      }

      .task-detail-form input,
      .task-detail-form select,
      .task-detail-form textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        font-size: 14px;
      }

      .task-detail-form textarea {
        min-height: 100px;
        resize: vertical;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
     <div class="sidebar">
      <div class="menu-item " data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child active" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="data_permission_control.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-calendar-alt page-title-icon"></i>
        任务调度看板
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">五级穿透调度</a></div>
        <div class="breadcrumb-item active">任务调度看板</div>
      </div>

      <!-- 搜索框 -->

      <!-- 选项卡容器 -->
      <div class="tabs-container">
        <button class="tab active" onclick="switchTab('task-overview')">
          <i class="fas fa-chart-pie"></i>
          任务总览
        </button>
        <button class="tab" onclick="switchTab('task-management')">
          <i class="fas fa-list-alt"></i>
          任务管理
        </button>
        <button class="tab" onclick="switchTab('generation-rules')">
          <i class="fas fa-cogs"></i>
          生成规则管理
        </button>
        <button class="tab" onclick="switchTab('task-configuration')">
          <i class="fas fa-sliders-h"></i>
          任务配置管理
        </button>
        <button class="tab" onclick="switchTab('notification')">
          <i class="fas fa-bell"></i>
          通知下发
        </button>
      </div>

      <!-- 任务总览 -->
      <div class="tab-content active" id="task-overview">
        <!-- 数据概览卡片 -->
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px">
          <div class="stat-card" style="border-left-color: var(--warning-color)">
            <div class="stat-card-title">
              <i class="fas fa-clock"></i>
              待处理任务
            </div>
            <div class="stat-card-value">32</div>
            <div class="stat-card-extra">
              <i class="fas fa-arrow-up"></i>
              <span class="tag tag-warning">+5 较昨日</span>
            </div>
          </div>
          <div class="stat-card" style="border-left-color: var(--info-color)">
            <div class="stat-card-title">
              <i class="fas fa-spinner"></i>
              处理中任务
            </div>
            <div class="stat-card-value">18</div>
            <div class="stat-card-extra">
              <i class="fas fa-arrow-up"></i>
              <span class="tag tag-info">+3 较昨日</span>
            </div>
          </div>
          <div class="stat-card" style="border-left-color: var(--success-color)">
            <div class="stat-card-title">
              <i class="fas fa-check"></i>
              已完成任务
            </div>
            <div class="stat-card-value">156</div>
            <div class="stat-card-extra">
              <i class="fas fa-arrow-up"></i>
              <span class="tag tag-success">+12 较昨日</span>
            </div>
          </div>
          <div class="stat-card" style="border-left-color: var(--danger-color)">
            <div class="stat-card-title">
              <i class="fas fa-times"></i>
              失败任务
            </div>
            <div class="stat-card-value">3</div>
            <div class="stat-card-extra">
              <i class="fas fa-arrow-down"></i>
              <span class="tag tag-danger">-2 较昨日</span>
            </div>
          </div>
        </div>

        <!-- 任务分类统计 -->
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 20px">
          <div class="card">
            <div class="card-header">
              <div class="card-title">
                <i class="fas fa-tags"></i>
                任务类型分布
              </div>
            </div>
            <div class="card-body">
              <div style="height: 250px; display: flex; align-items: center; justify-content: center">
                <canvas id="taskTypeChart"></canvas>
              </div>
            </div>
          </div>
          <div class="card">
            <div class="card-header">
              <div class="card-title">
                <i class="fas fa-signal"></i>
                任务优先级分布
              </div>
            </div>
            <div class="card-body">
              <div style="height: 250px; display: flex; align-items: center; justify-content: center">
                <canvas id="taskPriorityChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务管理 -->
      <div class="tab-content" id="task-management">
        <div class="card">
          <div class="card-header">
            <div class="card-title">
              <i class="fas fa-tasks"></i>
              任务管理
            </div>
          </div>
          <div class="card-body">
            <!-- <button id="addTaskBtn" class="btn btn-primary" style="margin-bottom: 15px;">
            <i class="fas fa-plus-circle"></i> 新增任务
          </button> -->

            <div class="form-group">
              <label>快速筛选</label>
              <select style="margin-bottom: 15px">
                <option>所有任务</option>
                <option>待处理任务</option>
                <option>处理中任务</option>
                <option>已完成任务</option>
                <option>高优先级任务</option>
              </select>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 15px">
              <button class="btn btn-primary" style="flex: 1">
                <i class="fas fa-filter"></i>
                高级筛选
              </button>
              <button class="btn" style="flex: 1">
                <i class="fas fa-sync-alt"></i>
                刷新任务
              </button>
              <button class="btn" style="flex: 1">
                <i class="fas fa-download"></i>
                导出任务
              </button>
            </div>
          </div>
        </div>

        <!-- 最近任务列表 -->
        <div class="card">
          <div class="card-header">
            <div class="card-title">
              <i class="fas fa-list-alt"></i>
              最近任务
            </div>
          </div>
          <div class="card-body">
            <!-- 任务列表操作区 -->
            <div class="task-actions" style="display: flex; justify-content: space-between; margin-bottom: 15px">
              <button class="btn btn-primary" id="addTaskBtn">
                <i class="fas fa-plus"></i>
                新增任务
              </button>
              <div class="task-search-box" style="display: flex; align-items: center; width: 300px">
                <input type="text" class="search-input task-search-input" placeholder="搜索任务..." style="flex: 1; margin-right: 5px" />
                <i class="fas fa-search search-icon task-search-icon"></i>
              </div>
            </div>

            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>任务ID</th>
                    <th>任务名称</th>
                    <th>优先级</th>
                    <th>状态</th>
                    <th>截止时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="fade-in" style="animation-delay: 0.1s">
                    <td>#10091</td>
                    <td>数据仓库定期维护</td>
                    <td>
                      <span class="tag tag-warning">
                        <i class="fas fa-exclamation"></i>
                        中
                      </span>
                    </td>
                    <td>
                      <span class="tag tag-success">
                        <i class="fas fa-check"></i>
                        已完成
                      </span>
                    </td>
                    <td>2023-07-17 18:00</td>
                    <td>
                      <button class="btn view-task tooltip" data-task-id="10091" style="color: var(--primary-color)">
                        <i class="fas fa-eye"></i>
                        查看
                        <span class="tooltip-text">查看任务详情</span>
                      </button>
                      <button class="btn distribute-task tooltip" data-task-id="10091" style="color: var(--success-color)">
                        <i class="fas fa-paper-plane"></i>
                        下发
                        <span class="tooltip-text">下发任务</span>
                      </button>
                      <!-- <button class="btn operation-record tooltip" data-task-id="10091" style="color: var(--info-color); margin-left: 5px">
                        <i class="fas fa-history"></i>
                        操作记录
                        <span class="tooltip-text">查看任务操作记录</span>
                      </button>
                      <button class="btn task-tracking tooltip" data-task-id="10091" style="color: var(--warning-color); margin-left: 5px">
                        <i class="fas fa-chart-line"></i>
                        任务跟踪
                        <span class="tooltip-text">查看任务跟踪信息</span>
                      </button> -->
                    </td>
                  </tr>
                  <tr class="fade-in" style="animation-delay: 0.2s">
                    <td>#10092</td>
                    <td>用户行为数据分析</td>
                    <td>
                      <span class="tag tag-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        高
                      </span>
                    </td>
                    <td>
                      <span class="tag tag-info">
                        <i class="fas fa-spinner"></i>
                        处理中
                      </span>
                    </td>
                    <td>2023-07-18 20:00</td>
                    <td>
                      <button class="btn view-task tooltip" data-task-id="10092" style="color: var(--primary-color)">
                        <i class="fas fa-eye"></i>
                        查看
                        <span class="tooltip-text">查看任务详情</span>
                      </button>
                      <button class="btn distribute-task tooltip" data-task-id="10092" style="color: var(--success-color)">
                        <i class="fas fa-paper-plane"></i>
                        下发
                        <span class="tooltip-text">下发任务</span>
                      </button>
                      <!-- <button class="btn operation-record tooltip" data-task-id="10092" style="color: var(--info-color); margin-left: 5px">
                        <i class="fas fa-history"></i>
                        操作记录
                        <span class="tooltip-text">查看任务操作记录</span>
                      </button>
                      <button class="btn task-tracking tooltip" data-task-id="10092" style="color: var(--warning-color); margin-left: 5px">
                        <i class="fas fa-chart-line"></i>
                        任务跟踪
                        <span class="tooltip-text">查看任务跟踪信息</span>
                      </button> -->
                    </td>
                  </tr>
                  <tr class="fade-in" style="animation-delay: 0.3s">
                    <td>#10093</td>
                    <td>系统性能监控报告</td>
                    <td>
                      <span class="tag tag-primary">
                        <i class="fas fa-circle"></i>
                        低
                      </span>
                    </td>
                    <td>
                      <span class="tag tag-warning">
                        <i class="fas fa-clock"></i>
                        待处理
                      </span>
                    </td>
                    <td>2023-07-19 12:00</td>
                    <td>
                      <button class="btn view-task tooltip" data-task-id="10093" style="color: var(--primary-color)">
                        <i class="fas fa-eye"></i>
                        查看
                        <span class="tooltip-text">查看任务详情</span>
                      </button>
                      <button class="btn distribute-task tooltip" data-task-id="10093" style="color: var(--success-color)">
                        <i class="fas fa-paper-plane"></i>
                        下发
                        <span class="tooltip-text">下发任务</span>
                      </button>
                      <!-- <button class="btn operation-record tooltip" data-task-id="10093" style="color: var(--info-color); margin-left: 5px">
                        <i class="fas fa-history"></i>
                        操作记录
                        <span class="tooltip-text">查看任务操作记录</span>
                      </button>
                      <button class="btn task-tracking tooltip" data-task-id="10093" style="color: var(--warning-color); margin-left: 5px">
                        <i class="fas fa-chart-line"></i>
                        任务跟踪
                        <span class="tooltip-text">查看任务跟踪信息</span>
                      </button> -->
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
              <div class="pagination-item"><i class="fas fa-angle-left"></i></div>
              <div class="pagination-item active">1</div>
              <div class="pagination-item">2</div>
              <div class="pagination-item">3</div>
              <div class="pagination-item"><i class="fas fa-angle-right"></i></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生成规则 -->
      <div class="tab-content" id="generation-rules">
        <div class="config-item">
          <div class="config-label">任务自动生成频率</div>
          <div>
            <select>
              <option>每小时</option>
              <option selected>每天</option>
              <option>每周</option>
              <option>每月</option>
              <option>自定义</option>
            </select>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">任务优先级生成规则</div>
          <div>
            <label>
              <input type="checkbox" checked />
              基于任务类型自动分配优先级
            </label>
            <br />
            <label>
              <input type="checkbox" checked />
              基于业务重要性调整优先级
            </label>
            <br />
            <label>
              <input type="checkbox" />
              基于历史执行时间调整优先级
            </label>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">任务负责人分配规则</div>
          <div>
            <select>
              <option selected>基于部门自动分配</option>
              <option>基于技能标签匹配</option>
              <option>基于工作量均衡分配</option>
              <option>固定负责人</option>
            </select>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">任务截止时间设置</div>
          <div>
            <select>
              <option>固定时长（如24小时内）</option>
              <option selected>基于任务类型预设</option>
              <option>基于工作时间计算</option>
            </select>
          </div>
        </div>

        <div style="text-align: right">
          <button class="btn btn-primary save-settings">保存设置</button>
          <button class="btn btn-text reset-settings">重置</button>
        </div>
      </div>

      <!-- 任务配置 -->
      <div class="tab-content" id="task-configuration">
        <div class="card">
          <div class="card-header">
            <div class="card-title">
              <i class="fas fa-cog"></i>
              任务类型配置
            </div>
          </div>
          <div class="card-body">
            <div class="task-type-item active">
              <i class="fas fa-database" style="margin-right: 8px"></i>
              数据处理任务
            </div>
            <div class="task-type-item">
              <i class="fas fa-chart-line" style="margin-right: 8px"></i>
              数据分析任务
            </div>
            <div class="task-type-item">
              <i class="fas fa-server" style="margin-right: 8px"></i>
              系统维护任务
            </div>
          </div>
        </div>
      </div>

      <!-- 通知下发 -->
      <div class="tab-content" id="notification">
        <div class="card">
          <div class="card-header">
            <div class="card-title">
              <i class="fas fa-paper-plane"></i>
              发送通知
            </div>
          </div>
          <div class="card-body">
            <div class="form-group">
              <label>通知类型</label>
              <select id="notificationType">
                <option value="task">任务通知</option>
                <option value="system">系统通知</option>
                <option value="alert">告警通知</option>
              </select>
            </div>
            <div class="form-group">
              <label>接收人</label>
              <input type="text" placeholder="输入接收人，多个用逗号分隔" />
            </div>
            <div class="form-group">
              <label>通知标题</label>
              <input type="text" placeholder="输入通知标题" />
            </div>
            <div class="form-group">
              <label>通知内容</label>
              <textarea placeholder="输入通知内容"></textarea>
            </div>
            <button class="btn btn-primary send-notification">发送通知</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 查看任务详情弹窗 -->
    <div class="modal-overlay" id="viewTaskModal">
      <div class="modal">
        <div class="modal-header">
          <div class="modal-title">任务详情</div>
          <button class="modal-close" id="closeViewTaskModal" onclick="closeModal('viewTaskModal')">&times;</button>
        </div>
        <div class="modal-body">
          <div class="task-detail-form">
            <div class="form-group">
              <label>任务ID</label>
              <input type="text" id="taskId" readonly />
            </div>
            <div class="form-group">
              <label>任务名称</label>
              <input type="text" id="taskName" readonly />
            </div>
            <div class="form-group">
              <label>优先级</label>
              <input type="text" id="taskPriority" readonly />
            </div>
            <div class="form-group">
              <label>状态</label>
              <input type="text" id="taskStatus" readonly />
            </div>
            <div class="form-group">
              <label>截止时间</label>
              <input type="text" id="taskDeadline" readonly />
            </div>
            <div class="form-group">
              <label>任务描述</label>
              <textarea id="taskDescription" readonly></textarea>
            </div>
            <div class="form-group">
              <label>负责人</label>
              <input type="text" id="taskOwner" readonly />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-text modal-close-btn" onclick="closeModal('viewTaskModal')">关闭</button>
        </div>
      </div>
    </div>

    <!-- 在现有代码的适当位置添加以下弹窗代码 -->
    <div class="modal-overlay" id="addTaskModal">
      <div class="modal">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus-circle"></i>
            新增任务
          </div>
          <button class="modal-close" onclick="closeModal('addTaskModal')">&times;</button>
        </div>
        <div class="modal-body">
          <form class="task-detail-form">
            <div class="form-group">
              <label for="taskName">任务名称</label>
              <input type="text" id="taskName" required />
            </div>
            <div class="form-group">
              <label for="taskType">任务类型</label>
              <select id="taskType" required>
                <option value="">请选择任务类型</option>
                <option value="data-analysis">数据分析</option>
                <option value="system-maintenance">系统维护</option>
                <option value="report-generation">报告生成</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label for="taskPriority">优先级</label>
              <select id="taskPriority" required>
                <option value="high">高</option>
                <option value="medium" selected>中</option>
                <option value="low">低</option>
              </select>
            </div>
            <div class="form-group">
              <label for="taskDeadline">截止时间</label>
              <input type="datetime-local" id="taskDeadline" required />
            </div>
            <div class="form-group">
              <label for="taskAssignee">负责人</label>
              <input type="text" id="taskAssignee" required />
            </div>
            <div class="form-group">
              <label for="taskDescription">任务描述</label>
              <textarea id="taskDescription"></textarea>
            </div>

            <!-- 新增任务配合规则配置选项框 -->
            <div class="form-group">
              <label for="cooperationRules">任务配合规则配置</label>
              <select id="cooperationRules">
                <option value="">无特殊规则配置</option>
                <option value="sequential">数据分析任务</option>
                <option value="parallel">数据分析任务</option>
                <option value="dependency">系统维护任务</option>
                <option value="resource-sharing">数据处理任务</option>
                <option value="custom">自定义规则</option>
              </select>
            </div>

            <div class="form-group" id="customRuleContainer" style="display: none">
              <label for="customRule">自定义配合规则</label>
              <textarea id="customRule" placeholder="请输入自定义配合规则描述..."></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" onclick="closeModal('addTaskModal')">取消</button>
          <button class="btn btn-primary" onclick="saveTask()">自动生成任务</button>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      // 为新增任务按钮添加点击事件
      document.getElementById('addTaskBtn').addEventListener('click', function () {
        document.getElementById('addTaskModal').classList.add('active');
      });

      // 关闭弹窗函数
      function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
      }

      // 保存任务函数
      function saveTask() {
        // 这里添加保存任务的逻辑
        alert('任务保存成功！');
        closeModal('addTaskModal');
      }

      // 任务配合规则变更事件 - 显示/隐藏自定义规则输入框
      document.getElementById('cooperationRules').addEventListener('change', function () {
        const customContainer = document.getElementById('customRuleContainer');
        if (this.value === 'custom') {
          customContainer.style.display = 'block';
        } else {
          customContainer.style.display = 'none';
        }
      });
    </script>

    <script>
      // 选项卡切换功能
      function switchTab(tabId) {
        // 隐藏所有选项卡内容
        document.querySelectorAll('.tab-content').forEach(tab => {
          tab.classList.remove('active');
        });

        // 移除所有选项卡的激活状态
        document.querySelectorAll('.tab').forEach(tab => {
          tab.classList.remove('active');
        });

        // 显示选中的选项卡内容
        document.getElementById(tabId).classList.add('active');

        // 激活选中的选项卡按钮
        document.querySelectorAll('.tab').forEach(tab => {
          if (tab.getAttribute('onclick').includes(tabId)) {
            tab.classList.add('active');
          }
        });
      }
      // // 显示消息弹窗
      // function showMessage(title, content) {
      //   document.getElementById('messageTitle').textContent = title;
      //   document.getElementById('messageContent').textContent = content;
      //   document.getElementById('messageModal').classList.add('active');
      // }

      // 关闭弹窗
      function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
      }
      // 任务数据
      const tasksData = {
        10091: {
          id: '#10091',
          name: '数据仓库定期维护',
          priority: '中',
          status: '已完成',
          deadline: '2023-07-17 18:00',
          description: '对数据仓库进行定期维护，包括索引优化、数据清理和性能检查。确保数据仓库运行稳定，查询性能良好。',
          owner: '张三',
        },
        10092: {
          id: '#10092',
          name: '用户行为数据分析',
          priority: '高',
          status: '处理中',
          deadline: '2023-07-18 20:00',
          description: '分析最近一周的用户行为数据，生成用户活跃度、留存率和转化率报告，为产品优化提供数据支持。',
          owner: '李四',
        },
        10093: {
          id: '#10093',
          name: '系统性能监控报告',
          priority: '低',
          status: '待处理',
          deadline: '2023-07-19 12:00',
          description: '收集并分析系统最近30天的性能数据，包括CPU使用率、内存占用、响应时间等指标，生成性能监控报告并提出优化建议。',
          owner: '王五',
        },
      };

      // 初始化图表
      function initCharts() {
        // 任务类型分布图表
        const taskTypeCtx = document.getElementById('taskTypeChart').getContext('2d');
        new Chart(taskTypeCtx, {
          type: 'doughnut',
          data: {
            labels: ['数据处理', '数据分析', '系统维护', '其他'],
            datasets: [
              {
                data: [45, 30, 20, 5],
                backgroundColor: ['#4096ff', '#52c41a', '#faad14', '#86909c'],
                borderWidth: 0,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right',
              },
            },
          },
        });

        // 任务优先级分布图表
        const taskPriorityCtx = document.getElementById('taskPriorityChart').getContext('2d');
        new Chart(taskPriorityCtx, {
          type: 'pie',
          data: {
            labels: ['高', '中', '低'],
            datasets: [
              {
                data: [25, 50, 25],
                backgroundColor: ['#ff4d4f', '#faad14', '#4096ff'],
                borderWidth: 0,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right',
              },
            },
          },
        });
      }

      // // 显示消息弹窗
      // function showMessage(title, content) {
      //   document.getElementById('messageTitle').textContent = title;
      //   document.getElementById('messageContent').textContent = content;
      //   document.getElementById('messageModal').classList.add('active');
      // }

      // 关闭弹窗
      function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
      }

      // 初始化事件监听
      function initEventListeners() {
        // 查看任务按钮点击事件
        document.querySelectorAll('.view-task').forEach(button => {
          button.addEventListener('click', function () {
            const taskId = this.getAttribute('data-task-id');
            const task = tasksData[taskId];
            console.log('------', taskId);

            if (task) {
              document.getElementById('taskId').value = task.id;
              document.getElementById('taskName').value = task.name;
              document.getElementById('taskPriority').value = task.priority;
              document.getElementById('taskStatus').value = task.status;
              document.getElementById('taskDeadline').value = task.deadline;
              document.getElementById('taskDescription').value = task.description;
              document.getElementById('taskOwner').value = task.owner;

              document.getElementById('viewTaskModal').classList.add('active');
            }
          });
        });

        // 新增任务按钮点击事件
        document.getElementById('addTaskBtn').addEventListener('click', function () {
          document.getElementById('addTaskModal').classList.add('active');
        });

        // 保存任务按钮点击事件
        // document.getElementById('saveTaskBtn').addEventListener('click', function () {
        //   const taskName = document.getElementById('newTaskName').value;

        //   if (!taskName) {
        //     showMessage('提示', '请输入任务名称');
        //     return;
        //   }

        //   closeModal('addTaskModal');
        //   showMessage('成功', '任务已成功创建');
        // });

        // 下发任务按钮点击事件
        document.querySelectorAll('.distribute-task').forEach(button => {
          button.addEventListener('click', function () {
            const taskId = this.getAttribute('data-task-id');
            const taskName = getTaskNameById(taskId);
            const task = tasksData[taskId];

            if (task) {
              // showMessage('提示', `任务 ${task.id} 已下发成功`);
              if (confirm(`确定要下发任务 #${taskId} "${taskName}" 吗？`)) {
                alert(`任务 #${taskId} 已成功下发！`);
              }
            }
          });
        });

        // 辅助函数：根据任务ID获取任务名称
        function getTaskNameById(taskId) {
          const taskNames = {
            10091: '数据仓库定期维护',
            10092: '用户行为数据分析',
            10093: '系统性能监控报告',
            10094: '数据备份与恢复测试',
            10095: '用户画像更新',
          };
          return taskNames[taskId] || '未知任务';
        }

        // 操作记录按钮点击事件
        document.querySelectorAll('.operation-record').forEach(button => {
          button.addEventListener('click', function () {
            const taskId = this.getAttribute('data-task-id');
            const task = tasksData[taskId];

            if (task) {
              // showMessage('操作记录', `任务 ${task.id} 的操作记录将在这里显示`);
            }
          });
        });

        // 任务跟踪按钮点击事件
        document.querySelectorAll('.task-tracking').forEach(button => {
          button.addEventListener('click', function () {
            const taskId = this.getAttribute('data-task-id');
            const task = tasksData[taskId];

            if (task) {
              showMessage('任务跟踪', `任务 ${task.id} 的跟踪信息将在这里显示`);
            }
          });
        });

        // 保存设置按钮点击事件
        document.querySelector('.save-settings').addEventListener('click', function () {
          showMessage('成功', '设置已保存');
        });

        // 重置按钮点击事件
        document.querySelector('.reset-settings').addEventListener('click', function () {
          showMessage('提示', '设置已重置为默认值');
        });

        // 发送通知按钮点击事件
        document.querySelector('.send-notification').addEventListener('click', function () {
          showMessage('成功', '通知已发送');
        });

        // 关闭弹窗按钮事件
        document.querySelectorAll('.modal-close, .modal-close-btn').forEach(button => {
          button.addEventListener('click', function () {
            document.querySelectorAll('.modal-overlay').forEach(modal => {
              modal.classList.remove('active');
            });
          });
        });

        // 点击弹窗外部关闭弹窗
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
          overlay.addEventListener('click', function (e) {
            if (e.target === this) {
              this.classList.remove('active');
            }
          });
        });
      }

      // 页面加载完成后初始化
      window.addEventListener('DOMContentLoaded', function () {
        initCharts();
        initEventListeners();
      });
    </script>
  </body>
</html>
