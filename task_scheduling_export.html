<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 统计分析与报表管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* 原有样式保持不变 */
    :root {
      --primary-color: #165DFF;
      --primary-light: #E8F3FF;
      --success-color: #00B42A;
      --success-light: #E6FFED;
      --warning-color: #FF7D00;
      --warning-light: #FFF7E8;
      --danger-color: #F53F3F;
      --danger-light: #FFECE8;
      --info-color: #86909C;
      --info-light: #F2F3F5;
      --text-primary: #1D2129;
      --text-secondary: #4E5969;
      --text-tertiary: #86909C;
      --border-color: #D9D9D9;
      --bg-color: #F7F8FA;
      --card-shadow: 0 2px 14px rgba(0, 0, 0, 0.06);
      --transition-default: all 0.3s ease;

       --primary-color: #1890ff;
  --secondary-color: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #ff4d4f;
  --info-color: #1890ff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e8e8e8;
  --hover-color: #f0f0f0;
    }

  
    /* 按钮美化 */
    .btn {
      padding: 6px 16px;
      border-radius: 4px;
      border: none;
      cursor: pointer;
      transition: var(--transition-default);
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      outline: none;
    }

    .btn:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    .btn:active {
      transform: translateY(1px);
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: #fff;
    }

    .btn-primary:hover {
      background-color: #0E42D2;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
    }

    /* 卡片美化 */
    .card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: var(--card-shadow);
      overflow: hidden;
      transition: var(--transition-default);
    }

    .card:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .card-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-weight: 600;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .card-body {
      padding: 20px;
    }

    /* 统计卡片美化 */
    .stat-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: var(--card-shadow);
      transition: var(--transition-default);
      border-top: 3px solid var(--primary-color);
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .stat-card-title {
      color: var(--text-secondary);
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .stat-card-value {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    /* 标签美化 */
    .tag {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .tag-primary {
      background-color: var(--primary-light);
      color: var(--primary-color);
    }

    .tag-success {
      background-color: var(--success-light);
      color: var(--success-color);
    }

    .tag-warning {
      background-color: var(--warning-light);
      color: var(--warning-color);
    }

    .tag-info {
      background-color: var(--info-light);
      color: var(--info-color);
    }

    /* 表格美化 */
    .table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
    }

    .table th,
    .table td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    .table th {
      background-color: var(--bg-color);
      font-weight: 600;
      color: var(--text-secondary);
      position: relative;
    }

    .table th::after {
      content: '';
      position: absolute;
      right: 0;
      top: 12px;
      height: 16px;
      width: 1px;
      background-color: var(--border-color);
    }

    .table th:last-child::after {
      display: none;
    }

    .table tbody tr:hover {
      background-color: var(--bg-color);
    }

    /* 选项卡美化 */
    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 8px 8px 0 0;
      overflow: hidden;
    }

    .tab {
      padding: 14px 20px;
      cursor: pointer;
      transition: var(--transition-default);
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 3px solid transparent;
    }

    .tab:hover:not(.active) {
      color: var(--primary-color);
      background-color: var(--bg-color);
    }

    .tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }

    /* 表单元素美化 */
    .form-group {
      margin-bottom: 16px;
    }

    label {
      display: block;
      margin-bottom: 6px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      transition: var(--transition-default);
      font-size: 14px;
    }

    input:focus, select:focus, textarea:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
    }

    /* 模态框美化 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .modal.show {
      display: flex;
      opacity: 1;
    }

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 600px;
      max-width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      transform: translateY(-20px);
      transition: transform 0.3s ease;
    }

    .modal.show .modal-content {
      transform: translateY(0);
    }

    .modal-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-title {
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: var(--text-tertiary);
      transition: var(--transition-default);
    }

    .modal-close:hover {
      color: var(--danger-color);
    }

    .modal-body {
      padding: 20px;
    }

    .modal-footer {
      padding: 16px 20px;
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    /* 下拉菜单美化 */
    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-toggle {
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 10px;
      border-radius: 4px;
      transition: var(--transition-default);
    }

    .dropdown-toggle:hover {
      background-color: var(--bg-color);
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      min-width: 200px;
      z-index: 100;
      display: none;
      margin-top: 4px;
      overflow: hidden;
    }

    .dropdown:hover .dropdown-menu {
      display: block;
      animation: fadeIn 0.2s ease;
    }

    .dropdown-item {
      padding: 10px 16px;
      cursor: pointer;
      transition: var(--transition-default);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dropdown-item:hover {
      background-color: var(--primary-light);
      color: var(--primary-color);
    }

    /* 面包屑美化 */
    .breadcrumb {
      display: flex;
      gap: 8px;
      margin-bottom: 20px;
      color: var(--text-tertiary);
    }

    .breadcrumb-item::after {
      content: '/';
      margin-left: 8px;
    }

    .breadcrumb-item:last-child::after {
      display: none;
    }

    .breadcrumb-item.active {
      color: var(--text-primary);
      font-weight: 500;
    }

    .breadcrumb-item a:hover {
      color: var(--primary-color);
      text-decoration: underline;
    }

    /* 动画效果 */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* 图表容器美化 */
    .chart-container {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: var(--card-shadow);
      transition: var(--transition-default);
    }

    .chart-container:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    /* 页面标题美化 */
    .page-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    /* 徽章美化 */
    .badge {
      background-color: var(--danger-color);
      color: #fff;
      border-radius: 10px;
      padding: 0 6px;
      font-size: 12px;
      font-weight: 500;
    }

    /* 加载动画 */
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
   <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
      <div class="sidebar">
      <div class="menu-item " data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child active" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="data_permission_control.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-chart-pie page-title-icon"></i>
      统计分析与报表管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item active">统计分析与报表管理</div>
    </div>

    <!-- 选项卡导航 -->
    <div class="tabs">
      <div class="tab active" data-tab-target="stat-dimension"><i class="fas fa-cubes"></i> 统计维度</div>
      <!-- <div class="tab" data-tab-target="stat-analysis"><i class="fas fa-chart-line"></i> 统计分析</div>-->
      <div class="tab" data-tab-target="report-management"><i class="fas fa-file-alt"></i> 报表管理</div>
      <div class="tab" data-tab-target="data-export"><i class="fas fa-file-export"></i> 数据管理</div>
    </div>
    

    <!-- 统计维度定义 -->
    <div class="tab-content active" id="stat-dimension">
    <!-- 功能说明和刷新按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="font-size: 14px; color: var(--text-secondary);">
        <i class="fas fa-info-circle"></i> 统计分析功能帮助您从多维度分析业务数据，生成可视化报表并支持数据导出
      </div>
      <div style="display: flex;">
        <button class="btn btn-primary" id="refreshDataBtn"><i class="fas fa-sync-alt"></i> 刷新数据</button>
      </div>
    </div>

      <div class="card">
        <div class="card-header">
          <div class="card-title"><i class="fas fa-cubes"></i> 统计维度定义</div>
          <div style="display: flex;gap: 10px;">
          <button class="btn btn-primary" id="addDimensionBtn"><i class="fas fa-plus"></i> 新增维度</button>
          <div class="ml-3"><input type="text" id="dimensionSearch" class="form-control" placeholder="搜索维度..." style="width: 200px; display: inline-block;"></div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>维度ID</th>
                  <th>维度名称</th>
                  <th>维度类型</th>
                  <th>关联数据表</th>
                  <th>创建时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>DIM-001</td>
                  <td>用户活跃度</td>
                  <td><span class="tag tag-primary">数值型</span></td>
                  <td>用户行为表</td>
                  <td>2023-07-01</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editDimensionModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfirmModal"><i class="fas fa-trash"></i> 删除</button><button class="btn" style="color: var(--info-color);" data-modal-target="statAnalysisModal"><i class="fas fa-chart-bar"></i> 统计分析</button>
                  </td>
                </tr>
                <tr>
                  <td>DIM-002</td>
                  <td>业务类型</td>
                  <td><span class="tag tag-info">枚举型</span></td>
                  <td>业务数据表</td>
                  <td>2023-07-05</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editDimensionModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfirmModal"><i class="fas fa-trash"></i> 删除</button><button class="btn" style="color: var(--info-color);" data-modal-target="statAnalysisModal"><i class="fas fa-chart-bar"></i> 统计分析</button>
                  </td>
                </tr>
                <tr>
                  <td>DIM-003</td>
                  <td>操作时间</td>
                  <td><span class="tag tag-warning">日期型</span></td>
                  <td>系统日志表</td>
                  <td>2023-07-08</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editDimensionModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfirmModal"><i class="fas fa-trash"></i> 删除</button><button class="btn" style="color: var(--info-color);" data-modal-target="statAnalysisModal"><i class="fas fa-chart-bar"></i> 统计分析</button>
                  </td>
                </tr>
                <tr>
                  <td>DIM-004</td>
                  <td>地域分布</td>
                  <td><span class="tag tag-info">枚举型</span></td>
                  <td>用户信息表</td>
                  <td>2023-07-12</td>
                  <td><span class="tag tag-warning">未启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editDimensionModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--danger-color);" data-modal-target="deleteConfirmModal"><i class="fas fa-trash"></i> 删除</button><button class="btn" style="color: var(--info-color);" data-modal-target="statAnalysisModal"><i class="fas fa-chart-bar"></i> 统计分析</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 报表管理 -->
    <div class="tab-content" id="report-management">
   
    <!-- 功能说明和刷新按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="font-size: 14px; color: var(--text-secondary);">
        <i class="fas fa-info-circle"></i> 统计分析功能帮助您从多维度分析业务数据，生成可视化报表并支持数据导出
      </div>
      <div style="display: flex;">
        <button class="btn btn-primary" id="refreshDataBtn"><i class="fas fa-sync-alt"></i> 刷新数据</button>
      </div>
    </div>

      <div class="card">
        <div class="card-header">
          <div class="card-title"><i class="fas fa-file-alt"></i> 报表管理</div>
          <button class="btn btn-primary" id="createReportBtn"><i class="fas fa-plus"></i> 创建报表</button>
        </div>
        <div class="card-body">
          <div style="margin-bottom: 20px; display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div class="form-group">
              <label for="reportCycle">生成周期</label>
              <select id="reportCycle" name="reportCycle">
                <option value="daily">每日</option>
                <option value="weekly">每周</option>
                <option value="monthly">每月</option>
                <option value="quarterly">每季度</option>
                <option value="yearly">每年</option>
              </select>
            </div>
            <div class="form-group">
              <label for="reportFormat">报表格式</label>
              <select id="reportFormat" name="reportFormat">
                <option value="excel">Excel</option>
                <option value="pdf">PDF</option>
                <option value="html">HTML</option>
              </select>
            </div>
            <div class="form-group">
              <label for="reportRecipients">抄送列表</label>
              <input type="text" id="reportRecipients" name="reportRecipients" placeholder="请输入邮箱，多个用逗号分隔">
            </div>
          </div>
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>报表ID</th>
                  <th>报表名称</th>
                  <th>报表类型</th>
                  <th>生成周期</th>
                  <th>创建时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>REP-001</td>
                  <td>用户活跃度报表</td>
                  <td><span class="tag tag-primary">统计报表</span></td>
                  <td>每日</td>
                  <td>2023-07-01 10:00</td>
                  <td><span class="tag tag-success">正常</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="viewReportModal"><i class="fas fa-eye"></i> 查看</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadReportModal"><i class="fas fa-download"></i> 下载</button>
                  </td>
                </tr>
                <tr>
                  <td>REP-002</td>
                  <td>业务办理统计</td>
                  <td><span class="tag tag-info">汇总报表</span></td>
                  <td>每周</td>
                  <td>2023-07-05 15:30</td>
                  <td><span class="tag tag-success">正常</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="viewReportModal"><i class="fas fa-eye"></i> 查看</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadReportModal"><i class="fas fa-download"></i> 下载</button>
                  </td>
                </tr>
                <tr>
                  <td>REP-003</td>
                  <td>系统性能分析</td>
                  <td><span class="tag tag-warning">分析报表</span></td>
                  <td>每月</td>
                  <td>2023-07-10 09:45</td>
                  <td><span class="tag tag-success">正常</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="viewReportModal"><i class="fas fa-eye"></i> 查看</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadReportModal"><i class="fas fa-download"></i> 下载</button>
                  </td>
                </tr>
                <tr>
                  <td>REP-004</td>
                  <td>地域分布分析</td>
                  <td><span class="tag tag-info">汇总报表</span></td>
                  <td>每月</td>
                  <td>2023-07-12 14:20</td>
                  <td><span class="tag tag-warning">待优化</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i> 编辑</button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="viewReportModal"><i class="fas fa-eye"></i> 查看</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据导出 -->
 
<!-- 数据导出内容已迁移至模态框 -->
          <div class="tab-content" id="data-export">
              <!-- 数据概览卡片 -->
    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px;">
      <div class="stat-card">
        <div class="stat-card-title"><i class="fas fa-cubes"></i> 已定义维度</div>
        <div class="stat-card-value">12</div>
        <div class="stat-card-extra"><span class="tag tag-primary">+2 本月</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title"><i class="fas fa-chart-line"></i> 分析报表</div>
        <div class="stat-card-value">28</div>
        <div class="stat-card-extra"><span class="tag tag-success">+5 本周</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title"><i class="fas fa-file-export"></i> 数据导出</div>
        <div class="stat-card-value">45</div>
        <div class="stat-card-extra"><span class="tag tag-info">+12 本周</span></div>
      </div>
      <div class="stat-card">
        <div class="stat-card-title"><i class="fas fa-download"></i> 导出容量</div>
        <div class="stat-card-value">1.2 GB</div>
        <div class="stat-card-extra"><span class="tag tag-warning">+320 MB 较上周</span></div>
      </div>
    </div>

    <!-- 功能说明和刷新按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="font-size: 14px; color: var(--text-secondary);">
        <i class="fas fa-info-circle"></i> 统计分析功能帮助您从多维度分析业务数据，生成可视化报表并支持数据导出
      </div>
      <div style="display: flex;">
        <button class="btn btn-primary" id="refreshDataBtn"><i class="fas fa-sync-alt"></i> 刷新数据</button>
      </div>
    </div>

            <div class="card">
              <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                <div class="card-title"><i class="fas fa-history"></i> 导出历史</div>
                <div style="display: flex; gap: 10px;">
                 <!-- <input type="text" class="form-control" placeholder="搜索导出历史..." style="width: 200px;">-->
                  <input type="text" class="form-control" placeholder="搜索导出历史..." style="width: 200px;" id="exportHistorySearch">
                  <!-- <button class="btn btn-secondary"><i class="fas fa-search"></i> 查询</button>-->
                  <button class="btn btn-primary" data-modal-target="dataExportModal"><i class="fas fa-download"></i> 数据导出</button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-container">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>导出ID</th>
                        <th>导出名称</th>
                        <th>导出时间</th>
                        <th>文件大小</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>EXP-001</td>
                        <td>用户活跃度数据</td>
                        <td>2023-07-16 14:30</td>
                        <td>2.4 MB</td>
                        <td><span class="tag tag-success">完成</span></td>
                        <td>
                          <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadFileModal"><i class="fas fa-download"></i> 下载</button>
                        </td>
                      </tr>
                      <tr>
                        <td>EXP-002</td>
                        <td>业务数据汇总</td>
                        <td>2023-07-15 09:15</td>
                        <td>8.7 MB</td>
                        <td><span class="tag tag-success">完成</span></td>
                        <td>
                          <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadFileModal"><i class="fas fa-download"></i> 下载</button>
                        </td>
                      </tr>
                      <tr>
                        <td>EXP-003</td>
                        <td>系统性能数据</td>
                        <td>2023-07-10 16:40</td>
                        <td>5.2 MB</td>
                        <td><span class="tag tag-success">完成</span></td>
                        <td>
                          <button class="btn" style="color: var(--primary-color);" data-modal-target="downloadFileModal"><i class="fas fa-download"></i> 下载</button>
                        </td>
                      </tr>
                      <tr>
                        <td>EXP-004</td>
                        <td>多维度分析数据</td>
                        <td>2023-07-17 10:05</td>
                        <td>-</td>
                        <td><span class="tag tag-info">处理中</span></td>
                        <td>
                          <button class="btn" style="color: var(--warning-color);" data-modal-target="cancelExportModal"><i class="fas fa-stop"></i> 取消</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 模态框 - 新增/编辑维度 -->
  <div class="modal" id="editDimensionModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑统计维度</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="editDimensionForm">
          <div class="form-group">
            <label for="dimensionName">维度名称</label>
            <input type="text" id="dimensionName" name="dimensionName" required placeholder="请输入维度名称">
          </div>
          <div class="form-group">
            <label for="dimensionType">维度类型</label>
            <select id="dimensionType" name="dimensionType" required>
              <option value="numeric">数值型</option>
              <option value="string">字符型</option>
              <option value="date">日期型</option>
              <option value="enum">枚举型</option>
            </select>
          </div>
          <div class="form-group">
            <label for="relatedTable">关联数据表</label>
            <select id="relatedTable" name="relatedTable" required>
              <option value="user_behavior">用户行为表</option>
              <option value="business_data">业务数据表</option>
              <option value="system_log">系统日志表</option>
              <option value="user_info">用户信息表</option>
              <option value="custom">自定义表</option>
            </select>
          </div>
          <div class="form-group">
            <label for="dimensionDesc">维度描述</label>
            <textarea id="dimensionDesc" name="dimensionDesc" rows="3" placeholder="请输入维度描述信息"></textarea>
          </div>
          <div class="form-group">
            <label for="dimensionLevel">维度层级</label>
            <select id="dimensionLevel" name="dimensionLevel">
              <option value="1">一级维度</option>
              <option value="2">二级维度</option>
              <option value="3">三级维度</option>
            </select>
          </div>
          <div class="form-group">
            <label for="parentDimension">父维度</label>
            <select id="parentDimension" name="parentDimension">
              <option value="">无</option>
              <option value="user">用户维度</option>
              <option value="business">业务维度</option>
              <option value="time">时间维度</option>
            </select>
          </div>
          <div class="form-group">
            <label for="relatedIndicators">关联指标</label>
            <select id="relatedIndicators" name="relatedIndicators" multiple style="height: 100px;">
              <option value="active_users">活跃用户数</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('editDimensionModal')">取消</button>
        <button class="btn btn-primary">保存</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 新增维度 -->
  <div class="modal" id="addDimensionModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增统计维度</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addDimensionForm">
          <div class="form-group">
            <label for="newDimensionName">维度名称</label>
            <input type="text" id="newDimensionName" name="newDimensionName" required placeholder="请输入维度名称">
          </div>
          <div class="form-group">
            <label for="newDimensionType">维度类型</label>
            <select id="newDimensionType" name="newDimensionType" required>
              <option value="numeric">数值型</option>
              <option value="string">字符型</option>
              <option value="date">日期型</option>
              <option value="enum">枚举型</option>
            </select>
          </div>
          <div class="form-group">
            <label for="newRelatedTable">关联数据表</label>
            <select id="newRelatedTable" name="newRelatedTable" required>
              <option value="user_behavior">用户行为表</option>
              <option value="business_data">业务数据表</option>
              <option value="system_log">系统日志表</option>
              <option value="user_info">用户信息表</option>
              <option value="custom">自定义表</option>
            </select>
          </div>
          <div class="form-group">
            <label for="newDimensionDesc">维度描述</label>
            <textarea id="newDimensionDesc" name="newDimensionDesc" rows="3" placeholder="请输入维度描述信息"></textarea>
          </div>
          <div class="form-group">
            <label for="newDimensionLevel">维度层级</label>
            <select id="newDimensionLevel" name="newDimensionLevel">
              <option value="1">一级维度</option>
              <option value="2">二级维度</option>
              <option value="3">三级维度</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('addDimensionModal')">取消</button>
        <button class="btn btn-primary">创建</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 删除确认 -->
  <div class="modal" id="deleteConfirmModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-exclamation-triangle"></i> 确认删除</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p style="margin: 0; padding: 10px 0;">您确定要删除该维度吗？此操作不可撤销，删除后将影响相关的统计分析和报表。</p>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('deleteConfirmModal')">取消</button>
        <button class="btn" style="background-color: var(--danger-color); color: white;">确认删除</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 导出图表 -->
  <div class="modal" id="exportChartModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-download"></i> 导出图表</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>选择图表</label>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 维度分析图表
            </label>
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 趋势分析图表
            </label>
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 分布分析图表
            </label>
          </div>
        </div>
        <div class="form-group">
          <label>导出格式</label>
          <div style="display: flex; gap: 15px;">
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" checked style="margin-right: 5px;"> PNG
            </label>
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" style="margin-right: 5px;"> JPG
            </label>
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" style="margin-right: 5px;"> PDF
            </label>
          </div>
        </div>
        <div class="form-group">
          <label for="chartQuality">图片质量</label>
          <select id="chartQuality">
            <option value="low">低 (快速导出)</option>
            <option value="medium" selected>中 (平衡)</option>
            <option value="high">高 (高质量)</option>
          </select>
        </div>
      </div>
      </div>
      </div>
    </div>
  </div>

  <!-- 统计分析弹窗 -->
  <div class="modal" id="statAnalysisModal">
    <div class="modal-content" style="width: 90%; max-width: 1200px;">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-chart-line"></i> 统计分析</div>
        <button class="modal-close" onclick="closeModal('statAnalysisModal')">&times;</button>
      </div>
      <div class="modal-body">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div class="chart-container">
            <canvas id="dimensionAnalysisChart" height="300"></canvas>
          </div>
          <div class="chart-container">
            <canvas id="trendAnalysisChart" height="300"></canvas>
          </div>
        </div>
        <div style="margin-top: 20px;">
          <div style="margin-bottom: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div class="form-group">
              <label for="distributionDimension">分布维度</label>
              <select id="distributionDimension" name="distributionDimension">
                <option value="region">地域分布</option>
                <option value="user_type">用户类型</option>
                <option value="business_type">业务类型</option>
                <option value="time_period">时间段</option>
              </select>
            </div>
            <div class="form-group">
              <label>&nbsp;</label>
              <button class="btn btn-primary" style="width: 100%;" id="analyzeDistributionBtn">分析分布</button>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="distributionChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('statAnalysisModal')">关闭</button>
        <button class="btn btn-primary" data-modal-target="exportChartModal"><i class="fas fa-download"></i> 导出图表</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 导出图表 -->
  <div class="modal" id="exportChartModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-download"></i> 导出图表</div>
        <button class="modal-close" onclick="closeModal('exportChartModal')">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>选择图表</label>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 维度分析图表
            </label>
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 趋势分析图表
            </label>
            <label style="display: flex; align-items: center;">
              <input type="checkbox" checked style="margin-right: 5px;"> 分布分析图表
            </label>
          </div>
        </div>
        <div class="form-group">
          <label>导出格式</label>
          <div style="display: flex; gap: 15px;">
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" checked style="margin-right: 5px;"> PNG
            </label>
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" style="margin-right: 5px;"> JPG
            </label>
            <label style="display: flex; align-items: center;">
              <input type="radio" name="chart-format" style="margin-right: 5px;"> PDF
            </label>
          </div>
        </div>
        <div class="form-group">
          <label for="chartQuality">图片质量</label>
          <select id="chartQuality">
            <option value="low">低 (快速导出)</option>
            <option value="medium" selected>中 (平衡)</option>
            <option value="high">高 (高质量)</option>
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('exportChartModal')">取消</button>
        <button class="btn btn-primary">确认导出</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 创建报表 -->
  <div class="modal" id="createReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 创建报表</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="createReportForm">
          <div class="form-group">
            <label for="reportName">报表名称</label>
            <input type="text" id="reportName" required placeholder="请输入报表名称">
          </div>
          <div class="form-group">
            <label for="reportType">报表类型</label>
            <select id="reportType" required>
              <option value="statistic">统计报表</option>
              <option value="summary">汇总报表</option>
              <option value="analysis">分析报表</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportCycleNew">生成周期</label>
            <select id="reportCycleNew" required>
              <option value="daily">每日</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="quarterly">每季度</option>
              <option value="yearly">每年</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportDimensions">包含维度</label>
            <select id="reportDimensions" multiple style="height: 100px;">
              <option value="DIM-001">用户活跃度</option>
              <option value="DIM-002">业务类型</option>
              <option value="DIM-003">操作时间</option>
              <option value="DIM-004">地域分布</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportDesc">报表描述</label>
            <textarea id="reportDesc" rows="3" placeholder="请输入报表描述信息"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('createReportModal')">取消</button>
        <button class="btn btn-primary">创建</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 编辑报表 -->
  <div class="modal" id="editReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑报表</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="editReportForm">
          <div class="form-group">
            <label for="editReportName">报表名称</label>
            <input type="text" id="editReportName" required placeholder="请输入报表名称">
          </div>
          <div class="form-group">
            <label for="editReportCycle">生成周期</label>
            <select id="editReportCycle" required>
              <option value="daily">每日</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="quarterly">每季度</option>
              <option value="yearly">每年</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editReportFormat">报表格式</label>
            <select id="editReportFormat" required>
              <option value="excel">Excel</option>
              <option value="pdf">PDF</option>
              <option value="html">HTML</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editReportRecipients">抄送列表</label>
            <input type="text" id="editReportRecipients" placeholder="请输入邮箱，多个用逗号分隔">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('editReportModal')">取消</button>
        <button class="btn btn-primary">保存</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 查看报表 -->
  <div class="modal" id="viewReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-eye"></i> 查看报表</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div style="text-align: center; padding: 20px 0;">
          <div style="margin-bottom: 20px; font-size: 18px; font-weight: 600;">用户活跃度报表</div>
          <div style="background-color: var(--bg-color); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <canvas id="reportChart" height="300"></canvas>
          </div>
          <div style="text-align: left; margin-top: 20px;">
            <div style="margin-bottom: 10px; font-weight: 500;">报表信息：</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <div>报表ID：REP-001</div>
              <div>生成时间：2023-07-17 09:00</div>
              <div>生成周期：每日</div>
              <div>文件格式：Excel</div>
              <div>数据范围：2023-06-17 至 2023-07-17</div>
              <div>状态：正常</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('viewReportModal')">关闭</button>
        <button class="btn btn-primary" data-modal-target="downloadReportModal">下载报表</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 下载报表 -->
  <div class="modal" id="downloadReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-download"></i> 下载报表</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>报表信息</label>
          <div style="background-color: var(--bg-color); padding: 15px; border-radius: 4px;">
            <div style="margin-bottom: 5px;">名称：用户活跃度报表 (REP-001)</div>
            <div style="margin-bottom: 5px;">生成时间：2023-07-17 09:00</div>
            <div>文件大小：2.4 MB</div>
          </div>
        </div>
        <div class="form-group">
          <label>下载格式</label>
          <div style="display: flex; gap: 15px;">
            <label style="display: flex; align-items: center;">
              <input type="radio" name="report-download-format" checked style="margin-right: 5px;"> Excel (.xlsx)
            </label>
            <label style="display: flex; align-items: center;">
              <input type="radio" name="report-download-format" style="margin-right: 5px;"> PDF (.pdf)
            </label>
          </div>
        </div>
        <div class="form-group">
          <label for="downloadPassword">下载密码（可选）</label>
          <input type="password" id="downloadPassword" placeholder="为下载文件设置密码保护">
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('downloadReportModal')">取消</button>
        <button class="btn btn-primary">确认下载</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 确认导出 -->
  <div class="modal" id="confirmExportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-exclamation-circle"></i> 确认数据导出</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p>您即将导出以下数据：</p>
        <div style="background-color: var(--bg-color); padding: 15px; border-radius: 4px; margin: 10px 0;">
          <div style="margin-bottom: 5px;"><strong>导出维度：</strong>用户活跃度、业务类型</div>
          <div style="margin-bottom: 5px;"><strong>时间范围：</strong>2023-06-17 至 2023-07-17</div>
          <div><strong>文件格式：</strong>Excel</div>
        </div>
        <p style="color: var(--warning-color);"><i class="fas fa-info-circle"></i> 数据导出可能需要几分钟时间，请耐心等待。大型数据集可能需要更长时间。</p>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('confirmExportModal')">取消</button>
        <button class="btn btn-primary">开始导出</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 下载文件 -->
  <div class="modal" id="downloadFileModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-download"></i> 下载文件</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>文件信息</label>
          <div style="background-color: var(--bg-color); padding: 15px; border-radius: 4px;">
            <div style="margin-bottom: 5px;">名称：用户活跃度数据 (EXP-001)</div>
            <div style="margin-bottom: 5px;">创建时间：2023-07-16 14:30</div>
            <div style="margin-bottom: 5px;">文件大小：2.4 MB</div>
            <div>格式：Excel</div>
          </div>
        </div>
        <div class="form-group">
          <label for="downloadExpiration">下载链接有效期</label>
          <select id="downloadExpiration">
            <option value="1h">1小时</option>
            <option value="24h" selected>24小时</option>
            <option value="7d">7天</option>
          </select>
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" checked style="margin-right: 5px;"> 下载后发送通知到我的邮箱
          </label>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('downloadFileModal')">取消</button>
        <button class="btn btn-primary">下载文件</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 取消导出 -->
  <div class="modal" id="cancelExportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-exclamation-triangle"></i> 确认取消导出</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p>您确定要取消当前正在进行的导出任务吗？</p>
        <p style="color: var(--text-secondary); margin-top: 10px;">任务名称：多维度分析数据 (EXP-004)</p>
        <p style="color: var(--warning-color); margin-top: 10px;"><i class="fas fa-info-circle"></i> 取消后，已处理的数据将被丢弃，需要重新发起导出。</p>
      </div>
      <div class="modal-footer">
        <button class="btn" style="background-color: var(--bg-color);" onclick="closeModal('cancelExportModal')">继续导出</button>
        <button class="btn" style="background-color: var(--danger-color); color: white;">确认取消</button>
      </div>
    </div>
  </div>

  <!-- 数据导出模态框 -->
  <div class="modal" id="dataExportModal">
    <div class="modal-content" style="width: 600px;">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-download"></i> 数据导出配置</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="config-item" style="margin-bottom: 15px;">
          <div class="config-label">导出任务名称</div>
          <input type="text" id="exportTaskName" placeholder="请输入导出任务名称" style="width: 100%;">
        </div>
        <div style="display: flex; gap: 20px;">
          <div style="flex: 1;">
            <div class="config-item">
              <div class="config-label">维度选择</div>
              <select multiple style="width: 100%; height: 120px;">
                <option selected>用户活跃度</option>
                <option selected>业务类型</option>
                <option>操作时间</option>
                <option>地域分布</option>
                <option>用户年龄段</option>
                <option>消费金额</option>
              </select>
              <div style="margin-top: 8px; font-size: 12px; color: var(--text-secondary);">
                <i class="fas fa-info-circle"></i> 按住Ctrl键可多选
              </div>
            </div>
          </div>
          <div style="flex: 1;">
            <div class="config-item">
              <div class="config-label">时间范围</div>
              <div style="display: flex; gap: 10px;">
                <input type="date" style="width: 50%;" value="2023-06-17">
                <input type="date" style="width: 50%;" value="2023-07-17">
              </div>
            </div>
            <div class="config-item">
              <div class="config-label">数据格式</div>
              <div style="display: flex; gap: 15px;">
                <label style="display: flex; align-items: center;">
                  <input type="radio" name="export-format" checked style="margin-right: 5px;"> Excel
                </label>
                <label style="display: flex; align-items: center;">
                  <input type="radio" name="export-format" style="margin-right: 5px;"> CSV
                </label>
                <label style="display: flex; align-items: center;">
                  <input type="radio" name="export-format" style="margin-right: 5px;"> PDF
                </label>
              </div>
            </div>
            <div class="config-item">
              <div class="config-label">导出状态</div>
              <select id="exportStatus" style="width: 100%;">
                <option value="pending">待处理</option>
                <option value="processing">处理中</option>
                <option value="completed">已完成</option>
                <option value="failed">已失败</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="justify-content: center; padding: 15px;">
        <button class="btn btn-primary" style="padding: 8px 30px;" id="startExportBtn" data-modal-target="confirmExportModal"><i class="fas fa-download"></i> 开始导出</button>
      </div>
    </div>
  </div>

  <!-- 模态框 - 刷新数据提示 -->
  <div class="modal" id="refreshDataModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-sync-alt"></i> 数据刷新中</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body" style="text-align: center; padding: 30px 0;">
        <div class="loading"></div>
        <p style="margin-top: 20px;">正在刷新数据，请稍候...</p>
        <p style="color: var(--text-secondary); font-size: 12px; margin-top: 10px;">这可能需要几秒钟时间，取决于数据量大小</p>
      </div>
    </div>
  </div>
  <script src="js/common.js"></script>
  <script>
    // 初始化图表
    function initCharts() {
      // 维度分析图表
      const dimensionCtx = document.getElementById('dimensionAnalysisChart').getContext('2d');
      new Chart(dimensionCtx, {
        type: 'bar',
        data: {
          labels: ['用户活跃度', '业务类型', '操作时间', '地域分布'],
          datasets: [{
            label: '数据量',
            data: [1250, 890, 2100, 750],
            backgroundColor: '#165DFF'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });

      // 趋势分析图表
      const trendCtx = document.getElementById('trendAnalysisChart').getContext('2d');
      new Chart(trendCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '趋势数据',
            data: [65, 78, 52, 91, 85, 105],
            borderColor: '#165DFF',
            tension: 0.3
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });

      // 分布分析图表
      const distributionCtx = document.getElementById('distributionChart').getContext('2d');
      new Chart(distributionCtx, {
        type: 'pie',
        data: {
          labels: ['华东', '华北', '华南', '西部', '东北'],
          datasets: [{
            data: [35, 25, 20, 15, 5],
            backgroundColor: ['#165DFF', '#00B42A', '#FF7D00', '#F53F3F', '#86909C']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });

      // 报表查看图表
      const reportCtx = document.getElementById('reportChart').getContext('2d');
      new Chart(reportCtx, {
        type: 'line',
        data: {
          labels: ['6/17', '6/22', '6/27', '7/2', '7/7', '7/12', '7/17'],
          datasets: [{
            label: '用户活跃度',
            data: [65, 72, 68, 85, 92, 88, 95],
            borderColor: '#165DFF',
            tension: 0.3
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
    }

    // 打开模态框
    function openModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('show');
      }
    }

    // 关闭模态框
    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.remove('show');
      }
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const modals = document.getElementsByClassName('modal');
      for (let i = 0; i < modals.length; i++) {
        const modal = modals[i];
        if (event.target === modal && modal.classList.contains('show')) {
          modal.classList.remove('show');
        }
      }
    }

    // 为带data-modal-target属性的按钮添加点击事件
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化图表
      initCharts();

      // 为所有带data-modal-target属性的按钮添加点击事件
      const modalButtons = document.querySelectorAll('[data-modal-target]');
      modalButtons.forEach(button => {
        button.addEventListener('click', function() {
          const targetModal = this.getAttribute('data-modal-target');
          openModal(targetModal);
        });
      });

      // 为所有关闭按钮添加点击事件
      const closeButtons = document.querySelectorAll('.modal-close');
      closeButtons.forEach(button => {
        button.addEventListener('click', function() {
          const modal = this.closest('.modal');
          if (modal) {
            modal.classList.remove('show');
          }
        });
      });

      // 为新增维度按钮添加事件
      document.getElementById('addDimensionBtn').addEventListener('click', function() {
        openModal('addDimensionModal');
      });

      // 维度搜索框模糊查询功能
      document.getElementById('dimensionSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        const dimensionTable = document.querySelector('#stat-dimension .table-container table');
        
        if (dimensionTable) {
          const rows = dimensionTable.querySelectorAll('tbody tr');
          rows.forEach(row => {
            const dimensionName = row.textContent.toLowerCase();
            row.style.display = dimensionName.includes(searchTerm) ? '' : 'none';
          });
        }
      });

      // 为创建报表按钮添加事件
      document.getElementById('createReportBtn').addEventListener('click', function() {
        openModal('createReportModal');
      });

      // 为刷新数据按钮添加事件
      document.getElementById('refreshDataBtn').addEventListener('click', function() {
        openModal('refreshDataModal');
        
        // 模拟数据刷新
        setTimeout(function() {
          closeModal('refreshDataModal');
          // 显示刷新成功提示（可以添加一个提示模态框）
        }, 2000);
      });

      // 为分析分布按钮添加事件
      document.getElementById('analyzeDistributionBtn').addEventListener('click', function() {
        // 这里可以添加分析分布的逻辑
        alert('正在分析分布数据，请查看图表区域');
      });

      // 选项卡切换功能
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有选项卡的active类
          tabs.forEach(t => t.classList.remove('active'));
          // 给当前点击的选项卡添加active类
          this.classList.add('active');
          
          // 隐藏所有内容区域
          const tabContents = document.querySelectorAll('.tab-content');
          tabContents.forEach(content => {
            content.style.display = 'none';
          });
          
          // 显示对应的内容区域
          const targetId = this.getAttribute('data-tab-target');
          document.getElementById(targetId).style.display = 'block';
        });
      });
    });
  </script>

  <!-- 在页面底部添加JavaScript代码 -->
<script>
// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 获取搜索输入框和表格数据行
  const searchInput = document.getElementById('exportHistorySearch');
  const tableRows = document.querySelectorAll('#data-export table tbody tr');
  
  // 为搜索框添加输入事件监听
  searchInput.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase().trim();
    
    // 遍历所有表格行，根据搜索词显示或隐藏
    tableRows.forEach(row => {
      // 获取当前行的文本内容（导出ID和导出名称）
      const exportId = row.querySelector('td:first-child').textContent.toLowerCase();
      const exportName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
      
      // 检查是否匹配搜索词
      const isMatch = exportId.includes(searchTerm) || exportName.includes(searchTerm);
      
      // 根据匹配结果显示或隐藏行
      row.style.display = isMatch ? '' : 'none';
    });
  });
});
</script>

</body>
</html>
