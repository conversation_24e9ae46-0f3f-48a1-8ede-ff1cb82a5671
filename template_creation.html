<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 模板创建</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#165DFF',
              secondary: '#4080FF',
              success: '#00B42A',
              warning: '#FF7D00',
              danger: '#F53F3F',
              info: '#86909C',
              light: '#F2F3F5',
              dark: '#1D2129',
            },
            fontFamily: {
              inter: ['Inter', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .menu-active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          border-left: 4px solid var(--primary-color);
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
          transition: all 200ms;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-secondary {
          background-color: white;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
          transition: all 200ms;
        }
        .btn-secondary:hover {
          background-color: rgba(24, 144, 255, 0.05);
        }
        .panel {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #f3f4f6;
        }
        .panel-header {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .panel-body {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    <div class="flex  overflow-hidden" style="margin-top: 64px;">
      <!-- 侧边栏 -->
      <!-- <div class="w-64 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
            <div class="py-4">
                <a href="index.html" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-home w-5 h-5 mr-3"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-database w-5 h-5 mr-3"></i>
                    <span>数据融通</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-chart-pie w-5 h-5 mr-3"></i>
                    <span>智能洞察分析</span>
                </a>
                <div class="relative">
                    <button class="flex items-center justify-between w-full px-6 py-3 text-primary bg-primary/10 border-l-4 border-primary">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                            <span>运营视图</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="ml-6 mt-1 border-l border-gray-200 pl-4 space-y-1">
                        <a href="#" class="block py-2 text-sm text-gray-600 hover:text-primary transition-colors duration-200">大屏模板</a>
                        <div class="relative">
                            <button class="flex items-center justify-between w-full py-2 text-sm text-primary">
                                <span>画布管理</span>
                                <i class="fas fa-chevron-right text-xs"></i>
                            </button>
                        </div>
                        <a href="#" class="block py-2 text-sm text-gray-600 hover:text-primary transition-colors duration-200">组件库</a>
                        <div class="relative">
                            <button class="flex items-center justify-between w-full py-2 text-sm text-primary">
                                <span>模板管理</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="ml-4 mt-1 border-l border-gray-200 pl-3 space-y-1">
                                <a href="template_creation.html" class="block py-1.5 text-xs text-primary font-medium">模板创建</a>
                                <a href="template_editing.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板编辑</a>
                                <a href="template_list.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板列表</a>
                                <a href="template_deletion.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板删除</a>
                                <a href="template_copy.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板复制</a>
                                <a href="template_query.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板查询</a>
                                <a href="template_permission.html" class="block py-1.5 text-xs text-gray-600 hover:text-primary transition-colors duration-200">模板权限控制</a>
                            </div>
                        </div>
                        <a href="#" class="block py-2 text-sm text-gray-600 hover:text-primary transition-colors duration-200">自定义报表</a>
                        <a href="#" class="block py-2 text-sm text-gray-600 hover:text-primary transition-colors duration-200">智能问数</a>
                    </div>
                </div>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                    <span>统一运营门户</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-tasks w-5 h-5 mr-3"></i>
                    <span>五级穿透调度</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-server w-5 h-5 mr-3"></i>
                    <span>微服务管理</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-user-shield w-5 h-5 mr-3"></i>
                    <span>权限管理</span>
                </a>
                <a href="#" class="flex items-center px-6 py-3 text-gray-600 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-cog w-5 h-5 mr-3"></i>
                    <span>系统设置</span>
                </a>
            </div>
        </div> -->

      <!-- 主内容区 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500 mb-6">
          <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <span class="text-primary">模板创建</span>
        </div>

        <!-- 页面标题 -->
        <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板创建</h1>

        <!-- 进度条 -->
        <div class="mb-8 panel">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">创建进度</h2>
            <span class="text-xs text-gray-500">3/3 阶段</span>
          </div>
          <div class="panel-body">
            <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
              <div class="absolute top-0 left-0 h-full bg-primary rounded-full" style="width: 100%"></div>
            </div>
            <div class="flex justify-between mt-2">
              <div class="flex flex-col items-center">
                <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-xs mb-1">1</div>
                <span class="text-xs text-primary font-medium">模板配置</span>
              </div>
              <div class="flex flex-col items-center">
                <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-xs mb-1">2</div>
                <span class="text-xs text-primary font-medium">内容编辑</span>
              </div>
              <div class="flex flex-col items-center">
                <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-xs mb-1">3</div>
                <span class="text-xs text-primary font-medium">预览发布</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具栏 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6 flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center gap-3">
            <button class="px-4 py-2 rounded-lg btn-primary flex items-center gap-2" onclick="createTemplate('template_List.html')">
              <i class="fas fa-save"></i>
              <span>保存草稿</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-eye"></i>
              <span>预览</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-code"></i>
              <span>代码模式</span>
            </button>
          </div>
          <div class="flex items-center gap-3">
            <button class="px-4 py-2 rounded-lg bg-success text-white hover:bg-success/90 transition-all duration-200 flex items-center gap-2" onclick="createTemplate('template_List.html')">
              <i class="fas fa-paper-plane"></i>
              <span>发布</span>
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧：模板属性配置 -->
          <div class="lg:col-span-1 space-y-6">
            <!-- 基本信息 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">基本信息</h2>
              </div>
              <div class="panel-body space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    模板名称
                    <span class="text-danger">*</span>
                  </label>
                  <input type="text" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" placeholder="请输入模板名称" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">模板描述</label>
                  <textarea rows="3" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" placeholder="请输入模板描述"></textarea>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    模板类型
                    <span class="text-danger">*</span>
                  </label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>请选择模板类型</option>
                    <option>全国模板</option>
                    <option>分省模板</option>
                    <option>市级模板</option>
                    <option>区县级模板</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">行业分类</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>请选择行业</option>
                    <option>通用</option>
                    <option>零售行业</option>
                    <option>金融行业</option>
                    <option>制造业</option>
                    <option>服务业</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">模板标签</label>
                  <div class="flex flex-wrap gap-2 mt-1">
                    <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full flex items-center gap-1">
                      数据监控
                      <button class="text-primary/70 hover:text-primary"><i class="fas fa-times text-xs"></i></button>
                    </span>
                    <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full flex items-center gap-1">
                      实时分析
                      <button class="text-primary/70 hover:text-primary"><i class="fas fa-times text-xs"></i></button>
                    </span>
                    <input type="text" placeholder="添加标签" class="px-3 py-1 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 text-sm w-24" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 高级设置 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">高级设置</h2>
              </div>
              <div class="panel-body space-y-4">
                <div>
                  <label class="flex items-center justify-between text-sm font-medium text-gray-700 mb-1">
                    <span>多环境配置</span>
                    <div class="relative inline-block h-5 w-9">
                      <input id="env-toggle" type="checkbox" class="peer sr-only" checked />
                      <label
                        for="env-toggle"
                        class="peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary/30 peer-focus:ring-offset-2 inline-flex items-center cursor-pointer bg-gray-200 peer-focus:bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"
                      ></label>
                    </div>
                  </label>
                  <div class="mt-2 grid grid-cols-2 gap-3">
                    <div class="flex items-center space-x-2">
                      <input type="checkbox" id="test-env" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" checked />
                      <label for="test-env" class="text-xs text-gray-700">测试环境</label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input type="checkbox" id="prod-env" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" checked />
                      <label for="prod-env" class="text-xs text-gray-700">生产环境</label>
                    </div>
                  </div>
                </div>
                <div>
                  <label class="flex items-center justify-between text-sm font-medium text-gray-700 mb-1">
                    <span>权限分级控制</span>
                    <div class="relative inline-block h-5 w-9">
                      <input id="permission-toggle" type="checkbox" class="peer sr-only" />
                      <label
                        for="permission-toggle"
                        class="peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary/30 peer-focus:ring-offset-2 inline-flex items-center cursor-pointer bg-gray-200 peer-focus:bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"
                      ></label>
                    </div>
                  </label>
                </div>
                <div>
                  <label class="flex items-center justify-between text-sm font-medium text-gray-700 mb-1">
                    <span>自动保存草稿</span>
                    <div class="relative inline-block h-5 w-9">
                      <input id="auto-save-toggle" type="checkbox" class="peer sr-only" checked />
                      <label
                        for="auto-save-toggle"
                        class="peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary/30 peer-focus:ring-offset-2 inline-flex items-center cursor-pointer bg-gray-200 peer-focus:bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"
                      ></label>
                    </div>
                  </label>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">保存间隔</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>5分钟</option>
                    <option>10分钟</option>
                    <option>15分钟</option>
                    <option>30分钟</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间：编辑器区域 -->
          <div class="lg:col-span-2 space-y-6">
            <!-- 编辑器工具栏 -->
            <div class="panel">
              <div class="panel-header border-b-0 p-2 bg-gray-50 flex flex-wrap gap-1">
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="段落样式"><i class="fas fa-paragraph"></i></button>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>正文</option>
                  <option>标题 1</option>
                  <option>标题 2</option>
                  <option>标题 3</option>
                </select>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>默认字体</option>
                  <option>Arial</option>
                  <option>宋体</option>
                  <option>黑体</option>
                </select>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>12px</option>
                  <option>14px</option>
                  <option>16px</option>
                  <option>18px</option>
                  <option>20px</option>
                </select>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="粗体"><i class="fas fa-bold"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="斜体"><i class="fas fa-italic"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="下划线"><i class="fas fa-underline"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="删除线"><i class="fas fa-strikethrough"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="左对齐"><i class="fas fa-align-left"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="居中对齐"><i class="fas fa-align-center"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="右对齐"><i class="fas fa-align-right"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="两端对齐"><i class="fas fa-align-justify"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="无序列表"><i class="fas fa-list-ul"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="有序列表"><i class="fas fa-list-ol"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="减少缩进"><i class="fas fa-outdent"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="增加缩进"><i class="fas fa-indent"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入链接"><i class="fas fa-link"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入图片"><i class="fas fa-image"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入表格"><i class="fas fa-table"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入代码"><i class="fas fa-code"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="撤销"><i class="fas fa-undo"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="重做"><i class="fas fa-redo"></i></button>
              </div>
              <!-- 编辑器内容区 -->
              <div class="panel-body p-0 min-h-[400px] bg-gray-50 border border-gray-200 rounded-b-lg overflow-hidden">
                <div class="p-4 h-full min-h-[400px]" contenteditable="true" spellcheck="false" class="focus:outline-none prose max-w-none">
                  <h2>全国运营监控大屏</h2>
                  <p>本模板用于展示全国范围内的运营数据监控，包含以下几个主要部分：</p>
                  <ul>
                    <li>实时销售数据概览</li>
                    <li>区域分布热力图</li>
                    <li>用户增长趋势分析</li>
                    <li>热门产品排行榜</li>
                    <li>客户满意度指标</li>
                  </ul>
                  <p>您可以通过拖拽右侧组件库中的元素到画布中来自定义模板内容。</p>
                </div>
              </div>
            </div>

            <!-- 组件库 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">组件库</h2>
                <div class="flex items-center gap-2">
                  <input type="text" placeholder="搜索组件" class="px-3 py-1 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200 text-sm w-32" />
                  <button class="p-1 text-gray-500 hover:text-primary"><i class="fas fa-filter"></i></button>
                </div>
              </div>
              <div class="panel-body">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                  <!-- 图表组件 -->
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-bar text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">柱状图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-line text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">折线图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-pie text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">饼图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-area text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">面积图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-map-marked-alt text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">地图</span>
                  </div>
                  <!-- 数据组件 -->
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-table text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">数据表格</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-tags text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">标签云</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-percentage text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">进度环</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-random text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">关系图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-pie text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">漏斗图</span>
                  </div>
                  <!-- 文本组件 -->
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-heading text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">标题</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-align-left text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">正文</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-quote-right text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">引用</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-list-ul text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">列表</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-table text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">表格</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部：版本历史 -->
        <div class="panel mt-6">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">版本历史</h2>
            <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200">查看全部</button>
          </div>
          <div class="panel-body">
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full bg-success/10 text-success flex items-center justify-center flex-shrink-0 mt-0.5">
                  <i class="fas fa-check text-xs"></i>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">已保存草稿</span>
                    <span class="text-xs text-gray-500">刚刚</span>
                  </div>
                  <p class="text-xs text-gray-500">系统自动保存于 2023-05-15 16:32:45</p>
                </div>
                <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200 mt-0.5">恢复</button>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center flex-shrink-0 mt-0.5">
                  <i class="fas fa-save text-xs"></i>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">手动保存</span>
                    <span class="text-xs text-gray-500">10分钟前</span>
                  </div>
                  <p class="text-xs text-gray-500">用户手动保存于 2023-05-15 16:22:18</p>
                </div>
                <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200 mt-0.5">恢复</button>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <i class="fas fa-file-alt text-xs"></i>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">创建模板</span>
                    <span class="text-xs text-gray-500">1小时前</span>
                  </div>
                  <p class="text-xs text-gray-500">模板创建于 2023-05-15 15:30:00</p>
                </div>
                <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200 mt-0.5">恢复</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览模态框 -->
    <div id="previewModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">模板预览</h3>
          <button id="closePreview" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto p-6">
          <div class="bg-gray-50 p-8 rounded-lg min-h-[400px]">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">全国运营监控大屏</h2>
            <p class="text-gray-600 mb-4">本模板用于展示全国范围内的运营数据监控，包含以下几个主要部分：</p>
            <ul class="list-disc pl-5 mb-4 space-y-1 text-gray-600">
              <li>实时销售数据概览</li>
              <li>区域分布热力图</li>
              <li>用户增长趋势分析</li>
              <li>热门产品排行榜</li>
              <li>客户满意度指标</li>
            </ul>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">销售数据概览</h3>
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-bar text-gray-300 text-5xl"></i>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">区域分布热力图</h3>
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-map-marked-alt text-gray-300 text-5xl"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button id="cancelPreview" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">取消</button>
          <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">发布</button>
        </div>
      </div>
    </div>

   <script src="js/common.js"></script>
    <script>
      function createTemplate(href) {
        window.location.href = href;
      }
      // 预览模态框交互
      document.querySelector('button:has(.fa-eye)').addEventListener('click', function () {
        document.getElementById('previewModal').classList.remove('hidden');
        document.getElementById('previewModal').classList.add('flex');
      });

      document.getElementById('closePreview').addEventListener('click', function () {
        document.getElementById('previewModal').classList.add('hidden');
        document.getElementById('previewModal').classList.remove('flex');
      });

      document.getElementById('cancelPreview').addEventListener('click', function () {
        document.getElementById('previewModal').classList.add('hidden');
        document.getElementById('previewModal').classList.remove('flex');
      });

      // 模拟编辑器保存状态
      setInterval(function () {
        const saveButton = document.querySelector('button:has(.fa-save)');
        saveButton.innerHTML = '<i class="fas fa-save"></i><span>已保存</span>';
        saveButton.classList.add('bg-success');
        saveButton.classList.remove('bg-primary');
        setTimeout(function () {
          saveButton.innerHTML = '<i class="fas fa-save"></i><span>保存草稿</span>';
          saveButton.classList.remove('bg-success');
          saveButton.classList.add('bg-primary');
        }, 2000);
      }, 30000);
    </script>
  </body>
</html>
