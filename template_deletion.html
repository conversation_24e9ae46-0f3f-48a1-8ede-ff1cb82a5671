<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 模板删除</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#165DFF',
              secondary: '#4080FF',
              success: '#00B42A',
              warning: '#FF7D00',
              danger: '#F53F3F',
              info: '#86909C',
              light: '#F2F3F5',
              dark: '#1D2129',
            },
            fontFamily: {
              inter: ['Inter', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .menu-active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          border-left: 4px solid var(--primary-color);
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
          transition: all 200ms;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-secondary {
          background-color: white;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
          transition: all 200ms;
        }
        .btn-secondary:hover {
          background-color: rgba(24, 144, 255, 0.05);
        }
        .btn-danger {
          background-color: var(--danger-color);
          color: white;
          transition: all 200ms;
        }
        .btn-danger:hover {
          background-color: rgba(255, 77, 79, 0.9);
        }
        .panel {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #f3f4f6;
        }
        .panel-header {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .panel-body {
          padding: 1rem;
        }
        .skeleton {
          animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          background-color: #f3f4f6;
          border-radius: 4px;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex overflow-hidden"  style="margin-top: 64px;">
      <!-- 侧边栏 -->

      <!-- 主内容区 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500 mb-6">
          <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <span class="text-primary">模板删除</span>
        </div>

        <!-- 页面标题 -->
        <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板删除</h1>

        <!-- 工具栏 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6 flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center gap-3">
            <button class="px-4 py-2 rounded-lg btn-danger flex items-center gap-2" onclick="createTemplate('template_List.html')">
              <i class="fas fa-trash-alt"></i>
              <span>批量删除</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-history"></i>
              <span>恢复已删除</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-filter"></i>
              <span>筛选</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-download"></i>
              <span>导出日志</span>
            </button>
          </div>
          <div class="relative">
            <input type="text" placeholder="搜索模板名称或ID" class="pl-9 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full sm:w-64" />
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        <!-- 搜索和筛选面板 -->
        <div class="panel mb-6">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">删除查询条件</h2>
            <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200">
              <i class="fas fa-sliders-h mr-1"></i>
              高级搜索
            </button>
          </div>
          <div class="panel-body grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
              <input type="text" placeholder="输入模板名称" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">删除时间范围</label>
              <div class="flex items-center gap-2 w-full">
                <input type="date" class="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
                <span class="text-gray-500">至</span>
                <input type="date" class="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">删除人</label>
              <input type="text" placeholder="输入删除人" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">模板类型</label>
              <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                <option value="">全部模板类型</option>
                <option value="1">全国模板</option>
                <option value="2">分省模板</option>
                <option value="3">市级模板</option>
                <option value="4">区县级模板</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">行业分类</label>
              <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                <option value="">全部行业</option>
                <option value="1">通用</option>
                <option value="2">零售行业</option>
                <option value="3">金融行业</option>
                <option value="4">制造业</option>
                <option value="5">服务业</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">删除状态</label>
              <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                <option value="">全部状态</option>
                <option value="1">可恢复</option>
                <option value="2">已永久删除</option>
              </select>
            </div>
            <div class="md:col-span-3 flex items-center justify-end gap-3 pt-2">
              <button class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">重置</button>
              <button class="px-4 py-2 rounded-lg btn-primary flex items-center justify-center gap-2 text-sm">
                <i class="fas fa-search"></i>
                <span>查询</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 删除进度面板 -->
        <div class="panel mb-6">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">删除进度</h2>
          </div>
          <div class="panel-body space-y-4">
            <div>
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs text-gray-500">查询阶段</span>
                <span class="text-xs font-medium text-gray-700">100%</span>
              </div>
              <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                <div class="absolute top-0 left-0 h-full bg-success rounded-full" style="width: 100%"></div>
              </div>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>总耗时: 1.8秒</span>
              <span>完成于: 刚刚</span>
            </div>
          </div>
        </div>

        <!-- 已删除模板列表 -->
        <div class="panel mb-6">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">已删除模板列表</h2>
            <div class="flex items-center gap-2">
              <button class="text-xs text-gray-500 hover:text-primary transition-colors duration-200 px-2 py-1 border border-gray-200 rounded hover:bg-gray-50 flex items-center gap-1">
                <i class="fas fa-save mr-1"></i>
                保存查询条件
              </button>
              <button class="text-xs text-gray-500 hover:text-primary transition-colors duration-200 px-2 py-1 border border-gray-200 rounded hover:bg-gray-50 flex items-center gap-1">
                <i class="fas fa-history mr-1"></i>
                历史查询
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                    <input type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" />
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板ID</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板类型</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业分类</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">删除人</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">删除时间</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余恢复时间</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-bar text-gray-400"></i>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">区域销售分析模板</div>
                        <div class="text-xs text-gray-500">v1.0.0</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230501-001</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">分省模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">零售行业</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-12 09:30:25</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-warning">2天15小时</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-primary hover:text-primary/80 transition-colors duration-200 mr-3" onclick="showRestoreModal('区域销售分析模板', 'TPL-20230501-001')">恢复</button>
                    <button class="text-danger hover:text-danger/80 transition-colors duration-200" onclick="showDeleteForeverModal('区域销售分析模板', 'TPL-20230501-001')">永久删除</button>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-pie text-gray-400"></i>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">客户满意度分析模板</div>
                        <div class="text-xs text-gray-500">v1.1.0</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230415-002</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">全国模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">服务业</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10 14:22:18</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-danger">已过期</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-gray-400 cursor-not-allowed mr-3">恢复</button>
                    <button class="text-danger hover:text-danger/80 transition-colors duration-200" onclick="showDeleteForeverModal('客户满意度分析模板', 'TPL-20230415-002')">永久删除</button>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-gray-400"></i>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">产品销量趋势模板</div>
                        <div class="text-xs text-gray-500">v1.2.0</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230320-003</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">市级模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">制造业</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-08 16:45:30</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">已永久删除</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-gray-400 cursor-not-allowed mr-3">恢复</button>
                    <button class="text-gray-400 cursor-not-allowed">永久删除</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  显示
                  <span class="font-medium">1</span>
                  到
                  <span class="font-medium">3</span>
                  条，共
                  <span class="font-medium">3</span>
                  条记录
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">上一页</span>
                    <i class="fas fa-chevron-left text-xs"></i>
                  </a>
                  <a href="#" aria-current="page" class="z-10 bg-primary/10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                  <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                  <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                  <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">下一页</span>
                    <i class="fas fa-chevron-right text-xs"></i>
                  </a>
                </nav>
              </div>
            </div>
          </div>
        </div>

        <!-- 删除日志面板 -->
        <div class="panel mb-6">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">删除操作日志</h2>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作ID</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">LOG-20230512-001</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">删除</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">区域销售分析模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-12 09:30:25</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.100</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">成功</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                </tr>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">LOG-20230510-002</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">删除</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">客户满意度分析模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10 14:22:18</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.101</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">成功</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                </tr>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">LOG-20230508-003</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">永久删除</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">产品销量趋势模板</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-08 16:45:30</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.102</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">成功</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  显示
                  <span class="font-medium">1</span>
                  到
                  <span class="font-medium">3</span>
                  条，共
                  <span class="font-medium">12</span>
                  条记录
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">上一页</span>
                    <i class="fas fa-chevron-left text-xs"></i>
                  </a>
                  <a href="#" aria-current="page" class="z-10 bg-primary/10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                  <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                  <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                  <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">4</a>
                  <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">下一页</span>
                    <i class="fas fa-chevron-right text-xs"></i>
                  </a>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 恢复确认模态框 -->
    <div id="restoreModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">恢复模板</h3>
          <button id="closeRestoreModal" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0 h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center mr-4">
              <i class="fas fa-undo text-warning text-xl"></i>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900" id="restoreModalTitle">确认恢复模板？</h4>
              <p class="text-xs text-gray-500 mt-1" id="restoreModalMessage">您确定要恢复此模板吗？恢复后模板将回到模板列表中。</p>
            </div>
          </div>
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-gray-500">模板名称:</span>
              <span class="font-medium text-gray-900" id="restoreModalTemplateName">区域销售分析模板</span>
            </div>
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-gray-500">模板ID:</span>
              <span class="font-medium text-gray-900" id="restoreModalTemplateId">TPL-20230501-001</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-500">删除时间:</span>
              <span class="font-medium text-gray-900" id="restoreModalDeleteTime">2023-05-12 09:30:25</span>
            </div>
          </div>
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100">
            <button id="cancelRestore" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">取消</button>
            <button id="confirmRestore" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200 text-sm">确认恢复</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 永久删除确认模态框 -->
    <div id="deleteForeverModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">永久删除模板</h3>
          <button id="closeDeleteForeverModal" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="flex-shrink-0 h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center mr-4">
              <i class="fas fa-trash-alt text-danger text-xl"></i>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-900" id="deleteForeverModalTitle">确认永久删除模板？</h4>
              <p class="text-xs text-gray-500 mt-1" id="deleteForeverModalMessage">此操作将永久删除模板，无法恢复。请确认是否继续。</p>
            </div>
          </div>
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-gray-500">模板名称:</span>
              <span class="font-medium text-gray-900" id="deleteForeverModalTemplateName">区域销售分析模板</span>
            </div>
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-gray-500">模板ID:</span>
              <span class="font-medium text-gray-900" id="deleteForeverModalTemplateId">TPL-20230501-001</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-500">删除时间:</span>
              <span class="font-medium text-gray-900" id="deleteForeverModalDeleteTime">2023-05-12 09:30:25</span>
            </div>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">请输入"DELETE"确认永久删除</label>
            <input type="text" id="deleteConfirmInput" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 text-sm" />
          </div>
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100">
            <button id="cancelDeleteForever" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">取消</button>
            <button id="confirmDeleteForever" class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 transition-colors duration-200 text-sm" disabled>确认永久删除</button>
          </div>
        </div>
      </div>
    </div>

   <script src="js/common.js"></script>
    <script>
      function createTemplate(href) {
        window.location.href = href;
      }
      // 恢复确认模态框交互
      function showRestoreModal(templateName, templateId) {
        document.getElementById('restoreModalTemplateName').textContent = templateName;
        document.getElementById('restoreModalTemplateId').textContent = templateId;
        document.getElementById('restoreModal').classList.remove('hidden');
        document.getElementById('restoreModal').classList.add('flex');
      }

      document.getElementById('closeRestoreModal').addEventListener('click', function () {
        document.getElementById('restoreModal').classList.add('hidden');
        document.getElementById('restoreModal').classList.remove('flex');
      });

      document.getElementById('cancelRestore').addEventListener('click', function () {
        document.getElementById('restoreModal').classList.add('hidden');
        document.getElementById('restoreModal').classList.remove('flex');
      });

      document.getElementById('confirmRestore').addEventListener('click', function () {
        // 模拟恢复操作
        document.getElementById('restoreModal').classList.add('hidden');
        document.getElementById('restoreModal').classList.remove('flex');
        showNotification('模板恢复成功', 'success');
      });

      // 永久删除确认模态框交互
      function showDeleteForeverModal(templateName, templateId) {
        document.getElementById('deleteForeverModalTemplateName').textContent = templateName;
        document.getElementById('deleteForeverModalTemplateId').textContent = templateId;
        document.getElementById('deleteConfirmInput').value = '';
        document.getElementById('confirmDeleteForever').setAttribute('disabled', true);
        document.getElementById('deleteForeverModal').classList.remove('hidden');
        document.getElementById('deleteForeverModal').classList.add('flex');
      }

      document.getElementById('closeDeleteForeverModal').addEventListener('click', function () {
        document.getElementById('deleteForeverModal').classList.add('hidden');
        document.getElementById('deleteForeverModal').classList.remove('flex');
      });

      document.getElementById('cancelDeleteForever').addEventListener('click', function () {
        document.getElementById('deleteForeverModal').classList.add('hidden');
        document.getElementById('deleteForeverModal').classList.remove('flex');
      });

      document.getElementById('deleteConfirmInput').addEventListener('input', function () {
        if (this.value === 'DELETE') {
          document.getElementById('confirmDeleteForever').removeAttribute('disabled');
        } else {
          document.getElementById('confirmDeleteForever').setAttribute('disabled', true);
        }
      });

      document.getElementById('confirmDeleteForever').addEventListener('click', function () {
        // 模拟永久删除操作
        document.getElementById('deleteForeverModal').classList.add('hidden');
        document.getElementById('deleteForeverModal').classList.remove('flex');
        showNotification('模板已永久删除', 'success');
      });

      // 通知提示函数
      function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg flex items-center z-50 transform transition-all duration-300 translate-x-full';

        // 设置通知类型样式
        if (type === 'success') {
          notification.classList.add('bg-success/10', 'text-success', 'border-l-4', 'border-success');
          notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i> <span>${message}</span>`;
        } else if (type === 'error') {
          notification.classList.add('bg-danger/10', 'text-danger', 'border-l-4', 'border-danger');
          notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i> <span>${message}</span>`;
        } else {
          notification.classList.add('bg-primary/10', 'text-primary', 'border-l-4', 'border-primary');
          notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i> <span>${message}</span>`;
        }

        // 添加到文档
        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
          notification.classList.remove('translate-x-full');
        }, 100);

        // 3秒后隐藏通知
        setTimeout(() => {
          notification.classList.add('translate-x-full');
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }
    </script>
  </body>
</html>
