<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 模板列表</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#165DFF',
              secondary: '#4080FF',
              success: '#00B42A',
              warning: '#FF7D00',
              danger: '#F53F3F',
              info: '#86909C',
              light: '#F2F3F5',
              dark: '#1D2129',
            },
            fontFamily: {
              inter: ['Inter', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .menu-active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          border-left: 4px solid var(--primary-color);
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
          transition: all 200ms;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-secondary {
          background-color: white;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
          transition: all 200ms;
        }
        .btn-secondary:hover {
          background-color: rgba(24, 144, 255, 0.05);
        }
        .card {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #f3f4f6;
          transition: all 200ms;
        }
        .card:hover {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" >
            <div class="menu-item child  active" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 页面标题 -->
      <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板列表</h1>
      <!-- 面包屑导航 -->
      <div class="flex items-center text-sm text-gray-500 mb-6">
        <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <span class="text-primary">模板列表</span>
      </div>

      <!-- 工具栏 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex flex-wrap items-center gap-3">
            <div class="relative">
              <input type="text" placeholder="搜索模板名称" class="pl-9 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-64" />
              <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            <select class="border border-gray-200 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option>全部模板类型</option>
              <option>全国模板</option>
              <option>分省模板</option>
              <option>市级模板</option>
              <option>区县级模板</option>
            </select>
            <select class="border border-gray-200 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option>全部行业</option>
              <option>零售行业</option>
              <option>金融行业</option>
              <option>制造业</option>
              <option>服务业</option>
            </select>
            <select class="border border-gray-200 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option>全部状态</option>
              <option>草稿</option>
              <option>已发布</option>
              <option>已禁用</option>
            </select>
          </div>
          <div class="flex items-center gap-3">
            <button id="viewGrid" class="p-2 rounded bg-primary/10 text-primary"><i class="fas fa-th-large"></i></button>
            <button id="viewList" class="p-2 rounded hover:bg-gray-100 text-gray-600"><i class="fas fa-list"></i></button>
            <button id="viewCompact" class="p-2 rounded hover:bg-gray-100 text-gray-600"><i class="fas fa-th"></i></button>
            <button id="createTemplateBtn" class="px-4 py-2 rounded-lg btn-primary flex items-center gap-2" onclick="createTemplate('template_creation.html');callApi('http://120.48.171.191:5000/test/createTemplateBtn')">
              <i class="fas fa-plus"></i>
              <span>创建模板</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 模板列表 - 卡片视图 -->
      <div id="gridView" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
        <!-- 模板卡片 1 -->
        <div class="card overflow-hidden">
          <div class="relative h-40 bg-gray-100 overflow-hidden">
            <img src="https://picsum.photos/id/180/800/400" alt="全国运营监控大屏" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105" />
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
              <span class="text-xs font-medium text-white/80">全国级</span>
              <h3 class="text-white font-semibold text-sm">全国运营监控大屏</h3>
            </div>
            <div class="absolute top-2 right-2 flex gap-1">
              <span class="px-1.5 py-0.5 bg-success/80 text-white text-xs rounded">已发布</span>
            </div>
          </div>
          <div class="p-4">
            <div class="flex items-center text-xs text-gray-500 mb-3">
              <span class="flex items-center mr-3">
                <i class="fas fa-industry mr-1"></i>
                通用
              </span>
              <span class="flex items-center mr-3">
                <i class="fas fa-eye mr-1"></i>
                128
              </span>
              <span class="flex items-center">
                <i class="fas fa-code-branch mr-1"></i>
                v1.2.0
              </span>
            </div>
            <div class="flex justify-between items-center">
              <div class="flex gap-1.5">
                <button id="editTemplateBtn_1" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑" onclick="createTemplate('template_editing.html');callApi('http://120.48.171.191:5000/test/editTemplateBtn_1')"><i class="fas fa-edit"></i></button>
                <button id="copyTemplateBtn_1" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制" onclick="createTemplate('template_copy.html');callApi('http://120.48.171.191:5000/test/copyTemplateBtn_1')"><i class="fas fa-copy"></i></button>
                <button id="deleteTemplateBtn_1" class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除" onclick="createTemplate('template_deletion.html');callApi('http://120.48.171.191:5000/test/deleteTemplateBtn_1')"><i class="fas fa-trash-alt"></i></button>
              </div>
              <button id="previewTemplateBtn_1" class="px-3 py-1 text-xs rounded-lg btn-secondary" onclick="callApi('http://120.48.171.191:5000/test/previewTemplateBtn_1')">预览</button>
            </div>
          </div>
        </div>

        <!-- 模板卡片 2 -->
        <div class="card overflow-hidden">
          <div class="relative h-40 bg-gray-100 overflow-hidden">
            <img src="https://picsum.photos/id/160/800/400" alt="省份运营分析大屏" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105" />
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
              <span class="text-xs font-medium text-white/80">省级</span>
              <h3 class="text-white font-semibold text-sm">省份运营分析大屏</h3>
            </div>
            <div class="absolute top-2 right-2 flex gap-1">
              <span class="px-1.5 py-0.5 bg-success/80 text-white text-xs rounded">已发布</span>
            </div>
          </div>
          <div class="p-4">
            <div class="flex items-center text-xs text-gray-500 mb-3">
              <span class="flex items-center mr-3">
                <i class="fas fa-industry mr-1"></i>
                通用
              </span>
              <span class="flex items-center mr-3">
                <i class="fas fa-eye mr-1"></i>
                96
              </span>
              <span class="flex items-center">
                <i class="fas fa-code-branch mr-1"></i>
                v1.1.0
              </span>
            </div>
            <div class="flex justify-between items-center">
              <div class="flex gap-1.5">
                <button id="editTemplateBtn_2" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑" onclick="createTemplate('template_editing.html');callApi('http://120.48.171.191:5000/test/editTemplateBtn_2')"><i class="fas fa-edit"></i></button>
                <button id="copyTemplateBtn_2" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制" onclick="createTemplate('template_copy.html');callApi('http://120.48.171.191:5000/test/copyTemplateBtn_2')"><i class="fas fa-copy"></i></button>
                <button id="deleteTemplateBtn_2" class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除" onclick="createTemplate('template_deletion.html');callApi('http://120.48.171.191:5000/test/deleteTemplateBtn_2')"><i class="fas fa-trash-alt"></i></button>
              </div>
              <button id="previewTemplateBtn_2" class="px-3 py-1 text-xs rounded-lg btn-secondary" onclick="callApi('http://120.48.171.191:5000/test/previewTemplateBtn_2')">预览</button>
            </div>
          </div>
        </div>

        <!-- 模板卡片 3 -->
        <div class="card overflow-hidden">
          <div class="relative h-40 bg-gray-100 overflow-hidden">
            <img src="https://picsum.photos/id/1039/800/400" alt="城市运营监控大屏" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105" />
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
              <span class="text-xs font-medium text-white/80">市级</span>
              <h3 class="text-white font-semibold text-sm">城市运营监控大屏</h3>
            </div>
            <div class="absolute top-2 right-2 flex gap-1">
              <span class="px-1.5 py-0.5 bg-success/80 text-white text-xs rounded">已发布</span>
            </div>
          </div>
          <div class="p-4">
            <div class="flex items-center text-xs text-gray-500 mb-3">
              <span class="flex items-center mr-3">
                <i class="fas fa-industry mr-1"></i>
                零售
              </span>
              <span class="flex items-center mr-3">
                <i class="fas fa-eye mr-1"></i>
                72
              </span>
              <span class="flex items-center">
                <i class="fas fa-code-branch mr-1"></i>
                v1.0.0
              </span>
            </div>
            <div class="flex justify-between items-center">
              <div class="flex gap-1.5">
                <button id="editTemplateBtn_3" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑" onclick="createTemplate('template_editing.html');callApi('http://120.48.171.191:5000/test/editTemplateBtn_3')"><i class="fas fa-edit"></i></button>
                <button id="copyTemplateBtn_3" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制" onclick="createTemplate('template_copy.html');callApi('http://120.48.171.191:5000/test/copyTemplateBtn_3')"><i class="fas fa-copy"></i></button>
                <button id="deleteTemplateBtn_3" class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除" onclick="createTemplate('template_deletion.html');callApi('http://120.48.171.191:5000/test/deleteTemplateBtn_3')"><i class="fas fa-trash-alt"></i></button>
              </div>
              <button id="previewTemplateBtn_3" class="px-3 py-1 text-xs rounded-lg btn-secondary" onclick="callApi('http://120.48.171.191:5000/test/previewTemplateBtn_3')">预览</button>
            </div>
          </div>
        </div>

        <!-- 模板卡片 4 -->
        <div class="card overflow-hidden">
          <div class="relative h-40 bg-gray-100 overflow-hidden">
            <img src="https://picsum.photos/id/1043/800/400" alt="区县运营分析大屏" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105" />
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
              <span class="text-xs font-medium text-white/80">区县级</span>
              <h3 class="text-white font-semibold text-sm">区县运营分析大屏</h3>
            </div>
            <div class="absolute top-2 right-2 flex gap-1">
              <span class="px-1.5 py-0.5 bg-warning/80 text-white text-xs rounded">草稿</span>
            </div>
          </div>
          <div class="p-4">
            <div class="flex items-center text-xs text-gray-500 mb-3">
              <span class="flex items-center mr-3">
                <i class="fas fa-industry mr-1"></i>
                制造业
              </span>
              <span class="flex items-center mr-3">
                <i class="fas fa-eye mr-1"></i>
                56
              </span>
              <span class="flex items-center">
                <i class="fas fa-code-branch mr-1"></i>
                v0.9.0
              </span>
            </div>
            <div class="flex justify-between items-center">
              <div class="flex gap-1.5">
                <button id="editTemplateBtn_4" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑" onclick="callApi('http://120.48.171.191:5000/test/editTemplateBtn_4')"><i class="fas fa-edit"></i></button>
                <button id="copyTemplateBtn_4" class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制" onclick="callApi('http://120.48.171.191:5000/test/copyTemplateBtn_4')"><i class="fas fa-copy"></i></button>
                <button id="deleteTemplateBtn_4" class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除" onclick="callApi('http://120.48.171.191:5000/test/deleteTemplateBtn_4')"><i class="fas fa-trash-alt"></i></button>
              </div>
              <button id="previewTemplateBtn_4" class="px-3 py-1 text-xs rounded-lg btn-secondary" onclick="callApi('http://120.48.171.191:5000/test/previewTemplateBtn_4')">预览</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex items-center justify-between mt-8">
        <div class="text-sm text-gray-500">
          显示
          <span class="font-medium text-gray-700">1</span>
          到
          <span class="font-medium text-gray-700">4</span>
          条，共
          <span class="font-medium text-gray-700">24</span>
          条
        </div>
        <div class="flex items-center space-x-1">
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 text-gray-400 hover:border-primary hover:text-primary transition-colors duration-200" disabled>
            <i class="fas fa-chevron-left text-xs"></i>
          </button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-primary bg-primary text-white">1</button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 hover:border-primary hover:text-primary transition-colors duration-200">2</button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 hover:border-primary hover:text-primary transition-colors duration-200">3</button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 hover:border-primary hover:text-primary transition-colors duration-200">4</button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 hover:border-primary hover:text-primary transition-colors duration-200">5</button>
          <button class="w-8 h-8 flex items-center justify-center rounded border border-gray-200 text-gray-600 hover:border-primary hover:text-primary transition-colors duration-200">
            <i class="fas fa-chevron-right text-xs"></i>
          </button>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      function createTemplate(href) {
        window.location.href = href;
      }
      // 视图切换功能
      document.getElementById('viewGrid').addEventListener('click', function () {
        document.getElementById('gridView').style.display = 'grid';
        document.getElementById('listView').style.display = 'none';
        document.getElementById('compactView').style.display = 'none';
        this.classList.add('bg-primary/10', 'text-primary');
        this.classList.remove('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewList').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewList').classList.add('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewCompact').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewCompact').classList.add('hover:bg-gray-100', 'text-gray-600');
      });

      document.getElementById('viewList').addEventListener('click', function () {
        document.getElementById('gridView').style.display = 'none';
        document.getElementById('listView').style.display = 'block';
        document.getElementById('compactView').style.display = 'none';
        this.classList.add('bg-primary/10', 'text-primary');
        this.classList.remove('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewGrid').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewGrid').classList.add('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewCompact').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewCompact').classList.add('hover:bg-gray-100', 'text-gray-600');
      });

      document.getElementById('viewCompact').addEventListener('click', function () {
        document.getElementById('gridView').style.display = 'none';
        document.getElementById('listView').style.display = 'none';
        document.getElementById('compactView').style.display = 'grid';
        this.classList.add('bg-primary/10', 'text-primary');
        this.classList.remove('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewGrid').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewGrid').classList.add('hover:bg-gray-100', 'text-gray-600');
        document.getElementById('viewList').classList.remove('bg-primary/10', 'text-primary');
        document.getElementById('viewList').classList.add('hover:bg-gray-100', 'text-gray-600');
      });

      // 初始化隐藏其他视图
      document.addEventListener('DOMContentLoaded', function () {
        const listView = document.createElement('div');
        listView.id = 'listView';
        listView.className = 'hidden';
        listView.innerHTML = `
                <div class="overflow-hidden bg-white rounded-lg shadow-sm border border-gray-100 mb-6">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">查看次数</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 object-cover rounded" src="https://picsum.photos/id/180/40/40" alt="全国运营监控大屏">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">全国运营监控大屏</div>
                                            <div class="text-xs text-gray-500">全国级数据总览模板</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">全国模板</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">通用</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">v1.2.0</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-success/10 text-success">已发布</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">128</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-edit mr-1"></i>编辑</button>
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-copy mr-1"></i>复制</button>
                                        <button class="text-danger hover:text-danger/80 transition-colors duration-200"><i class="fas fa-trash-alt mr-1"></i>删除</button>
                                        <button class="text-gray-600 hover:text-gray-800 transition-colors duration-200"><i class="fas fa-play mr-1"></i>预览</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 object-cover rounded" src="https://picsum.photos/id/160/40/40" alt="省份运营分析大屏">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">省份运营分析大屏</div>
                                            <div class="text-xs text-gray-500">省级区域数据分析模板</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">分省模板</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">通用</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">v1.1.0</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-success/10 text-success">已发布</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">96</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-edit mr-1"></i>编辑</button>
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-copy mr-1"></i>复制</button>
                                        <button class="text-danger hover:text-danger/80 transition-colors duration-200"><i class="fas fa-trash-alt mr-1"></i>删除</button>
                                        <button class="text-gray-600 hover:text-gray-800 transition-colors duration-200"><i class="fas fa-play mr-1"></i>预览</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 object-cover rounded" src="https://picsum.photos/id/1039/40/40" alt="城市运营监控大屏">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">城市运营监控大屏</div>
                                            <div class="text-xs text-gray-500">市级区域数据监控模板</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级模板</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">零售</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">v1.0.0</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-success/10 text-success">已发布</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">72</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-edit mr-1"></i>编辑</button>
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-copy mr-1"></i>复制</button>
                                        <button class="text-danger hover:text-danger/80 transition-colors duration-200"><i class="fas fa-trash-alt mr-1"></i>删除</button>
                                        <button class="text-gray-600 hover:text-gray-800 transition-colors duration-200"><i class="fas fa-play mr-1"></i>预览</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded">
                                            <img class="h-10 w-10 object-cover rounded" src="https://picsum.photos/id/1043/40/40" alt="区县运营分析大屏">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">区县运营分析大屏</div>
                                            <div class="text-xs text-gray-500">区县级区域数据分析模板</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">区县级模板</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">制造业</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">v0.9.0</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-warning/10 text-warning">草稿</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">56</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-edit mr-1"></i>编辑</button>
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200"><i class="fas fa-copy mr-1"></i>复制</button>
                                        <button class="text-danger hover:text-danger/80 transition-colors duration-200"><i class="fas fa-trash-alt mr-1"></i>删除</button>
                                        <button class="text-gray-600 hover:text-gray-800 transition-colors duration-200"><i class="fas fa-play mr-1"></i>预览</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
        document.querySelector('#gridView').parentNode.appendChild(listView);

        const compactView = document.createElement('div');
        compactView.id = 'compactView';
        compactView.className = 'hidden grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
        compactView.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 hover:shadow-md transition-all duration-200 flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 bg-gray-100 rounded mr-3">
                        <img class="h-12 w-12 object-cover rounded" src="https://picsum.photos/id/180/48/48" alt="全国运营监控大屏">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900 truncate">全国运营监控大屏</h3>
                        <div class="flex items-center text-xs text-gray-500 mt-1">
                            <span class="px-1.5 py-0.5 bg-success/10 text-success rounded mr-2">已发布</span>
                            <span class="mr-2">全国级</span>
                            <span class="mr-2">通用</span>
                            <span>v1.2.0</span>
                        </div>
                    </div>
                    <div class="flex gap-1 ml-2">
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑"><i class="fas fa-edit"></i></button>
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制"><i class="fas fa-copy"></i></button>
                        <button class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 hover:shadow-md transition-all duration-200 flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 bg-gray-100 rounded mr-3">
                        <img class="h-12 w-12 object-cover rounded" src="https://picsum.photos/id/160/48/48" alt="省份运营分析大屏">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900 truncate">省份运营分析大屏</h3>
                        <div class="flex items-center text-xs text-gray-500 mt-1">
                            <span class="px-1.5 py-0.5 bg-success/10 text-success rounded mr-2">已发布</span>
                            <span class="mr-2">省级</span>
                            <span class="mr-2">通用</span>
                            <span>v1.1.0</span>
                        </div>
                    </div>
                    <div class="flex gap-1 ml-2">
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑"><i class="fas fa-edit"></i></button>
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制"><i class="fas fa-copy"></i></button>
                        <button class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 hover:shadow-md transition-all duration-200 flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 bg-gray-100 rounded mr-3">
                        <img class="h-12 w-12 object-cover rounded" src="https://picsum.photos/id/1039/48/48" alt="城市运营监控大屏">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900 truncate">城市运营监控大屏</h3>
                        <div class="flex items-center text-xs text-gray-500 mt-1">
                            <span class="px-1.5 py-0.5 bg-success/10 text-success rounded mr-2">已发布</span>
                            <span class="mr-2">市级</span>
                            <span class="mr-2">零售</span>
                            <span>v1.0.0</span>
                        </div>
                    </div>
                    <div class="flex gap-1 ml-2">
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑"><i class="fas fa-edit"></i></button>
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制"><i class="fas fa-copy"></i></button>
                        <button class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 hover:shadow-md transition-all duration-200 flex items-center">
                    <div class="flex-shrink-0 h-12 w-12 bg-gray-100 rounded mr-3">
                        <img class="h-12 w-12 object-cover rounded" src="https://picsum.photos/id/1043/48/48" alt="区县运营分析大屏">
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900 truncate">区县运营分析大屏</h3>
                        <div class="flex items-center text-xs text-gray-500 mt-1">
                            <span class="px-1.5 py-0.5 bg-warning/10 text-warning rounded mr-2">草稿</span>
                            <span class="mr-2">区县级</span>
                            <span class="mr-2">制造业</span>
                            <span>v0.9.0</span>
                        </div>
                    </div>
                    <div class="flex gap-1 ml-2">
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="编辑"><i class="fas fa-edit"></i></button>
                        <button class="p-1.5 text-xs text-primary hover:bg-primary/5 rounded transition-colors duration-200" title="复制"><i class="fas fa-copy"></i></button>
                        <button class="p-1.5 text-xs text-danger hover:bg-danger/5 rounded transition-colors duration-200" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
            `;
        document.querySelector('#gridView').parentNode.appendChild(compactView);
      });
    </script>
  <script>
function callApi(url) {
  console.log('调用API:', url);
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('API响应:', data);
      // 这里可以添加处理响应数据的逻辑
    })
    .catch(error => {
      console.error('API错误:', error);
      // 这里可以添加错误处理逻辑
    });
}
</script>
</body>
</html>
