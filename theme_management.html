<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 主题管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script src="js/canvas_modal.js"></script>
    <style>

      /* 面包屑导航样式 */
      .breadcrumb {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 16px;
      }

      .breadcrumb-item {
        position: relative;
        padding: 0 12px;
      }

      .breadcrumb-item:not(:last-child)::after {
        /* content: '>';
        position: absolute;
        right: 0;
        top: 0;
        color: var(--text-tertiary); */
      }

      .breadcrumb-item:first-child {
        padding-left: 0;
      }

      .breadcrumb-item:last-child {
        padding-right: 0;
        color: var(--primary-color);
      }
    </style>

    <!-- Tailwind配置 -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#1890FF',
              secondary: '#52C41A',
              warning: '#FAAD14',
              danger: '#FF4D4F',
              info: '#13C2C2',
              dark: '#1D2129',
              light: '#F2F3F5',
              'gray-100': '#F7F8FA',
              'gray-200': '#E5E6EB',
              'gray-300': '#C9CDD4',
              'gray-400': '#86909C',
              'gray-500': '#4E5969',
              'gray-600': '#272E3B',
            },
            fontFamily: {
              inter: ['Inter', 'system-ui', 'sans-serif'],
            },
            boxShadow: {
              card: '0 2px 12px 0 rgba(0, 0, 0, 0.08)',
              hover: '0 4px 16px 0 rgba(0, 0, 0, 0.12)',
            },
          },
        },
      };
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .grid-bg {
          background-size: 20px 20px;
          background-image: linear-gradient(to right, rgba(229, 231, 235, 0.5) 1px, transparent 1px), linear-gradient(to bottom, rgba(229, 231, 235, 0.5) 1px, transparent 1px);
        }
        .sidebar-item {
          display: flex;
          align-items: center;
          padding: 0.625rem 1rem;
          border-radius: 0.375rem;
          cursor: pointer;
          transition: all 200ms;
        }
        .sidebar-item:hover {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
        }
        .sidebar-item.active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          font-weight: 500;
        }
        .submenu-item {
          display: flex;
          align-items: center;
          padding: 0.375rem 1rem;
          border-radius: 0.375rem;
          cursor: pointer;
          transition: all 200ms;
          font-size: 0.875rem;
          margin-left: 1rem;
        }
        .submenu-item:hover {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
        }
        .submenu-item.active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          font-weight: 500;
        }
        .tab-item {
          padding: 0.5rem 1rem;
          cursor: pointer;
          transition: all 200ms;
          border-bottom: 2px solid transparent;
        }
        .tab-item:hover {
          color: var(--primary-color);
        }
        .tab-item.active {
          border-color: var(--primary-color);
          color: var(--primary-color);
          font-weight: 500;
        }
        .property-item {
          display: flex;
          flex-direction: column;
          gap: 0.375rem;
          margin-bottom: 1rem;
        }
        .btn {
          padding: 0.375rem 0.75rem;
          border-radius: 0.375rem;
          transition: all 200ms;
          font-weight: 500;
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-outline {
          border: 1px solid #d1d5db;
          color: #4b5563;
        }
        .btn-outline:hover {
          background-color: #f3f4f6;
        }
        .input-field {
          width: 100%;
          padding: 0.375rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          outline: none;
        }
        .input-field:focus {
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.5);
          border-color: var(--primary-color);
        }
        .label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
        }
        .canvas-component {
          position: absolute;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          background-color: white;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          transition: all 200ms;
          cursor: move;
          overflow: hidden;
        }
        .theme-card {
          border: 1px solid #e5e7eb;
          border-radius: 0.375rem;
          overflow: hidden;
          transition: all 200ms;
          cursor: pointer;
        }
        .theme-card:hover {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .theme-card.selected {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
        }
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child  active" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>
 <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">穿透权限管理</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
     
    </div>
    <div class="main-content">
      <!-- 中央内容区域 -->
      <!-- <section class="flex-1 overflow-hidden flex flex-col bg-gray-50"> -->
      <!-- <div class="p-4 border-b border-gray-200 bg-white">
          <h1 class="text-lg font-bold text-gray-800 mb-2">主题管理</h1> -->

      <!-- 面包屑导航 -->
      <!-- <div class="breadcrumb">
            <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
            <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
            <div class="breadcrumb-item active"><a href="canvas_management.html" style="text-decoration: none; color: inherit">画布管理</a></div>
          </div>
        </div> -->
      <div class="page-title">
        <i class="fas fa-project-diagram page-title-icon"></i>
        运营视图 - 画布管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item"><a href="canvas_management.html" style="text-decoration: none; color: inherit">画布管理</a></div>
      </div>

      <!-- 标签页内容 -->
      <div class="flex-1 overflow-hidden flex flex-col">
        <!-- 画布管理标签页 -->
        <div id="canvas-management" class="flex-1 overflow-hidden p-4">
          <div class="grid grid-cols-3 gap-4 h-full">
            <div class="col-span-3 bg-white rounded-md shadow-card p-4 overflow-y-auto">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-sm font-medium text-gray-700">画布列表</h3>
                <div>
                  <button class="btn btn-primary text-sm" onclick="createNewCanvas()">
                    <i class="fa fa-plus mr-1"></i>
                    新建画布
                  </button>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="flex flex-col gap-1">
                  <label class="text-xs font-medium text-gray-500">创建人</label>
                  <select class="input-field text-sm px-3 py-2 box-border">
                    <option value="">全部创建人</option>
                    <option value="管理员">管理员</option>
                    <option value="张三">张三</option>
                    <option value="李四">李四</option>
                    <option value="王五">王五</option>
                  </select>
                </div>
                <div class="flex flex-col gap-1">
                  <label class="text-xs font-medium text-gray-500">数据源类型</label>
                  <select class="input-field text-sm px-3 py-2 box-border">
                    <option value="">全部数据源</option>
                    <option value="销售数据">销售数据</option>
                    <option value="订单数据">订单数据</option>
                    <option value="用户数据">用户数据</option>
                    <option value="库存数据">库存数据</option>
                  </select>
                </div>
                <div class="flex flex-col gap-1">
                  <label class="text-xs font-medium text-gray-500">创建时间</label>
                  <div class="flex items-center gap-2">
                    <div class="relative flex-1">
                      <div class="relative flex-1 input-field text-sm w-full px-3 py-2 box-border bg-white rounded">
                        <i class="fa fa-calendar absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"></i>
                        <span class="text-gray-400 text-xs ml-8">yyyy/mm/dd</span>
                        <input type="text" class="absolute inset-0 left-4 text-sm w-full px-3 py-2 box-border bg-transparent outline-none" id="startTime" />
                      </div>
                    </div>
                    <span class="text-gray-400 whitespace-nowrap">至</span>
                    <div class="relative flex-1">
                      <div class="relative flex-1 input-field text-sm w-full px-3 py-2 box-border bg-white rounded">
                        <i class="fa fa-calendar absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"></i>
                        <span class="text-gray-400 text-xs ml-8">yyyy/mm/dd</span>
                        <input type="text" class="absolute inset-0 left-4 text-sm w-full px-3 py-2 box-border bg-transparent outline-none" id="endTime" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex flex-col gap-1">
                  <label class="text-xs font-medium text-gray-500">大屏名称</label>
                  <div class="relative">
                    <div class="relative input-field text-sm w-full px-3 py-2 box-border bg-white rounded">
                      <i class="fa fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"></i>
                      <span class="text-gray-400 text-xs ml-8">请输入大屏名称</span>
                      <input type="text" class="absolute inset-0 left-4 text-sm w-full px-3 py-2 box-border bg-transparent outline-none" />
                    </div>
                  </div>
                </div>
                <div class="col-span-1 md:col-span-2 flex items-end justify-end">
                  <div class="flex gap-2 mt-2">
                    <button class="btn btn-outline text-sm" onclick="searchCanvas()">查询</button>
                    <button class="btn btn-outline text-sm" onclick="resetCanvas()">重置</button>
                    <!-- <button class="btn btn-outline text-sm">
                      <i class="fas fa-plus"></i>
                      新增
                    </button> -->
                  </div>
                </div>
              </div>
              <!-- 优化后的画布列表 -->
              <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mb-6">
                <!-- 运营数据大屏 -->
                <div class="card shadow-card hover:shadow-hover transition-all duration-300 rounded-md overflow-hidden flex flex-col h-full">
                  <div style="position: relative; height: 140px; overflow: hidden">
                    <img src="https://picsum.photos/id/180/600/400" alt="运营数据大屏预览" style="width: 100%; height: 100%; object-fit: cover" />
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); color: white; padding: 10px">
                      <div style="font-size: 14px; font-weight: 500">运营数据大屏</div>
                      <div style="font-size: 12px">综合数据总览</div>
                    </div>
                  </div>
                  <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 12px 14px">
                    <div style="display: flex; flex-wrap: wrap; gap-y: 8px">
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-calendar-o mr-1.5 text-gray-400"></i>
                        2023-07-15
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-user-o mr-1.5 text-gray-400"></i>
                        管理员
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-code-fork mr-1.5 text-gray-400"></i>
                        v1.2.0
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-database mr-1.5 text-gray-400"></i>
                        销售数据
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px">
                      <span class="px-2 py-0.5 bg-secondary/10 text-secondary text-xs rounded-full">启用</span>
                      <div style="display: flex; gap: 6px">
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="预览" onclick="previewCanvas(this)">
                          <i class="fa fa-eye text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="视图编辑" onclick="editCanvas(this)">
                          <i class="fa fa-pencil text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除" onclick="deleteCanvas(this)">
                          <i class="fa fa-trash text-xs"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 销售分析画布 -->
                <div class="card shadow-card hover:shadow-hover transition-all duration-300 rounded-md overflow-hidden flex flex-col h-full">
                  <div style="position: relative; height: 140px; overflow: hidden">
                    <img src="https://picsum.photos/id/160/600/400" alt="销售分析画布预览" style="width: 100%; height: 100%; object-fit: cover" />
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); color: white; padding: 10px">
                      <div style="font-size: 14px; font-weight: 500">销售分析画布</div>
                      <div style="font-size: 12px">销售数据深度分析</div>
                    </div>
                  </div>
                  <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 12px 14px">
                    <div style="display: flex; flex-wrap: wrap; gap-y: 8px">
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-calendar-o mr-1.5 text-gray-400"></i>
                        2023-07-10
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-user-o mr-1.5 text-gray-400"></i>
                        张三
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-code-fork mr-1.5 text-gray-400"></i>
                        v2.0.1
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-database mr-1.5 text-gray-400"></i>
                        订单数据
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px">
                      <span class="px-2 py-0.5 bg-secondary/10 text-secondary text-xs rounded-full">启用</span>
                      <div style="display: flex; gap: 6px">
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="预览" onclick="previewCanvas(this)">
                          <i class="fa fa-eye text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="视图编辑" onclick="editCanvas(this)">
                          <i class="fa fa-pencil text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除" onclick="deleteCanvas(this)">
                          <i class="fa fa-trash text-xs"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 用户行为分析 -->
                <div class="card shadow-card hover:shadow-hover transition-all duration-300 rounded-md overflow-hidden flex flex-col h-full">
                  <div style="position: relative; height: 140px; overflow: hidden">
                    <img src="https://picsum.photos/id/1039/600/400" alt="用户行为分析预览" style="width: 100%; height: 100%; object-fit: cover" />
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); color: white; padding: 10px">
                      <div style="font-size: 14px; font-weight: 500">用户行为分析</div>
                      <div style="font-size: 12px">用户行为模式分析</div>
                    </div>
                  </div>
                  <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 12px 14px">
                    <div style="display: flex; flex-wrap: wrap; gap-y: 8px">
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-calendar-o mr-1.5 text-gray-400"></i>
                        2023-07-05
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-user-o mr-1.5 text-gray-400"></i>
                        李四
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-code-fork mr-1.5 text-gray-400"></i>
                        v0.9.3
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-database mr-1.5 text-gray-400"></i>
                        用户数据
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px">
                      <span class="px-2 py-0.5 bg-warning/10 text-warning text-xs rounded-full">草稿</span>
                      <div style="display: flex; gap: 6px">
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="预览" onclick="previewCanvas(this)">
                          <i class="fa fa-eye text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="视图编辑" onclick="editCanvas(this)">
                          <i class="fa fa-pencil text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除" onclick="deleteCanvas(this)">
                          <i class="fa fa-trash text-xs"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 库存监控画布 -->
                <div class="card shadow-card hover:shadow-hover transition-all duration-300 rounded-md overflow-hidden flex flex-col h-full">
                  <div style="position: relative; height: 140px; overflow: hidden">
                    <img src="https://picsum.photos/id/1043/600/400" alt="库存监控画布预览" style="width: 100%; height: 100%; object-fit: cover" />
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)); color: white; padding: 10px">
                      <div style="font-size: 14px; font-weight: 500">库存监控画布</div>
                      <div style="font-size: 12px">库存实时监控</div>
                    </div>
                  </div>
                  <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 12px 14px">
                    <div style="display: flex; flex-wrap: wrap; gap-y: 8px">
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-calendar-o mr-1.5 text-gray-400"></i>
                        2023-06-28
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-user-o mr-1.5 text-gray-400"></i>
                        王五
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-code-fork mr-1.5 text-gray-400"></i>
                        v1.0.0
                      </div>
                      <div style="flex: 0 0 50%; font-size: 12px; color: var(--text-tertiary); display: flex; align-items: center">
                        <i class="fa fa-database mr-1.5 text-gray-400"></i>
                        库存数据
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px">
                      <span class="px-2 py-0.5 bg-danger/10 text-danger text-xs rounded-full">禁用</span>
                      <div style="display: flex; gap: 6px">
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="预览" onclick="previewCanvas(this)">
                          <i class="fa fa-eye text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="视图编辑" onclick="editCanvas(this)">
                          <i class="fa fa-pencil text-xs"></i>
                        </button>
                        <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除" onclick="deleteCanvas(this)">
                          <i class="fa fa-trash text-xs"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主题选择标签页 -->
        <div id="theme-selection" class="hidden flex-1 overflow-hidden p-4">
          <div class="grid grid-cols-4 gap-4 h-full">
            <div class="col-span-1 bg-white rounded-md shadow-card p-4 overflow-y-auto">
              <h3 class="text-sm font-medium text-gray-700 mb-3">主题筛选</h3>
              <div class="space-y-3">
                <div class="property-item">
                  <label class="label">主题风格</label>
                  <select class="input-field text-sm">
                    <option>全部风格</option>
                    <option>商务风格</option>
                    <option>科技风格</option>
                    <option>简约风格</option>
                    <option>活力风格</option>
                  </select>
                </div>
                <div class="property-item">
                  <label class="label">色系</label>
                  <select class="input-field text-sm">
                    <option>全部色系</option>
                    <option>蓝色系</option>
                    <option>绿色系</option>
                    <option>橙色系</option>
                    <option>紫色系</option>
                  </select>
                </div>
                <div class="property-item">
                  <label class="label">行业</label>
                  <select class="input-field text-sm">
                    <option>全部行业</option>
                    <option>金融行业</option>
                    <option>零售行业</option>
                    <option>制造业</option>
                    <option>服务业</option>
                  </select>
                </div>
                <div class="pt-2">
                  <button class="btn btn-primary w-full text-sm">筛选</button>
                </div>

                <h3 class="text-sm font-medium text-gray-700 mt-6 mb-3">预览模式</h3>
                <div class="space-y-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="previewMode" checked class="sr-only peer" value="static" />
                    <div class="w-4 h-4 rounded-full border border-gray-300 peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center"><div class="w-2 h-2 rounded-full bg-white"></div></div>
                    <span class="ml-2 text-sm">静态预览</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="previewMode" class="sr-only peer" value="dynamic" />
                    <div class="w-4 h-4 rounded-full border border-gray-300 peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center"><div class="w-2 h-2 rounded-full bg-white"></div></div>
                    <span class="ml-2 text-sm">动态预览</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="previewMode" class="sr-only peer" value="interactive" />
                    <div class="w-4 h-4 rounded-full border border-gray-300 peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center"><div class="w-2 h-2 rounded-full bg-white"></div></div>
                    <span class="ml-2 text-sm">交互预览</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="col-span-3 bg-white rounded-lg shadow-card p-6 overflow-hidden flex flex-col">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-base font-medium text-gray-800">主题预览</h3>
                <div class="flex items-center gap-3">
                  <div class="flex items-center gap-2">
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="上一个">
                      <i class="fa fa-chevron-left text-xs"></i>
                    </button>
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="下一个">
                      <i class="fa fa-chevron-right text-xs"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 主题对比视图 -->
              <div class="flex-1 overflow-auto p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 主题1 -->
                <div class="theme-card selected rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-primary bg-white h-[320px] flex flex-col transform hover:-translate-y-1">
                  <div class="bg-primary/10 p-3 flex justify-between items-center border-b border-primary/20">
                    <div class="font-medium text-sm text-primary">蓝色商务主题</div>
                    <div class="px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full font-medium border border-primary/30">已选中</div>
                  </div>
                  <div class="flex-1 p-4">
                    <div class="rounded-lg bg-gray-50 p-3 mb-3 h-[200px] flex flex-col border border-gray-100">
                      <div class="flex justify-between items-center mb-2 pb-2 border-b border-gray-200">
                        <div class="font-medium text-sm">数据概览</div>
                        <div class="text-xs text-gray-500">今日</div>
                      </div>
                      <div class="grid grid-cols-2 gap-2 mb-3 flex-grow">
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">销售额</div>
                          <div class="text-lg font-bold text-primary mt-1">¥128,560</div>
                        </div>
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">订单数</div>
                          <div class="text-lg font-bold text-secondary mt-1">2,856</div>
                        </div>
                      </div>
                      <div class="h-16 w-full bg-white rounded shadow-sm border border-gray-100"></div>
                    </div>
                  </div>
                  <div class="p-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
                    <span>适用于: 金融、商务</span>
                    <span>版本: 1.0.0</span>
                  </div>
                </div>

                <!-- 主题2 -->
                <div class="theme-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 bg-white h-[320px] flex flex-col transform hover:-translate-y-1">
                  <div class="bg-gray-50 p-3 flex justify-between items-center border-b border-gray-100">
                    <div class="font-medium text-sm text-gray-800">科技感主题</div>
                    <div class="px-2 py-0.5 bg-gray-100 text-gray-500 text-xs rounded-full font-medium border border-gray-200">未选中</div>
                  </div>
                  <div class="flex-1 p-4">
                    <div class="rounded-lg bg-gray-900 p-3 mb-3 h-[200px] flex flex-col border border-gray-700 text-white">
                      <div class="flex justify-between items-center mb-2 pb-2 border-b border-gray-700">
                        <div class="font-medium text-sm">数据概览</div>
                        <div class="text-xs text-gray-400">今日</div>
                      </div>
                      <div class="grid grid-cols-2 gap-2 mb-3 flex-grow">
                        <div class="bg-gray-800 rounded p-2 shadow-sm border border-gray-700">
                          <div class="text-xs text-gray-400">销售额</div>
                          <div class="text-lg font-bold text-info mt-1">¥128,560</div>
                        </div>
                        <div class="bg-gray-800 rounded p-2 shadow-sm border border-gray-700">
                          <div class="text-xs text-gray-400">订单数</div>
                          <div class="text-lg font-bold text-secondary mt-1">2,856</div>
                        </div>
                      </div>
                      <div class="h-16 w-full bg-gray-800 rounded shadow-sm border border-gray-700"></div>
                    </div>
                  </div>
                  <div class="p-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
                    <span>适用于: 科技、互联网</span>
                    <span>版本: 1.0.0</span>
                  </div>
                </div>

                <!-- 主题3 -->
                <div class="theme-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 bg-white h-[320px] flex flex-col transform hover:-translate-y-1">
                  <div class="bg-gray-50 p-3 flex justify-between items-center border-b border-gray-100">
                    <div class="font-medium text-sm text-gray-800">简约主题</div>
                    <div class="px-2 py-0.5 bg-gray-100 text-gray-500 text-xs rounded-full font-medium border border-gray-200">未选中</div>
                  </div>
                  <div class="flex-1 p-4">
                    <div class="rounded-lg bg-gray-50 p-3 mb-3 h-[200px] flex flex-col border border-gray-200">
                      <div class="flex justify-between items-center mb-2 pb-2 border-b border-gray-200">
                        <div class="font-medium text-sm">数据概览</div>
                        <div class="text-xs text-gray-500">今日</div>
                      </div>
                      <div class="grid grid-cols-2 gap-2 mb-3 flex-grow">
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">销售额</div>
                          <div class="text-lg font-bold text-gray-800 mt-1">¥128,560</div>
                        </div>
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">订单数</div>
                          <div class="text-lg font-bold text-gray-800 mt-1">2,856</div>
                        </div>
                      </div>
                      <div class="h-16 w-full bg-white rounded shadow-sm border border-gray-100"></div>
                    </div>
                  </div>
                  <div class="p-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
                    <span>适用于: 通用、教育</span>
                    <span>版本: 1.0.0</span>
                  </div>
                </div>

                <!-- 主题4 -->
                <div class="theme-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 bg-white h-[320px] flex flex-col transform hover:-translate-y-1">
                  <div class="bg-gray-50 p-3 flex justify-between items-center border-b border-gray-100">
                    <div class="font-medium text-sm text-gray-800">活力主题</div>
                    <div class="px-2 py-0.5 bg-gray-100 text-gray-500 text-xs rounded-full font-medium border border-gray-200">未选中</div>
                  </div>
                  <div class="flex-1 p-4">
                    <div class="rounded-lg bg-gray-50 p-3 mb-3 h-[200px] flex flex-col border border-gray-200">
                      <div class="flex justify-between items-center mb-2 pb-2 border-b border-gray-200">
                        <div class="font-medium text-sm">数据概览</div>
                        <div class="text-xs text-gray-500">今日</div>
                      </div>
                      <div class="grid grid-cols-2 gap-2 mb-3 flex-grow">
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">销售额</div>
                          <div class="text-lg font-bold text-orange-500 mt-1">¥128,560</div>
                        </div>
                        <div class="bg-white rounded p-2 shadow-sm border border-gray-100">
                          <div class="text-xs text-gray-500">订单数</div>
                          <div class="text-lg font-bold text-pink-500 mt-1">2,856</div>
                        </div>
                      </div>
                      <div class="h-16 w-full bg-white rounded shadow-sm border border-gray-100"></div>
                    </div>
                  </div>
                  <div class="p-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
                    <span>适用于: 零售、餐饮</span>
                    <span>版本: 1.0.0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主题管理标签页 -->
        <div id="theme-management" class="hidden flex-1 overflow-hidden p-4">
          <div class="grid grid-cols-12 gap-4 h-full">
            <div class="col-span-3 bg-white rounded-lg shadow-card p-5 overflow-y-auto">
              <h3 class="text-base font-medium text-gray-800 mb-4">我的主题</h3>
              <div class="flex justify-between items-center mb-4">
                <span class="text-sm text-gray-500">共 6 个主题</span>
                <button class="btn btn-primary text-sm flex items-center gap-1.5 px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                  <i class="fa fa-plus"></i>
                  新建主题
                </button>
              </div>
              <div class="space-y-3">
                <div class="p-3 border border-primary bg-primary/5 rounded-lg cursor-pointer flex justify-between items-center hover:shadow-md transition-all duration-300 transform hover:-translate-x-0.5">
                  <div>
                    <div class="font-medium text-sm text-primary">蓝色商务主题</div>
                    <div class="text-xs text-gray-500 mt-1.5">上次修改: 2023-07-15</div>
                  </div>
                  <div class="flex items-center gap-2">
                    <button class="p-2 rounded-full hover:bg-primary/10 text-primary transition-all duration-300" title="编辑">
                      <i class="fa fa-edit text-sm"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-primary/10 text-primary transition-all duration-300" title="复制">
                      <i class="fa fa-copy text-sm"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-danger/10 text-danger transition-all duration-300" title="删除">
                      <i class="fa fa-trash-o text-sm"></i>
                    </button>
                  </div>
                </div>
                <div class="p-3 border border-gray-200 rounded-lg cursor-pointer flex justify-between items-center hover:border-gray-300 hover:bg-gray-50 hover:shadow-md transition-all duration-300 transform hover:-translate-x-0.5">
                  <div>
                    <div class="font-medium text-sm text-gray-800">科技感主题</div>
                    <div class="text-xs text-gray-500 mt-1.5">上次修改: 2023-07-10</div>
                  </div>
                  <div class="flex items-center gap-2">
                    <button class="p-2 rounded-full hover:bg-gray-100 text-gray-600 transition-all duration-300" title="编辑">
                      <i class="fa fa-edit text-sm"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-gray-100 text-gray-600 transition-all duration-300" title="复制">
                      <i class="fa fa-copy text-sm"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-gray-100 text-gray-600 transition-all duration-300" title="删除">
                      <i class="fa fa-trash-o text-sm"></i>
                    </button>
                  </div>
                </div>
                <div class="p-2 border border-gray-200 rounded-md cursor-pointer flex justify-between items-center hover:border-gray-300 hover:bg-gray-50">
                  <div>
                    <div class="font-medium text-sm">简约主题</div>
                    <div class="text-xs text-gray-500 mt-1">上次修改: 2023-07-05</div>
                  </div>
                  <div class="flex items-center gap-1.5">
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="编辑">
                      <i class="fa fa-edit text-xs"></i>
                    </button>
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="复制">
                      <i class="fa fa-copy text-xs"></i>
                    </button>
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除">
                      <i class="fa fa-trash-o text-xs"></i>
                    </button>
                  </div>
                </div>
                <div class="p-2 border border-gray-200 rounded-md cursor-pointer flex justify-between items-center hover:border-gray-300 hover:bg-gray-50">
                  <div>
                    <div class="font-medium text-sm">活力主题</div>
                    <div class="text-xs text-gray-500 mt-1">上次修改: 2023-06-28</div>
                  </div>
                  <div class="flex items-center gap-1.5">
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="编辑">
                      <i class="fa fa-edit text-xs"></i>
                    </button>
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="复制">
                      <i class="fa fa-copy text-xs"></i>
                    </button>
                    <button class="p-1.5 rounded hover:bg-gray-100 text-gray-500" title="删除">
                      <i class="fa fa-trash-o text-xs"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-span-9 bg-white rounded-md shadow-card p-4 overflow-hidden flex flex-col">
              <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                <h3 class="text-base font-medium text-gray-800">主题编辑器 - 蓝色商务主题</h3>
                <div class="flex items-center gap-3">
                  <button class="btn btn-outline text-sm px-4 py-2 rounded-lg border border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300" title="保存">
                    <i class="fa fa-save mr-1.5"></i>
                    保存
                  </button>
                  <button class="btn btn-primary text-sm px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-300" title="应用">
                    <i class="fa fa-check mr-1.5"></i>
                    应用
                  </button>
                </div>
              </div>

              <!-- 主题编辑器内容 -->
              <div class="flex-1 overflow-hidden flex flex-col">
                <div class="flex border-b border-gray-200">
                  <button class="px-4 py-2 text-sm font-medium text-primary border-b-2 border-primary">色板管理</button>
                  <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">字体系统</button>
                  <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">间距设置</button>
                  <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">边框圆角</button>
                  <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">版本历史</button>
                </div>

                <div class="flex-1 overflow-auto p-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-2">
                    <div>
                      <h4 class="text-base font-medium text-gray-800 mb-4">基础颜色</h4>
                      <div class="space-y-5">
                        <div class="property-item rounded-lg bg-gray-50 p-4 border border-gray-100 hover:shadow-sm transition-all duration-300">
                          <label class="label flex items-center justify-between mb-2.5">
                            <span class="text-sm font-medium">主色调</span>
                            <span class="text-xs text-gray-400">--primary-color</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-primary border-2 border-gray-200 cursor-pointer flex items-center justify-center text-white font-bold shadow-sm" title="点击选择颜色">#1890FF</div>
                            <input type="text" class="input-field text-sm flex-1 rounded-lg border border-gray-300 focus:border-primary focus:ring-1 focus:ring-primary transition-all duration-300" value="#1890FF" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item rounded-lg bg-gray-50 p-4 border border-gray-100 hover:shadow-sm transition-all duration-300">
                          <label class="label flex items-center justify-between mb-2.5">
                            <span class="text-sm font-medium">辅助色</span>
                            <span class="text-xs text-gray-400">--secondary-color</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-secondary border-2 border-gray-200 cursor-pointer flex items-center justify-center text-white font-bold shadow-sm" title="点击选择颜色">#52C41A</div>
                            <input type="text" class="input-field text-sm flex-1 rounded-lg border border-gray-300 focus:border-primary focus:ring-1 focus:ring-primary transition-all duration-300" value="#52C41A" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>警告色</span>
                            <span class="text-xs text-gray-400">--warning-color</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-warning border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#FAAD14</div>
                            <input type="text" class="input-field text-sm flex-1" value="#FAAD14" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>危险色</span>
                            <span class="text-xs text-gray-400">--danger-color</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-danger border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#FF4D4F</div>
                            <input type="text" class="input-field text-sm flex-1" value="#FF4D4F" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>信息色</span>
                            <span class="text-xs text-gray-400">--info-color</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-info border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#13C2C2</div>
                            <input type="text" class="input-field text-sm flex-1" value="#13C2C2" placeholder="输入颜色值" />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-3">文本颜色</h4>
                      <div class="space-y-4">
                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>主要文本</span>
                            <span class="text-xs text-gray-400">--text-primary</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-gray-800 border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#1D2129</div>
                            <input type="text" class="input-field text-sm flex-1" value="#1D2129" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>次要文本</span>
                            <span class="text-xs text-gray-400">--text-secondary</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-gray-600 border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#4E5969</div>
                            <input type="text" class="input-field text-sm flex-1" value="#4E5969" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <div class="property-item">
                          <label class="label flex items-center justify-between">
                            <span>辅助文本</span>
                            <span class="text-xs text-gray-400">--text-tertiary</span>
                          </label>
                          <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-md bg-gray-400 border border-gray-300 cursor-pointer flex items-center justify-center text-white font-bold" title="点击选择颜色">#86909C</div>
                            <input type="text" class="input-field text-sm flex-1" value="#86909C" placeholder="输入颜色值" />
                          </div>
                        </div>

                        <h4 class="text-sm font-medium text-gray-700 mt-6 mb-3">背景颜色</h4>
                        <div class="space-y-4">
                          <div class="property-item">
                            <label class="label flex items-center justify-between">
                              <span>主要背景</span>
                              <span class="text-xs text-gray-400">--bg-primary</span>
                            </label>
                            <div class="flex items-center gap-3">
                              <div class="w-10 h-10 rounded-md bg-white border border-gray-300 cursor-pointer flex items-center justify-center text-gray-800 font-bold" title="点击选择颜色">#FFFFFF</div>
                              <input type="text" class="input-field text-sm flex-1" value="#FFFFFF" placeholder="输入颜色值" />
                            </div>
                          </div>

                          <div class="property-item">
                            <label class="label flex items-center justify-between">
                              <span>次要背景</span>
                              <span class="text-xs text-gray-400">--bg-secondary</span>
                            </label>
                            <div class="flex items-center gap-3">
                              <div class="w-10 h-10 rounded-md bg-gray-50 border border-gray-300 cursor-pointer flex items-center justify-center text-gray-800 font-bold" title="点击选择颜色">#F7F8FA</div>
                              <input type="text" class="input-field text-sm flex-1" value="#F7F8FA" placeholder="输入颜色值" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="mt-10 bg-gray-50 p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h4 class="text-base font-medium text-gray-800 mb-4">主题预览</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div class="bg-white p-4 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                        <button class="btn btn-primary w-full mb-3 text-sm py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">主要按钮</button>
                        <button class="btn btn-outline w-full text-sm py-2 rounded-lg border border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300">次要按钮</button>
                      </div>
                      <div class="bg-white p-4 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                        <div class="h-24 bg-primary/10 rounded-lg mb-3 flex items-center justify-center border border-primary/20">
                          <div class="text-primary font-medium">主色调背景</div>
                        </div>
                        <div class="text-sm text-gray-800 font-medium">主要文本</div>
                        <div class="text-sm text-gray-600 mt-1.5">次要文本</div>
                        <div class="text-sm text-gray-400 mt-1.5">辅助文本</div>
                      </div>
                      <div class="bg-white p-4 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                        <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-100">
                          <div class="text-sm font-medium text-gray-800">数据卡片</div>
                          <div class="text-xs text-gray-400">示例</div>
                        </div>
                        <div class="text-lg font-bold text-primary">¥128,560</div>
                        <div class="text-xs text-green-500 flex items-center gap-1 mt-2">
                          <i class="fa fa-arrow-up"></i>
                          8.5%
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- </section> -->
    </div>
    <!-- </main> -->

    <script>
      // 标签页切换
      document.addEventListener('DOMContentLoaded', function () {
        // 初始化图表
        initCharts();

        // 标签页切换
        const tabItems = document.querySelectorAll('.tab-item');
        tabItems.forEach(tab => {
          tab.addEventListener('click', function () {
            // 移除所有标签页的激活状态
            tabItems.forEach(item => item.classList.remove('active'));
            // 添加当前标签页的激活状态
            this.classList.add('active');
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('#canvas-management, #theme-selection, #theme-management');
            tabContents.forEach(content => content.classList.add('hidden'));
            // 显示当前标签页内容
            const targetTab = this.getAttribute('data-tab');
            document.getElementById(targetTab).classList.remove('hidden');
          });
        });

        // 主题卡片选择
        const themeCards = document.querySelectorAll('.theme-card');
        themeCards.forEach(card => {
          card.addEventListener('click', function () {
            // 移除所有主题卡片的选中状态
            themeCards.forEach(item => item.classList.remove('selected'));
            // 添加当前主题卡片的选中状态
            this.classList.add('selected');
          });
        });
      });

      // 初始化图表
      function initCharts() {
        // 销售趋势图表
        if (document.getElementById('salesChart')) {
          const salesCtx = document.getElementById('salesChart').getContext('2d');
          new Chart(salesCtx, {
            type: 'line',
            data: {
              labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
              datasets: [
                {
                  label: '销售额',
                  data: [12, 19, 15, 22, 28, 32],
                  borderColor: '#1890FF',
                  backgroundColor: 'rgba(24, 144, 255, 0.1)',
                  tension: 0.3,
                  fill: true,
                },
              ],
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  display: false,
                },
              },
              scales: {
                x: {
                  display: false,
                },
                y: {
                  display: false,
                },
              },
            },
          });
        }

        // 用户分布图表
        if (document.getElementById('userChart')) {
          const userCtx = document.getElementById('userChart').getContext('2d');
          new Chart(userCtx, {
            type: 'pie',
            data: {
              labels: ['华东', '华北', '华南', '西南', '其他'],
              datasets: [
                {
                  data: [35, 25, 20, 15, 5],
                  backgroundColor: ['#1890FF', '#52C41A', '#FAAD14', '#FF4D4F', '#86909C'],
                  borderWidth: 0,
                },
              ],
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'right',
                  labels: {
                    font: {
                      size: 10,
                    },
                    padding: 10,
                  },
                },
              },
            },
          });
        }
      }
    </script>
    <!-- 版本对比模态框 -->
    <div id="versionCompareModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div class="p-4 border-b flex justify-between items-center">
          <h3 class="font-medium">版本对比</h3>
          <button id="closeCompareModal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="p-4 overflow-y-auto flex-grow">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="border rounded p-3">
              <h4 class="font-medium mb-2">版本 v2.0.1</h4>
              <p class="text-sm text-gray-500 mb-2">创建于: 2023-07-15</p>
              <p class="text-sm text-gray-500 mb-2">作者: 张三</p>
              <div class="h-40 bg-gray-100 rounded flex items-center justify-center">
                <span class="text-gray-400">版本预览</span>
              </div>
            </div>
            <div class="border rounded p-3">
              <h4 class="font-medium mb-2">版本 v1.2.0</h4>
              <p class="text-sm text-gray-500 mb-2">创建于: 2023-07-10</p>
              <p class="text-sm text-gray-500 mb-2">作者: 管理员</p>
              <div class="h-40 bg-gray-100 rounded flex items-center justify-center">
                <span class="text-gray-400">版本预览</span>
              </div>
            </div>
          </div>
          <div class="border rounded p-3 bg-gray-50">
            <h4 class="font-medium mb-2">变更内容</h4>
            <ul class="text-sm space-y-1">
              <li class="flex items-start">
                <i class="fa fa-plus text-green-500 mr-2 mt-1"></i>
                添加了新的数据指标卡片
              </li>
              <li class="flex items-start">
                <i class="fa fa-pencil text-blue-500 mr-2 mt-1"></i>
                优化了图表显示效果
              </li>
              <li class="flex items-start">
                <i class="fa fa-trash text-red-500 mr-2 mt-1"></i>
                移除了过时的数据源
              </li>
            </ul>
          </div>
        </div>
        <div class="p-4 border-t flex justify-end">
          <button class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition mr-2">版本回溯</button>
          <button id="closeCompareBtn" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition">关闭</button>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      // API基础URL
      const API_BASE_URL = 'http://localhost:8000/api';

      // 封装fetch请求函数
      async function apiRequest(url, method, data = null) {
        try {
          const options = {
            method: method,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
            }
          };

          if (data) {
            options.body = JSON.stringify(data);
          }

          console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
          const response = await fetch(url, options);
          const result = await response.json();
          console.log(`接口响应:`, result);

          if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
          }

          return result;
        } catch (error) {
          console.error('API请求错误:', error);
          // 不处理响应结果，静默处理错误
          throw error;
        }
      }



      // 删除画布函数
      async function deleteCanvas(button) {
        if (confirm('确定要删除此画布吗？删除后将无法恢复。')) {
          try {
            const canvasItem = button.closest('.canvas-item');
            const canvasId = canvasItem.getAttribute('data-id') || 'default';
            
            // 调用删除画布接口
            await apiRequest(`${API_BASE_URL}/theme-management/delete-canvas`, 'DELETE', {
              canvasId: canvasId
            });
            console.log('删除画布成功');
          } catch (error) {
            console.error('删除画布失败:', error);
          }
          
          // 保持原有逻辑
          alert('删除成功！');
        }
      }

      // 预览画布函数
      async function previewCanvas(button) {
        try {
          const canvasItem = button.closest('.canvas-item');
          const canvasId = canvasItem.getAttribute('data-id') || 'default';
          
          // 调用预览画布接口
          await apiRequest(`${API_BASE_URL}/theme-management/preview-canvas`, 'POST', {
            canvasId: canvasId
          });
          console.log('预览画布成功');
        } catch (error) {
          console.error('预览画布失败:', error);
        }
        
        // 保持原有逻辑
        window.location.href = 'canvas_management.html';
      }

      // 编辑画布函数
      async function editCanvas(button) {
        try {
          const canvasItem = button.closest('.canvas-item');
          const canvasId = canvasItem.getAttribute('data-id') || 'default';
          
          // 调用编辑画布接口
          await apiRequest(`${API_BASE_URL}/theme-management/edit-canvas`, 'POST', {
            canvasId: canvasId
          });
          console.log('编辑画布成功');
        } catch (error) {
          console.error('编辑画布失败:', error);
        }
        
        // 保持原有逻辑
        window.location.href = 'canvas_management.html';
      }

      // 新建画布函数
      async function createNewCanvas() {
        try {
          // 调用新建画布接口
          await apiRequest(`${API_BASE_URL}/theme-management/create-canvas`, 'POST', {
            timestamp: new Date().toISOString()
          });
          console.log('新建画布成功');
        } catch (error) {
          console.error('新建画布失败:', error);
        }
        
        // 保持原有逻辑
        window.location.href = 'canvas_management.html';
      }

      // 查询画布函数
      async function searchCanvas() {
        try {
          const creator = document.querySelector('select[class*="input-field"]').value;
          const dataSource = document.querySelectorAll('select[class*="input-field"]')[1].value;
          const startTime = document.getElementById('startTime').value;
          const endTime = document.getElementById('endTime').value;
          const canvasName = document.querySelector('input[type="text"]').value;
          
          // 调用查询画布接口
          await apiRequest(`${API_BASE_URL}/theme-management/search-canvas`, 'POST', {
            creator: creator,
            dataSource: dataSource,
            startTime: startTime,
            endTime: endTime,
            canvasName: canvasName
          });
          console.log('查询画布成功');
        } catch (error) {
          console.error('查询画布失败:', error);
        }
        
        // 保持原有逻辑
        // 静默处理，不显示提示
      }

      // 重置画布函数
      async function resetCanvas() {
        try {
          // 调用重置画布接口
          await apiRequest(`${API_BASE_URL}/theme-management/reset-canvas`, 'POST', {
            timestamp: new Date().toISOString()
          });
          console.log('重置画布成功');
        } catch (error) {
          console.error('重置画布失败:', error);
        }
        
        // 保持原有逻辑
        // 重置所有表单字段
        document.querySelectorAll('select[class*="input-field"]').forEach(select => {
          select.value = '';
        });
        document.getElementById('startTime').value = '';
        document.getElementById('endTime').value = '';
        document.querySelector('input[type="text"]').value = '';
        // 静默处理，不显示提示
      }

      // 版本对比模态框逻辑
      document.addEventListener('DOMContentLoaded', function () {
        // 获取版本历史按钮
        const versionHistoryBtns = document.querySelectorAll('.fa-history').forEach(btn => {
          btn.parentElement.addEventListener('click', async function () {
            try {
              // 调用获取版本历史接口
              await apiRequest(`${API_BASE_URL}/theme-management/version-history`, 'POST', {
                canvasId: this.closest('.canvas-item').getAttribute('data-id') || 'default'
              });
              console.log('获取版本历史成功');
            } catch (error) {
              console.error('获取版本历史失败:', error);
            }
            
            // 保持原有逻辑
            document.getElementById('versionCompareModal').classList.remove('hidden');
          });
        });

        // 关闭模态框
        document.getElementById('closeCompareModal').addEventListener('click', function () {
          document.getElementById('versionCompareModal').classList.add('hidden');
        });

        document.getElementById('closeCompareBtn').addEventListener('click', function () {
          document.getElementById('versionCompareModal').classList.add('hidden');
        });

        // 点击模态框外部关闭
        document.getElementById('versionCompareModal').addEventListener('click', function (e) {
          if (e.target === this) {
            this.classList.add('hidden');
          }
        });

        // 初始化开始时间和结束时间日期选择器
        if (document.getElementById('startTime')) {
          flatpickr('#startTime', {
            dateFormat: 'Y/m/d',
            locale: 'zh_CN',
          });
        }

        if (document.getElementById('endTime')) {
          flatpickr('#endTime', {
            dateFormat: 'Y/m/d',
            locale: 'zh_CN',
          });
        }
      });
    </script>
    <!-- 画布创建模态框 -->
    <div id="createCanvasModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col transform transition-all duration-300 opacity-0 scale-95 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        <div class="p-4 border-b flex justify-between items-center">
          <h3 class="font-medium">新建画布</h3>
          <button class="modal-close text-gray-500 hover:text-gray-700 focus:outline-none">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="p-4 overflow-y-auto flex-grow">
          <div class="space-y-4">
            <div class="property-item">
              <label class="label block mb-2">画布名称</label>
              <input type="text" id="canvasName" class="input-field w-full" placeholder="请输入画布名称" />
            </div>
            <div class="property-item">
              <label class="label block mb-2">画布类型</label>
              <select id="canvasType" class="input-field w-full">
                <option value="dashboard">数据大屏</option>
                <option value="report">报表</option>
                <option value="analysis">分析视图</option>
              </select>
            </div>
            <div class="property-item">
              <label class="label block mb-2">数据源类型</label>
              <select id="dataSourceType" class="input-field w-full">
                <option value="salesData">销售数据</option>
                <option value="orderData">订单数据</option>
                <option value="userData">用户数据</option>
                <option value="inventoryData">库存数据</option>
              </select>
            </div>
            <div class="property-item">
              <label class="label block mb-2">画布描述</label>
              <textarea id="canvasDescription" class="input-field w-full" rows="3" placeholder="请输入画布描述"></textarea>
            </div>
          </div>
        </div>
        <div class="p-4 border-t flex justify-end gap-2">
          <button class="btn btn-outline text-sm">取消</button>
          <button class="btn btn-outline text-sm">保存</button>
        </div>
      </div>
    </div>


  </body>
</html>
